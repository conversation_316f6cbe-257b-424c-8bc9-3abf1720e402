# Irrigation, Fertigation, and Backwashing

## About the Document

This document aims to provide a comprehensive overview of the irrigation process in field crops, including the principles of irrigation, fertigation, and backwashing.

It will describe the workflow of the irrigation process executed by the small ot medium agricultural property owner, detailing the steps involved in managing irrigation, fertigation, and backwashing operations.

## Irrigation

Irrigation is the process of applying controlled amounts of water to crops at needed intervals. It is essential for maintaining soil moisture, promoting plant growth, and ensuring optimal crop yields.
The Irriga+ system automates the irrigation process, allowing for precise control over water application based on the specific needs of each crop. For this, the system allows the user to define projects. Each project contains:

- Sectors: A area of the field that is being irrigated by a valve.
- Irrigation Plans: A plan that defines how a set of sectors will be irrigated, including the duration, the order and frequency of irrigation.
  A irrigation plan contains a set of steps. A step specifies for how long a sector will be irrigated.

## Fertigation

Fertigation is the process of applying fertilizers through the irrigation system, allowing for efficient nutrient delivery to crops. The Irriga+ system supports fertigation by enabling users to define fertigation setup in the irrigation plan steps.
The fertigation will happen in the same time as the irrigation. For the fertigation to happen, we must ensure that the pipes are pressurized and that the water pump controller is running, so it is a good practice to start the fertigation some time after the irrigation starts. It is also required that the fertigation ends some time before the irrigation ends, so the fertigation solution is washed away by the irrigation water and the next sector is not contaminated by the fertigation solution of the previous sector.

```mermaid
gantt
  title Irrigation and Fertigation Timeline
  dateFormat X
  axisFormat %s min

  section Irrigation
  Irrigation Process : 0, 30

  section Fertigation
  Fertigation Process : 5, 25
```

## Backwashing

Backwashing is the process of cleaning the irrigation system by reversing the flow of water to remove debris and sediment that may have accumulated in the pipes and filters. This is essential for maintaining the efficiency and longevity of the irrigation system. It will happen at a configured period for a configured duration.

## System Structure

### System Project

A project is a entity that holds the information about the usage of a water pumps by a localized irrigation controller.
Note that the project is associated with water pumps, not water pump controllers. Each water pump is associated with water pump controllers, so water pumps holds the information of what controller will be used.
A project will have at least one water pump: the irrigation water pump. A fertigation water pump can also be associated with the project, but it is not mandatory. When the project has a fertigation water pump, it will be used to inject the fertigation solution into the irrigation system and it is said that fertigation is enabled for the project.

The project will also define:

- LIC (Localized Irrigation Controller): The controller that will manage the irrigation process for the project.
- Pipe wash time: The time in seconds it takes to wash the pipes after the fertigation is done.
- Backwash duration: The time in seconds it takes to backwash the irrigation system.
- Backwash period: The time in seconds between backwashing operations.

The project also contains sectors and irrigation plans.

### Sectors

A sector is a specific area of the field that is being irrigated by a valve. It is associated with a specific valve controller output. One valve controller will usually control multiple sectors, but each sector is controlled by only one valve controller output.

### Irrigation Plans

An irrigation plan defines how a set of sectors will be irrigated. It will define:

- Its status: enabled or disabled.
- The start time of day of the irrigation plan.
- The days of the week when the irrigation plan will be executed.
- If the project has fertigation enabled, the plan will also define if fertigation is enabled for the plan.

The irrigation plan will contain a set of steps.

### Irrigation Plan Steps

An irrigation plan step specifies for how long a sector will be irrigated. It will define:

- The sector to be irrigated.
- The duration of the irrigation.
- The order in which the sector will be irrigated.
- If enabled in the irrigation plan, the step will also define the fertigation start delay in seconds and duration in seconds.

It is important to notice that the fertigation start delay plus the fertigation duration plus the project's pipe wash time must be less than or equal to the irrigation step duration, so the fertigation solution is washed away by the irrigation water and the next sector is not contaminated by the fertigation solution of the previous sector.

## Irrigation Process Workflow

- The user creates a project and defines the localized irrigation controller, water pumps, pipe wash time, backwash duration, and backwash period.
- The user creates sectors and associates them with valve controllers.
- The user creates an irrigation plan, defining its status, start time, days of the week, and whether fertigation is enabled.
- The user adds steps to the irrigation plan, specifying the sector, duration, order, and fertigation parameters if applicable.
- The LIC (Localized Irrigation Controller) monitors the irrigation plan and executes the steps at the defined times.
- During irrigation, if fertigation is enabled, the system will inject the fertigation solution into the irrigation system according to the defined parameters.
- During irrigation, the system will perform backwashing operations at the configured intervals to maintain system efficiency.
