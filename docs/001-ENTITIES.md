# Entities

## Brief Overview of the system structure

The user will be able to add `Properties`. Properties can have `Water Pumps` and `Projects`. A `Water Pump` represents a physical water pump with its associated water pump controller device. A project is an entity that holds the information about an irrigation setup that uses water pumps controlled by a localized irrigation controller. The project can have many Valve Controllers associated with it, each controlling a specific valve that is associated to a specific irrigation zone (called `Sector`).
A `Sector` is a specific area of the field that is being irrigated by a valve. It is associated with a specific valve controller output. One valve controller will usually control multiple sectors, but each sector is controlled by only one valve controller output.
To manage the irrigation process, the user can create `Irrigation Plans`. An irrigation plan defines how a set of sectors will be irrigated, including the duration, the order and frequency of irrigation.

To give access to other users, the user can create an `AccountUser`, which links `DirectusUser`s to `Account`s, defining roles and access within an account. This allows for multi-user collaboration on properties, projects, sectors, and irrigation plans.

## Entities

Below is a description of each entity, outlining its purpose and relationships within the irrigation management system.

1.  **`BaseEntity`**:

    - **Description**: This is an abstract base entity that provides common fields for all other entities in the system.
    - **Purpose**: It ensures that every entity has a unique identifier (`id`), and timestamps for creation (`date_created`) and last update (`date_updated`), along with optional user tracking (`user_created`, `user_updated`).
    - **Relationships**: It is the parent entity from which all other specific entities inherit.

2.  **`Device`**:

    - **Description**: Represents a physical hardware device used in the irrigation system.
    - **Purpose**: To catalog and identify individual hardware components like Localized Irrigation Controllers (LIC), Water Pump Controllers (WPC-PL10, WPC-PL50), Valve Controllers (VC), or Reservoir Monitors (RM). Each device has a specific `model` and a unique `identifier` (e.g., serial number).
    - **Relationships**:
      - A `Device` is associated with a `Property` through the `PropertyDevice` entity.
      - `Device` instances (specifically LICs) are linked to `Project` entities to define the irrigation control infrastructure.
      - `Device` instances (specifically Water Pump Controllers) are linked to `WaterPump` entities to define pump control infrastructure.
      - `Device` instances (specifically VCs) are linked to `Sector` entities to control specific irrigation zones.

3.  **`Account`**:

    - **Description**: Represents a user account in the system.
    - **Purpose**: To manage user access and ownership of resources. Each account is identified by its `owner` ID (a foreign key to the directus_users table).
    - **Relationships**:
      - An `Account` can own multiple `Property` entities.

4.  **`Property`**:

    - **Description**: Represents a physical agricultural property or farm.
    - **Purpose**: To group irrigation projects and devices under a specific geographical location and owner. It includes details like `name`, `location` (as a GeoJSON `Point`), `timezone`, and `address`.
    - **Relationships**:
      - Owned by an `Account`.
      - Can have multiple `Device` entities associated with it via `PropertyDevice`.
      - Can contain multiple `Project` entities.

5.  **`PropertyDevice`**:

    - **Description**: An associative entity linking a `Device` to a `Property`.
    - **Purpose**: To track which devices are installed or associated with which property, and the duration of this association (`start_date`, `end_date`).
    - **Relationships**:
      - Links one `Device` to one `Property`.

6.  **`WaterPump`**:

    - **Description**: Represents a physical water pump and its associated water pump controller device within a `Property`.
    - **Purpose**: To manage and catalog water pumps, including their identification, type, model, and control device. Each water pump has a unique `label` and `identifier` within a property and is controlled by a specific `water_pump_controller` device.
    - **Water Pump Types**: The system supports three standardized pump types:
      - **IRRIGATION**: Irrigation Water Pump - Used for standard irrigation operations
      - **FERTIGATION**: Fertigation Water Pump - Used for fertilizer and nutrient delivery through irrigation
      - **SERVICE**: Service Water Pump - Used for maintenance, cleaning, or other service operations
    - **Relationships**:
      - Belongs to one `Property`.
      - Is controlled by one `Device` (a Water Pump Controller).
      - Can be used as irrigation or fertigation pump in multiple `Project` entities.

7.  **`Project`**:

    - **Description**: Represents a specific irrigation setup or initiative within a `Property`.
    - **Purpose**: To organize and manage a complete irrigation system, including its main controller and water pumps. It defines the primary `localized_irrigation_controller`, `irrigation_water_pump`, and optionally a `fertigation_water_pump`.
    - **Relationships**:
      - Belongs to one `Property`.
      - Links to one `Device` entity for the localized irrigation controller.
      - Links to `WaterPump` entities for irrigation and optionally fertigation pumps.
      - Can contain multiple `Sector` entities.
      - Can have multiple `IrrigationPlan` entities.

8.  **`Sector`**:

    - **Description**: Represents a distinct zone or area within a `Project` that is irrigated by a single valve.
    - **Purpose**: To define a manageable irrigation unit. Each sector is controlled by a specific `valve_controller` and one of its `valve_controller_output`s. It can have a defined `area` and `polygon` geometry.
    - **Relationships**:
      - Belongs to one `Project`.
      - Is controlled by one `Device` (a Valve Controller).
      - Can be part of multiple `IrrigationPlanStep` entities within different `IrrigationPlan`s.

9.  **`IrrigationPlan`**:

    - **Description**: Defines a schedule and sequence for irrigating one or more `Sector`s within a `Project`.
    - **Purpose**: To automate the irrigation process by specifying `start_time`, `days_of_week`, and whether the plan is `is_enabled`. It also tracks the `total_irrigation_duration`.
    - **Relationships**:
      - Belongs to one `Project`.
      - Comprises multiple `IrrigationPlanStep` entities, which define the sequence and duration for each sector in the plan.

10. **`IrrigationPlanStep`**:

    - **Description**: Represents a single step in an `IrrigationPlan`, detailing the irrigation action for a specific `Sector`.
    - **Purpose**: To define the `order` of irrigation, the `duration_seconds` for a specific `sector`, and any `delay_seconds_after` before the next step.
    - **Relationships**:
      - Belongs to one `IrrigationPlan`.
      - Targets one `Sector`.

11. **`AccountUser`**:

    - **Description**: Represents the assignment of a user to an account, with a specific role and temporal validity.
    - **Purpose**: To manage which users have access to which accounts, their roles (admin, user, guest), and the period of access.
    - **Relationships**:
      - Links one `Account` to one `DirectusUser`.
      - Each combination of `account` and `user` is unique.
      - Used to enforce access control and multi-user collaboration on properties.

```typescript
import { Point, Polygon } from "geojson";

/**
 * Represents a user in the Directus system.
 * This interface is used to define the structure of user data.
 */
export interface DirectusUser {
  id: string;
  first_name: string | null;
  last_name: string | null;
  email: string;
  /**
   * Avatar identifier for the user's profile picture.
   */
  avatar: string | null;
  language: string | null;
  status: string;
}

/**
 * Base model for all entities in the irrigation system.
 * Provides a unique identifier and a timestamp for creation and updates.
 */
export interface BaseEntity {
  /** Unique identifier for the entity (UUID) */
  id: string;
  /** Timestamp when the entity was created */
  date_created: Date;
  /** User who created the entity (UUID) */
  user_created: string | null;
  /** Timestamp when the entity was last updated */
  date_updated: Date;
  /** User who last updated the entity (UUID) */
  user_updated: string | null;
  /** Additional metadata for the entity (JSON) */
  metadata: object | null;
  /** General notes or description for the entity (optional) */
  notes: string | null;
}

/**
 * A device operated by the irrigation system
 */
export interface Device extends BaseEntity {
  /** Model of the device: LIC (Localized Irrigation Controller), WPC-PL10 (Water Pump Controller - PL10 Variant), WPC-PL50 (Water Pump Controller - PL50 Variant), VC (Valve Controller), or RM (Reservoir Monitor) */
  model: "LIC" | "WPC-PL10" | "WPC-PL50" | "VC" | "RM";
  /** Serial number or identifier of the device */
  identifier: string;
}

/**
 * The Account entity represents a user account in the irrigation management system.
 */
export interface Account extends BaseEntity {
  /**
   * ID of the account owner (UUID).
   * The owner is a Directus user who has primary control over the account.
   */
  owner: string;
}

/**
 * Represents a physical property or farm in the irrigation management system.
 * Each property is associated with an account and can have multiple devices, water pumps and projects.
 */
export interface Property extends BaseEntity {
  /** ID of the account that owns the property */
  account: string;
  /** Name of the property */
  name: string;
  /**
   * GeoJSON geometry for the location (Point).
   * Uses types from the 'geojson' package.
   * PostGIS geometry(Point,4326) type.
   */
  point: Point | null;
  /**
   * Timezone identifier for the property (e.g., "America/Sao_Paulo").
   * Defaults to "America/Sao_Paulo".
   */
  timezone: string;
  /**
   * Postal code for the property's address (e.g., "12345-678").
   */
  address_postal_code: string | null;
  /**
   * Street name of the property's address.
   */
  address_street_name: string | null;
  /**
   * Street number of the property's address.
   */
  address_street_number: string | null;
  /**
   * Additional address information, such as apartment or suite (optional).
   */
  address_complement: string | null;
  /**
   * Neighborhood ("bairro") of the property's address.
   */
  address_neighborhood: string | null;
  /**
   * City of the property's address.
   */
  address_city: string | null;
  /**
   * State abbreviation (2 letters, e.g., "SP") of the property's address.
   */
  address_state: string | null;
  /**
   * Country of the property's address (default is "Brazil").
   */
  address_country: string | null;
  /**
   * Time in minutes for backwashing operations (optional).
   * Essential for cleaning the irrigation system by reversing water flow to remove debris.
   */
  backwash_duration_minutes: number | null;
  /**
   * Time in minutes between backwashing operations (optional).
   * Essential for maintaining system efficiency and longevity through automated maintenance scheduling.
   */
  backwash_period_minutes: number | null;
  /**
   * Time in seconds before backwashing operations starts (optional).
   * Provides a delay before the backwashing process begins.
   */
  backwash_delay_seconds: number | null;
  /**
   * Indicates if rain gauge monitoring is enabled for this property.
   * When enabled, the system will monitor precipitation levels to automatically suspend irrigation.
   */
  rain_gauge_enabled: boolean;
  /**
   * Resolution of the rain gauge in millimeters (optional).
   * Defines the smallest measurable precipitation increment.
   * Defaults to 0.2mm.
   */
  rain_gauge_resolution_mm: number | null;
  /**
   * Maximum precipitation volume in millimeters before irrigation is suspended (optional).
   * When this threshold is reached, automatic irrigation will be paused.
   * Defaults to 2mm.
   */
  precipitation_volume_limit_mm: number | null;
  /**
   * Duration in hours to suspend irrigation after precipitation limit is reached (optional).
   * Defines how long irrigation remains suspended after the precipitation threshold is exceeded.
   * Defaults to 24 hours.
   */
  precipitation_suspended_duration_hours: number | null;
}

/**
 * Association between a device and a property.
 */
export interface PropertyDevice extends BaseEntity {
  /** ID of the device (UUID) */
  device: string;
  /** ID of the property (UUID) */
  property: string;
  /** Date when the device was associated with the property */
  start_date: Date;
  /** Optional date when the device was removed from the property */
  end_date: Date | null;
}

/**
 * A physical water pump and its associated water pump controller device.
 */
export interface WaterPump extends BaseEntity {
  /** ID of the property associated with the water pump (UUID) */
  property: string;
  /**
   * ID of the water pump controller device (UUID).
   * Each water pump controller is associated with only one water pump at a time and is responsible for monitoring and controlling its operation.
   */
  water_pump_controller: string | null;
  /** Label for the water pump (unique per property) */
  label: string;
  /** Identifier for the water pump (unique per property) */
  identifier: string;
  /** Type of the water pump - must be one of: IRRIGATION, FERTIGATION, SERVICE */
  pump_type: "IRRIGATION" | "FERTIGATION" | "SERVICE" | null;
  /** Model of the water pump (optional) */
  pump_model: string | null;
  /** Flag indicating if the water pump has a frequency inverter */
  has_frequency_inverter: boolean;
  /** Flag indicating whether the water pump controller should monitor this pump's operation status */
  monitor_operation: boolean;
}

/**
 * Represents an irrigation project within a property that utilizes water pumps and localized irrigation controllers.
 * Each project can have multiple sectors and irrigation plans.
 * Each project uses a exclusive irrigation water pump and can optionally use a fertigation water pump.
 * Projects using the same irrigation water pump cannot have overlapping [start_date, end_date] ranges
 */
export interface Project extends BaseEntity {
  /** Name of the project */
  name: string;
  /** Optional description of the project */
  description: string | null;
  /**
   * ID of the property to which this project belongs (UUID).
   * Establishes the ownership and association of the project within a specific property.
   */
  property: string;
  /**
   * ID of the irrigation water pump (UUID) used by this project.
   * This water pump is actively utilized for irrigation operations within the project.
   */
  irrigation_water_pump: string;
  /**
   * Optional ID of the fertigation water pump (UUID) used by this project.
   * This water pump is utilized for fertigation operations within the project, if applicable.
   */
  fertigation_water_pump: string | null;
  /**
   * ID of the localized irrigation controller (UUID).
   * The localized irrigation controller is responsible for controlling and managing communication between all devices within the project.
   */
  localized_irrigation_controller: string;
  /**
   * Time in minutes to wash pipes after fertigation (optional).
   * Essential for ensuring fertigation solution is washed away before the next sector irrigation.
   */
  pipe_wash_time_seconds: number | null;
  /**
   * Time in minutes for backwashing operations (optional).
   * Essential for cleaning the irrigation system by reversing water flow to remove debris.
   */
  backwash_duration_seconds: number | null;
  /**
   * Time in minutes between backwashing operations (optional).
   * Essential for maintaining system efficiency and longevity through automated maintenance scheduling.
   */
  backwash_period_seconds: number | null;
  /**
   * Start date of the project (optional, date).
   * Projects using the same irrigation_water_pump cannot have overlapping [start_date, end_date] ranges.
   */
  start_date: string | null;
  /**
   * End date of the project (optional, date).
   */
  end_date: string | null;
}

/**
 * Represents a specific area of the field that is being irrigated by a valve.
 * Each sector is controlled by a specific valve controller output.
 * The combination of `valve_controller` and `valve_controller_output` must be unique across all sectors.
 */
export interface Sector extends BaseEntity {
  /** Name of the sector */
  name: string;
  /** Optional description of the sector */
  description: string | null;
  /**
   * ID of the project to which this sector belongs (UUID).
   * Establishes the relationship that each sector is part of a specific project.
   */
  project: string;
  /**
   * ID of the valve controller associated with this sector (UUID).
   * The valve controller monitors and controls the physical valve responsible for irrigating this sector.
   */
  valve_controller: string | null;
  /**
   * Output number of the valve controller controlling this sector.
   * The combination of `valve_controller` and `valve_controller_output` must be unique across all sectors,
   * ensuring that each output of a valve controller is assigned to only one sector at a time.
   */
  valve_controller_output: 1 | 2 | 3 | 4 | null;
  /** Area of the sector in acres */
  area: number | null;
  /**
   * The polygon geometry of the sector (optional).
   * Uses PostGIS geometry(Polygon,4326) type.
   */
  polygon: Polygon | null;
}

/**
 * Represents an irrigation plan for a specific project.
 * An irrigation plan defines how a set of sectors will be irrigated, including the duration, the order and frequency of irrigation.
 * Each irrigation plan can have multiple steps, each targeting a specific sector.
 */
export interface IrrigationPlan extends BaseEntity {
  /** Name of the irrigation plan */
  name: string;
  /** Optional description of the irrigation plan */
  description: string | null;
  /**
   * ID of the project to which this irrigation plan belongs (UUID).
   * Establishes the relationship that each irrigation plan is associated with a specific project.
   */
  project: string;
  /**
   * Time string (HH:MM:SS) indicating when the plan's schedule starts.
   */
  start_time: string;
  /**
   * Days of the week when the plan should execute (e.g., ["MON", "WED", "FRI"]).
   * JSONB array of strings.
   * Possible values: "MON", "TUE", "WED", "THU", "FRI", "SAT", "SUN".
   */
  days_of_week: ("MON" | "TUE" | "WED" | "THU" | "FRI" | "SAT" | "SUN")[];
  /**
   * Indicates if the plan is currently active and should be run by the system.
   * Unique per plot (plot + name).
   */
  is_enabled: boolean;
  /**
   * Indicates if fertigation is enabled for this irrigation plan.
   * When enabled, the plan will include fertigation operations during irrigation steps.
   */
  fertigation_enabled: boolean;
  /**
   * Indicates if backwash is enabled for this irrigation plan.
   * When enabled, the plan will include backwash operations during irrigation cycles.
   */
  backwash_enabled: boolean;
  /**
   * Total irrigation duration (in seconds) for this plan.
   * This is the sum of all duration_seconds from associated SectorIrrigationStep entries.
   * Automatically updated by a database trigger.
   */
  total_irrigation_duration: number;
  /**
   * Start date of the plan (optional, date).
   */
  start_date: string | null;
  /**
   * End date of the plan (optional, date).
   */
  end_date: string | null;
}

/**
 * Represents a single step within an irrigation plan, specifying how a particular sector is irrigated.
 * Each step is owned by a specific irrigation plan and targets a specific sector.
 * The combination of `irrigation_plan` and `order` must be unique, ensuring a clear sequence of steps.
 */
export interface IrrigationPlanStep extends BaseEntity {
  /**
   * ID of the irrigation plan to which this step belongs (UUID).
   * Establishes a strong ownership relationship, ensuring that each step is uniquely associated with a single irrigation plan.
   */
  irrigation_plan: string;
  /**
   * ID of the sector targeted by this irrigation step (UUID).
   * Defines which sector will be irrigated during this step.
   */
  sector: string;
  /**
   * Optional description of the irrigation step.
   * Provides additional context or notes about this step.
   */
  description: string | null;
  /**
   * Explicit order of this step in the irrigation sequence (1 = first, 2 = second, ...).
   * Must be unique within the same irrigation plan to maintain a clear execution sequence.
   */
  order: number;
  /**
   * Duration (in seconds) to irrigate the specified sector during this step.
   */
  duration_seconds: number;
  /**
   * Delay (in seconds) before starting fertigation after irrigation begins for this step.
   * Only applicable when fertigation is enabled in the irrigation plan.
   */
  fertigation_start_delay_seconds: number | null;
  /**
   * Duration (in seconds) for fertigation operation during this step.
   * Only applicable when fertigation is enabled in the irrigation plan.
   * Must end before irrigation stops to allow for pipe washing.
   */
  fertigation_duration_seconds: number | null;
}

/**
 * Represents the association between a user and an account, including the user's role and access period.
 *
 */
export interface AccountUser extends BaseEntity {
  /** ID of the user (UUID) */
  user: string;
  /** ID of the account (UUID) */
  account: string;
  /** Role of the user in the account: "admin", "user", or "guest" */
  role: "admin" | "user" | "guest";
  /** Start date of the user's access to the account */
  start_date: Date;
  /** Optional end date of the user's access to the account */
  end_date: Date | null;
}
```
