# React Guidelines

Overview
Sources:
- /app/.github/instructions/coding.instructions.md
- /app/.github/instructions/react.instructions.md
- /app/.github/instructions/design.instructions.md

Principles
- Functional, hook-based components.
- Pure render; side effects in hooks.
- Strong typing for props and state.

Conventions
- Interfaces for props; named exports; small, focused components.
- useCallback/useMemo to control re-renders.
- Context for global/shared state; avoid prop drilling.
- Keys on lists; avoid inline lambdas in JSX when feasible.
- Align with design system tokens and patterns.

Patterns
- UI props mapped to DS variants/sizes.
- Use domain-organized components under src/components and src/pages.

Examples
Props typing aligned to DS
interface ButtonProps {
  variant: "primary" | "secondary" | "ghost";
  size: "sm" | "md" | "lg";
}

Do/Don’t
Do
- Follow design.json and tokens.
- Keep accessibility and performance in mind.

Don’t
- Break DS consistency.
- Leave side effects in render.

Checklists
- Props typed; component pure.
- DS adherence verified; states covered.

References
- ../foundations/design-system.md
- ../frontend/tailwindcss.md
- ../frontend/state-management-jotai.md

Conflicts & Resolutions
- None.
