# UX/UI Guidelines

Overview
Source: /app/.github/instructions/design.instructions.md and /app/docs/design.json

Principles
- DS-first: strict adherence to design.json.
- Accessibility: contrast, focus, tap targets (>=48px).
- Mobile-first; consistent interactions.

Conventions
- Reference tokens for colors, spacing, type, radius.
- Implement hover, active, disabled, focus states as defined.
- Use semantic HTML and ARIA where needed.

Patterns
- Screen layout: header, content, footer; container padding and grid per design.json.
- Lists, forms, and navigation styles per DS patterns.
- Icons: sizes and colors per DS.

Examples
- Buttons: primary, secondary, ghost variants with min-height 48px.
- Inputs: focus ring and error border tokens from DS.

Do/Don’t
Do
- Validate responsiveness across breakpoints.
- Ensure interactive states exist and match DS.

Don’t
- Invent ad-hoc styles; remain within DS.

Checklists
- Visual parity with DS.
- Accessibility checks.
- Breakpoint responsiveness.

References
- ../foundations/design-system.md
- ../references/design-tokens.md

Conflicts & Resolutions
- None.
