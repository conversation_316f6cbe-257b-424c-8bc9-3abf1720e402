# Irriga Mais Guidelines Index

Overview
This index links to consolidated guidelines extracted from:

- /app/.augment/rules/code.md
- /app/.github/instructions/coding.instructions.md
- /app/.github/instructions/design.instructions.md
- /app/.github/instructions/react.instructions.md
- /app/.github/instructions/tech-stack.instructions.md
- /app/docs/design.json
- /tasks/INSTRUCTIONS.md

Sections

- Foundations
  - Tech Stack: ../foundations/tech-stack.md
  - Architecture: ../foundations/architecture.md
  - Design System: ../foundations/design-system.md
  - Coding Conventions: ../foundations/coding-conventions.md
- Frontend
  - API & Services: ../backend/api-and-services.md
  - React: ../frontend/react.md
  - State (Jotai): ../frontend/state-management-jotai.md
  - TailwindCSS: ../frontend/tailwindcss.md
  - UX/UI: ../frontend/ux-ui-guidelines.md
  - PWA & Performance: ../frontend/pwa-and-performance.md
- Backend
  - Migrations & Seed: ../backend/migrations-and-seed.md
  - Security: ../backend/security.md
- Workflows
  - Tasks File Usage: ../workflows/tasks-file-usage.md
  - Git & Commit Policy: ../workflows/git-and-commit-policy.md
- Testing
  - Strategy: ../testing/testing-strategy.md
- References
  - Design Tokens (from design.json): ../references/design-tokens.md
  - Cross-References: ../references/cross-references.md

Conflicts & Resolutions

- None identified beyond normal consolidation. If future instructions diverge, record them in each file’s “Conflicts & Resolutions” section.
