# Migrations and Seed

Overview
Source: /tasks/INSTRUCTIONS.md

Principles
- Deterministic, ordered migrations.
- Seed data synced with schema changes.

Conventions
Migrations
- Directory: /directus/migrations
- Filename pattern: YYYYMMDD[A]-migration-name.js
- Letter suffix A, B, C… indicates order within the same date.
- No two migrations share the same date-letter key.

Seed
- File: /directus/src/seed/index.ts
- Any schema change must be reflected in the seed:
  - Dropped table -> remove seed data.
  - Created table -> add seed data.
  - Altered table -> update seed data.

Patterns
- Before adding a migration, inspect existing date-letter keys.
- Update seed in the same PR as migration changes.

Examples
- Create 20250731D-new-feature.js when C is the latest for 20250731.

Do/Don’t
Do
- Verify ordering keys and avoid collisions.
- Keep seed aligned with schema.

Don’t
- Merge migrations without seed updates.

Checklists
- Migration key unique and sequential.
- Seed updated for all structural changes.
- Docs updated (below).

References
Documentation updates required on schema changes:
- /docs/001-ENTITIES.md
- /docs/002-ENTITY_DIAGRAMS.md
- /docs/DDL.md

Conflicts & Resolutions
- None.
