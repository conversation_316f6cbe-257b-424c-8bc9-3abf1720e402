# Security

Overview
Source: /app/.github/instructions/tech-stack.instructions.md

Principles

- Keep secrets server-side; client vars prefixed BUN*PUBLIC*\*.
- Isolate production environment.

Conventions
Environment

- Use BUN*PUBLIC*\* only for safe client exposure.
- No secrets in client bundles.
- Separate dev/prod configs; minimize logs in prod.

Build Security

- Source maps in development only or external in prod.
- Minify and tree-shake for smaller attack surface.

Patterns

- Centralize auth in API service.
- Typed boundaries to avoid injection via untyped data.

Examples

- Configure client with non-sensitive env only.

Do/Don’t
Do

- Audit bundles for accidental secrets.
- Use interceptors to handle auth errors.

Don’t

- Hardcode tokens or endpoints with secrets in UI.

Checklists

- Env variables vetted.
- Source map policy enforced.

References

- ../frontend/api-and-services.md
- ../frontend/pwa-and-performance.md

Conflicts & Resolutions

- None.
