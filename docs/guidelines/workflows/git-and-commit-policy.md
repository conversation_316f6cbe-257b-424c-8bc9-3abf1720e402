# Git and Commit Policy

Overview
Source: /tasks/INSTRUCTIONS.md

Principles
- Predictable branching and commits that reference tasks.

Conventions
Branching
- Base branch: as specified in TASKS header; default develop when missing.
- Feature branches: <task-list-name>-<task-number>-<kebab-description>.

Commits
- First line follows Conventional Commits.
  - Examples: feat:, fix:, chore:, docs:, refactor:, test:
- Include task reference in the first line.

Merge
- Merge feature branch back into the base branch after completion.

Patterns
- One task per branch; keep changes scoped.

Examples
chore: 250729-1-property-address-required-fields

Do/Don’t
Do
- Ensure commits describe changes succinctly.
- Keep history clean and tied to tasks.

Don’t
- Squash unrelated work into one branch.

Checklists
- Branch name validated.
- Commit message validated.

References
- ../workflows/tasks-file-usage.md

Conflicts & Resolutions
- None.
