# Tasks File Usage

Overview
Source: /tasks/INSTRUCTIONS.md

Principles

- Follow the TASKS file without user interaction.
- Use standardized statuses and workflow.

Conventions
TASKS Format

- Header “Task list info” with:
  - name
  - base_branch
- Optional “Task list context”
- Tasks section with:
  - Title “Task N. Brief description”
  - Description block
  - Target directories list
  - Status: Pending|In Progress|Done|Canceled|Error
  - Optional Subtasks:
    - Title “Subtask N.M. Brief description”
    - Description block
    - Target directories list
    - Status: Pending|In Progress|Done|Canceled|Error

Execution Workflow

- Find next Pending task or subtask.
- Mark as In Progress.
- Create branch from base branch (or develop if undefined).
  - Naming: <task-list-name>-<task-number>-<kebab-description>
  - Example: 250729-1-property-address-required-fields
- Execute the task or subtask.
- Verification (frontend): bunx tsc --noEmit in /app, fix types.
- Mark as Done.
  - If subtask, ensure parent task status is updated if all subtasks are Done.
- Commit using Conventional Commits first line:
  - Example: chore: 250729-1-property-address-required-fields
  - Follow with brief description.
- Merge to base branch.

Patterns

- Keep all task metadata within TASKS file.
- One branch per task (subtasks share the parent task branch).

Examples
Branch name: 250730-2-fix-water-pump-flow-rate

Do/Don’t
Do

- Respect status transitions.
- Verify TypeScript types before completion.
- Handle subtasks as part of the parent task workflow.

Don’t

- Ask for user input during execution.
- Skip the verification step.

Checklists

- Status updated (Pending -> In Progress -> Done).
- Branch created with correct base and name.
- Types checked with bunx tsc --noEmit under /app.
- Parent task status updated if all subtasks are Done.
- Commit message conforms to Conventional Commits.

References

- ../workflows/git-and-commit-policy.md

Conflicts & Resolutions

- None.
