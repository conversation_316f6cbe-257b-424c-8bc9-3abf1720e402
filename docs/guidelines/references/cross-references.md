# Cross-References

Overview
Consolidated pointers across guidelines.

Frontdoor Files

- Coding Conventions: ../foundations/coding-conventions.md
- Design System: ../foundations/design-system.md
- Tech Stack: ../foundations/tech-stack.md
- Architecture: ../foundations/architecture.md
- React: ../frontend/react.md
- Jotai: ../frontend/state-management-jotai.md
- Tailwind: ../frontend/tailwindcss.md
- UX/UI: ../frontend/ux-ui-guidelines.md
- PWA & Performance: ../frontend/pwa-and-performance.md
- API & Services: ../frontend/api-and-services.md
- Migrations & Seed: ../backend/migrations-and-seed.md
- Security: ../backend/security.md
- Tasks Workflow: ../workflows/tasks-file-usage.md
- Git & Commits: ../workflows/git-and-commit-policy.md
- Design Tokens: ../references/design-tokens.md

Related Links

- React ↔ <PERSON><PERSON>: ../frontend/react.md ↔ ../frontend/state-management-jotai.md
- Design System ↔ Tailwind: ../foundations/design-system.md ↔ ../frontend/tailwindcss.md
- API Service ↔ Testing: ../frontend/api-and-services.md ↔ ../testing/testing-strategy.md
- Tasks Workflow ↔ Git Policy: ../workflows/tasks-file-usage.md ↔ ../workflows/git-and-commit-policy.md

Conflicts & Resolutions

- None.
