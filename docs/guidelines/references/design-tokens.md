# Design Tokens (from design.json)

Overview
Source: /app/docs/design.json

Principles
- Tokens define canonical values for colors, typography, spacing, radii, shadows, layout, breakpoints, animations, patterns, and states.
- All UI must resolve to these tokens.

Conventions
- Reference tokens by semantic names in CSS/JS.
- Map DS variants to Tailwind classes or CSS variables.

Tokens
Color Palette
- primary: 50–900
- neutral: 50–900
- semantic: success, warning, error, info
- background: primary, secondary, accent

Typography
- fontFamily: Inter, system stack
- fontSize: xs, sm, base, lg, xl, 2xl, 3xl, 4xl
- fontWeight: light, normal, medium, semibold, bold
- lineHeight: tight, normal, relaxed

Spacing
- xs, sm, md, lg, xl, 2xl, 3xl

Border Radius
- sm, md, lg, xl, 2xl, full

Shadows
- sm, md, lg, xl

Components
- button
  - primary: backgroundColor primary.500, color white, radius lg, padding md lg, fontSize base, fontWeight medium, minHeight 48px, states: hover primary.600, active primary.700, disabled neutral.300/neutral.500
  - secondary: transparent bg, color primary.500, border 1px solid primary.500, radius lg, padding md lg, fontSize base, fontWeight medium, minHeight 48px
  - ghost: transparent bg, color primary.500, radius lg, padding md lg, fontSize base, fontWeight medium
- card
  - default: bg background.primary, radius xl, padding lg, shadow md, border 1px neutral.100
  - elevated: bg background.primary, radius xl, padding lg, shadow lg
- input
  - default: bg background.primary, border 1px neutral.200, radius lg, padding md, fontSize base, minHeight 48px, states: focus border primary.500 + boxShadow 0 0 0 3px rgba(59, 167, 59, 0.1), error border semantic.error
- navigation
  - header: bg background.primary, borderBottom 1px neutral.100, padding md lg, minHeight 56px
  - tabBar: bg background.primary, borderTop 1px neutral.100, padding sm lg, minHeight 60px
- list
  - item: padding md lg, borderBottom 1px neutral.100, bg background.primary, hover bg background.secondary
- icon
  - sizes: sm 16px, md 20px, lg 24px, xl 32px
  - colors: primary primary.500, secondary neutral.500, accent primary.600
- badge
  - default: bg primary.100, color primary.700, radius full, padding xs sm, fontSize xs, fontWeight medium
  - success: bg semantic.success, color white, radius full, padding xs sm, fontSize xs, fontWeight medium
- progressIndicator
  - linear: bg neutral.200, height 4px, radius full, fill bg primary.500 radius full
  - circular: size 24px, strokeWidth 2px, color primary.500

Layout
- container: maxWidth 100%, padding 0 lg, margin 0 auto
- grid: columns 12, gap md
- section: padding xl 0

Breakpoints
- mobile 375px, tablet 768px, desktop 1024px

Animations
- duration: fast 150ms, normal 300ms, slow 500ms
- easing: easeInOut, easeOut, easeIn

Patterns
- screenLayout: header, content, footer; contentPadding md lg
- formLayout: fieldSpacing lg, labelSpacing sm, buttonSpacing xl
- cardGrid: gap md; columnsPerRow mobile 1, tablet 2, desktop 3
- listLayout: itemSpacing 0, groupSpacing lg, iconSpacing md

States
- loading: opacity 0.6, cursor wait
- disabled: opacity 0.4, cursor not-allowed
- interactive: cursor pointer, transition all 150ms ease

Examples
- See ../foundations/design-system.md for CSS and component examples.

Do/Don’t
Do
- Use these tokens as the single source of truth.

Don’t
- Hardcode arbitrary values that diverge from tokens.

Checklists
- Token usage verified in components.

References
- ../frontend/tailwindcss.md
- ../frontend/ux-ui-guidelines.md

Conflicts & Resolutions
- None.
