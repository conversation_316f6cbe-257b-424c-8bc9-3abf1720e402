# Tech Stack

Overview
Source: /app/.github/instructions/tech-stack.instructions.md

Irriga Mais is a full-stack web application for agricultural irrigation management using Bun, React 19, TypeScript (strict), TailwindCSS v4, Wouter, Lucide, and <PERSON>tai. It is PWA-ready and optimized for performance, scalability, and modern development workflows.

Principles
- Minimal dependencies; prefer lightweight, type-safe, modern packages.
- Strict TypeScript, ESNext targets, ESM modules.
- Design-system-first UI alignment with design.json.
- Mobile-first, responsive, accessible UX.
- Production security and performance optimizations by default.

Conventions
- Runtime: Bun v1.2.16+ for dev, build, run, test.
- Frontend: React 19, functional components, hooks.
- TS config: strict mode, JSX React transform, @/* alias to ./src/*.
- Styling: TailwindCSS v4 with custom tokens (see design-system.md).
- Routing: Wouter v3.7.1.
- Icons: Lucide React v0.525.0.
- State: Jotai with atomic, derived, and async atoms.

Patterns
- Build via build.ts with Tailwind plugin, sourcemaps, minify, splitting.
- API through Directus SDK with centralized service layer.

Examples
Scripts
{
  "dev": "bun --hot src/index.tsx",
  "start": "NODE_ENV=production bun src/index.tsx",
  "build": "bun run build.ts"
}

Build options
bun run build.ts --outdir dist --minify --source-map external --target browser --format esm --splitting --external react,react-dom

Do/Don’t
Do
- Use Bun’s PM, bundler, HMR.
- Prefer Wouter, Jotai, Lucide for lightweight perf.

Don’t
- Add heavy deps without justification.
- Break ESM/strict TS guarantees.

Checklists
- Bun >= 1.2.16 installed.
- TS strict enabled.
- TailwindCSS integrated with tokens.

References
- ../references/cross-references.md
- ../foundations/design-system.md
- /app/.github/instructions/tech-stack.instructions.md

Conflicts & Resolutions
- None.
