# Coding Conventions

Overview
Sources:
- /app/.github/instructions/coding.instructions.md
- /app/.github/instructions/react.instructions.md
- /app/.augment/rules/code.md

Principles
- Clarity, small focused units, consistency, and type safety.
- Prefer functional React patterns and modern ES features.
- Domain-organized state and APIs.

Conventions
General
- ES6+ features; named exports; small files/functions.
- Consistent naming (kebab-case/camelCase as appropriate).

TypeScript
- Type all params and returns.
- Interfaces for shapes/props; unions via type aliases.
- Avoid any; prefer unknown or exact types.
- Strict mode enabled.
- Prefer union types over enums for limited sets.

React
- Use hooks; keep render pure.
- useCallback/useMemo for perf when needed.
- Context for global state; avoid prop drilling.
- Keys for lists; avoid inline functions in JSX when possible.
- TS interfaces for props.

Jotai
- Small, focused atoms; use derived atoms for computed state.
- Async logic in write-only atoms.
- Organize atoms by domain in src/store.
- Hooks: useAtom, useAtomValue, useSetAtom as intended.
- Atom names end with Atom suffix.

TailwindCSS
- Use utilities directly; prefer composition.
- Use @apply for repeated patterns only.
- Keep className readable; use responsive/state variants.
- Avoid arbitrary values unless necessary.
- Use bg-black/50 pattern, not bg-opacity-[number].

Bun
- Use Bun APIs and bun install.
- Bun test runner for tests.
- Scripts in package.json or Bun scripts.

Code Style
- 2-space indent; single quotes; newline at EOF; semicolons.
- Prefer destructuring; sort imports (external, internal, styles/assets).
- Remove unused imports/vars.

Language
- English for code/comments/docs.
- UI strings in Brazilian Portuguese.

Patterns
- Domain-oriented file placement (store, api, components).
- Singleton services; typed boundaries.

Examples
- See ../frontend/state-management-jotai.md and ../backend/api-and-services.md.

Do/Don’t
Do
- Enforce strict TS; keep components pure.
- Use DS tokens and Tailwind utilities.

Don’t
- Use any or large God components.
- Bypass centralized services/state.

Checklists
- Type coverage for new code.
- Import order and cleanup verified.
- Tailwind classes follow guidance.

References
- ../foundations/tech-stack.md
- ../frontend/react.md
- ../frontend/tailwindcss.md

Conflicts & Resolutions
- None.
