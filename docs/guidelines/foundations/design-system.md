# Design System

Overview
Sources:
- /app/.github/instructions/design.instructions.md
- /app/docs/design.json

All UI must strictly adhere to design.json and tokens in src/index.css. Use mobile-first, accessible, performant, semantic patterns.

Principles
- Single source of truth: design.json, tokens in CSS.
- Consistency across components, screens, and states.
- Accessibility: contrast, 48px min interactive sizes.
- Mobile-first responsive design.

Conventions
- Always reference design.json before implementing components.
- Use tokens (colors, spacing, typography, radius, shadows).
- Implement hover/active/disabled states.
- Semantic HTML for better a11y and SEO.

Patterns
- Screen layout: header, content, footer with spacing.
- Form layout: field/label/button spacing presets.
- Card grids and list layouts per design.json patterns.

Examples
CSS using tokens
.button-primary {
  background-color: var(--primary-500);
  color: white;
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-md) var(--spacing-lg);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  min-height: 48px;
}

React prop typing aligned to DS
interface ButtonProps {
  variant: "primary" | "secondary" | "ghost";
  size: "sm" | "md" | "lg";
  children: React.ReactNode;
}

Do/Don’t
Do
- Match palette, typography, spacing, radius exactly.
- Implement interactive states.
- Ensure responsiveness and a11y.

Don’t
- Introduce custom styling unrelated to tokens.
- Ignore design.json specifications.

Checklists
- Colors/typography/spacing/radius match DS.
- States implemented.
- Responsive and accessible.
- Code follows conventions.

References
- ../references/design-tokens.md
- ../frontend/tailwindcss.md
- /app/.github/instructions/design.instructions.md
- /app/docs/design.json

Conflicts & Resolutions
- None.
