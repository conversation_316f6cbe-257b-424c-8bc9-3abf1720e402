# Irrigation System Structure Entities

## Description

This document documents the structure of the irrigation system entities used in the irrigation management system.
These entities are used to represent how the data is structured, thus, it works like a database modeling document.

## Brief Overview of the System

The irrigation management system is designed to automate and optimize the irrigation process for small to medium agricultural fields.
It is mainly designed to be used by small to medium agricultural property owners.

This project consists of developing a system for automated management of localized irrigation, designed for small rural properties. The system is composed of a main board connected to the internet and several remote boards that control water pumps and irrigation valves. Communication between the boards occurs via LoRa technology, enabling long-range control with low power consumption.

The main objective is to offer an accessible, intuitive and efficient solution to automate irrigation processes, optimizing the use of water resources and reducing the need for constant human intervention.

## Target Audience

This document is intended for developers and system architects who are involved in the design and implementation of the irrigation management system. It provides a clear understanding of the entities involved, their relationships, and how they interact with each other.

## The Devices

The system works by making use of remote devices that are connected to the irrigation system.
The devices are:

### 1. LIC (Localized Irrigation Controller):

This device works as the main controller for the irrigation system. Among its responsibilities are:

- Central point of communication with the server: It communicates with the server using TCP/MQTT and communicates with other local devices using LoRA.
- Management of local devices: It will store irrigation schedules, and coordinate the local devices according to the irrigation plan being executed.
- Real time monitoring: It will monitor the local devices and report their status to the server.

### 2. WPC-PL10 (Water Pump Controller - PL10 Variant)

This device is responsible for controlling the water pump with PL10 specifications. It is wired to the water pump and allows for the remote control of the pump's operation.
It communicates with the localized irrigation controller to receive commands and report its status using LoRA.

### 3. WPC-PL50 (Water Pump Controller - PL50 Variant)

This device is responsible for controlling the water pump with PL50 specifications. It is wired to the water pump and allows for the remote control of the pump's operation.
It communicates with the localized irrigation controller to receive commands and report its status using LoRA.

### 4. VC (Valve Controller)

This device is responsible for controlling the valves that regulate water flow to different sections of the irrigation system.
Each of its outputs is connected to a valve, and it communicates with the localized irrigation controller to receive commands and report its status using LoRA.

### 5. RM (Reservoir Monitor)

This device is responsible for monitoring water reservoir systems. It monitors water levels.
It communicates with the localized irrigation controller to report its status using LoRA.

---

## Entities

### Brief Overview of the system structure

The user will be able to add `Properties`. Properties can have `Projects`. A project is a entity that holds the information about the usage of a water pump controller that is controlled by a localized irrigation controller. The project can have many Valve Controllers associated with it, each controlling a specific valve that is associated to a specific irrigation zone (called `Sector`).

A `Sector` is a specific area of the field that is being irrigated by a valve. It is associated with a specific valve controller output. One valve controller will usually control multiple sectors, but each sector is controlled by only one valve controller output.

To manage the irrigation process, the user can create `Irrigation Plans`. An irrigation plan defines how a set of sectors will be irrigated, including the duration, the order and frequency of irrigation.

It is possible to a user to give its account access to other users, so they can see and/or perform actions on the properties, projects, sectors and irrigation plans of the account. It is done by creating an ``AccountUser`, that Links `DirectusUser`s to `Account`s, defining roles and access within an account
