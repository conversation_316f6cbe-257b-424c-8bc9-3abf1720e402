# Instructions for executing a task

The TASKS file is used to specify and track the progress of tasks. It is a markdown file with a specific format:

```md
# Task list info:

- name: <task-list-name>
- base_branch: <base-branch-name>

---

# Task list context: <optional>

Context to help understand the tasks. This section is optional.
This data is common to all tasks in the task list.

---

# Tasks

## Task 1. Brief description of the task

**Description**
Detailed description of the task

**Target directories**

- <directory-1> (<directory-description>)
- <directory-2> (<directory-description>)

**Status:** Pending|In Progress|Done|Canceled|Error

### Subtask 1.1. Brief description of the optional subtask

**Description**
Detailed description of the subtask

**Target directories**

- <directory-1> (<directory-description>)
- <directory-2> (<directory-description>)

**Status:** Pending|In Progress|Done|Canceled|Error
```

---

When working with the TASKS file, you will follow the instructions below:

- [tasks file] Find the next task with status "Pending" or "In Progress". This is the algorithm for finding the next task:
  ```
      for task in tasks:
          if task.status == "In Progress" or task.status == "Pending":
              if task.subtasks:
                  for subtask in task.subtasks:
                      if subtask.status == "In Progress" or subtask.status == "Pending":
                          return subtask
                  task.status = "Done"  # All subtasks are done, canceled or error
                  continue
              else:
                  return task
          else:
              continue
  ```
- [git] Create a branch for the task using the base branch defined in the task list info section as the base branch. The branch name must start with the task list name, followed by a hiphen, followed by the task number and followed by a hyphen and a descriptive name. Branch name example: `250729-1-property-address-required-fields`. Git command example: `git checkout -b <branch-name> <base-branch-name>`. Note: subtasks share the parent task branch.
- [tasks file] Mark its status as "In Progress" before starting to work on it.
- [execution] Execute the task (or subtask)
- [verification] If working in the app (frontend), execute `bunx tsc --noEmit` inside the `app` directory to verify there are no types errors. Fix any errors before proceeding.
- [tasks file] Mark its status as "Done" when it is finished. If it is a subtask, and all subtasks are done, mark the parent task as "Done" too.
- Commit the changes with a message with the first line following the Conventional Commits format. For example: `chore: 250729-1-property-address-required-fields`. The rest of the message should be a brief description of what was done. Git command example: `git add -A && git commit -m "chore: 250729-1-property-address-required-fields" -m "Your description here"`
- [git] Merge the branch into the base branch. Git command example: `git checkout <base-branch-name> && git merge <branch-name>`

> NEVER PUSH A BRANCH TO THE REMOTE. Pushing a branch to the remote is not allowed.

> Make sure all changed files are saved before committing. Save all files before proceeding.

> Base branch is defined in the task list info section. If it is not defined, use `develop` as the base branch.

> Task list name can be found in the task list info section.

> Task status can be: Pending, In Progress, Done, Canceled and Error.

## Guidelines

When executing the task list, you will not ask anything to the user. You will just execute the tasks as defined in the TASKS file until all tasks are done. The user is not expected to interact with you during the execution.

Guidelines are defined in the following files:

- /app/.github/instructions/react.instructions.md
- /app/.github/instructions/coding.instructions.md
- /app/.github/instructions/design.instructions.md
- /app/.github/instructions/tech-stack.instructions.md
- /app/docs/design.json

### Migration files

Migration files are used to migrate the database from one state to another. They are located in the `directus/migrations` directory. They are executed in order, based on the filename. The filename must follow the pattern `YYYYMMDDA-migration-name.js`, where `YYYYMMDD` is the date of creation and `A` is a letter indicating the order of execution. The first migration file must have the letter `A`, the second `B`, and so on.
Two migrations can not have the same combination of date and letter. You must check it and ensure the keys `YYYYMMDDA` do not conflict with each other. The letter should be the next in the alphabet, based on the existing migrations.

### Seed script

The seed script is used to populate the database with initial data for development purposes. It is located in the `directus/src/seed/index.ts` file.
Any changes to the database structure must be accompanied by a change to the seed script to ensure the data is consistent.
If a table is dropped, the corresponding data must be removed from the seed script.
If a table is created, the seed script must be updated to include the new table.
If a table is altered, the seed script must be updated to reflect the changes.

### Documentation updates

Any changes in the database structure must be documented. The documents that MUST be update are:

- docs/001-ENTITIES.md
- docs/002-ENTITY_DIAGRAMS.md
- docs/DDL.md

The documents that may be relevant for updating are:

- docs/000-DESCRIPTION.md
- docs/003-BUSINESS_RULES_AND_CONSTRAINTS.md
