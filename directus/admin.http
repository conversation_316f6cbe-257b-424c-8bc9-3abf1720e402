@url = http://localhost:8055
@username = <EMAIL>
@password = byagro123
@userRole = ac5ba5cb-1d74-4df4-99d3-6758fb49255c

# @name Login
POST {{url}}/auth/login
Content-Type: application/json

{
  "email": "{{username}}",
  "password": "{{password}}"
}

{{
  $global.token = response.parsedBody.data.access_token;
  $global.refreshToken = response.parsedBody.data.refresh_token;
}}

###
# @name RefreshToken
POST {{url}}/auth/refresh
Content-Type: application/json

{
  "refresh_token": "{{$global.refreshToken}}"
}

{{
  $global.token = response.parsedBody.data.access_token;
  $global.refreshToken = response.parsedBody.data.refresh_token;
}}

###
# @name GetUser
GET {{url}}/users/me
Authorization: Bearer {{$global.token}}


###
# @name CreateUser
POST {{url}}/users
Authorization: Bearer {{$global.token}}
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "newuserpassword",
  "first_name": "New a",
  "last_name": "User",
  "role": "{{userRole}}"
}

{{
  $global.createUserResponse = response.parsedBody;
  $global.accountId = response.parsedBody.data.account[0];
  $global.accountUserId = response.parsedBody.data.accounts[0];
  $global.userId = response.parsedBody.data.id;
}}

###
# @name DeleteAccountUser
DELETE {{url}}/items/account_user/{{$global.accountUserId}}
Authorization: Bearer {{$global.token}}
Content-Type: application/json

###
# @name DeleteAccount
DELETE {{url}}/items/account/{{$global.accountId}}
Authorization: Bearer {{$global.token}}
Content-Type: application/json

###
# @name DeleteUser
DELETE {{url}}/users/{{$global.createUserResponse.data.id}}
Authorization: Bearer {{$global.token}}
Content-Type: application/json


###
# @name GetAccountUserTree
GET {{url}}/items/account_user
  ?fields[]=*
  &fields[]=account.*
  &fields[]=account.owner.id,account.owner.first_name,account.owner.last_name,account.owner.email,account.owner.avatar
  &fields[]=account.properties.*
  &fields[]=account.properties.water_pumps.*
  &fields[]=account.properties.projects.*
  &fields[]=account.properties.projects.irrigation_plans.*
  &fields[]=account.properties.projects.irrigation_plans.steps.*
  &fields[]=account.properties.projects.sectors.*
  &fields[]=account.properties.devices.*
  &fields[]=account.properties.devices.device.*
Authorization: Bearer {{$global.token}}

###
# @name GetAccounts
GET {{url}}/items/account_user
  ?fields[]=id,role,start_date,end_date,account.id,account.owner.id,account.owner.first_name,account.owner.last_name,account.owner.email
Authorization: Bearer {{$global.token}}

###
# @name ListProperties
GET {{url}}/items/property
  ?fields[]=*
Authorization: Bearer {{$global.token}}


###
# @name GetDevices
GET {{url}}/items/device?fields=*
Authorization: Bearer {{$global.token}}

###
# @name GetAccountUsers
GET {{url}}/items/account_user
  ?fields=id,role,start_date,end_date,account.owner.id,account.owner.first_name,account.owner.last_name,account.owner.email
Authorization: Bearer {{$global.token}}


###
# @name ListIrrigationPlans
GET {{url}}/items/irrigation_plan
  ?fields[]=*
Authorization: Bearer {{$global.token}}

###
# @name ListIrrigationPlanSteps
GET {{url}}/items/irrigation_plan_step
  ?fields[]=*
Authorization: Bearer {{$global.token}}

###
# @name ListPropertyDevices
GET {{url}}/items/property_device
  ?fields[]=*
Authorization: Bearer {{$global.token}}

###
# @name ListWaterPumps
GET {{url}}/items/water_pump
  ?fields[]=*
Authorization: Bearer {{$global.token}}