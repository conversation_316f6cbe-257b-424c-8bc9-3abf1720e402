import { Directus } from "@directus/sdk";

export async function createProperty(
  directus: Directus<any>,
  token: string,
  name: string
) {
  return (await directus
    .items("property")
    .createOne(
      { name },
      { headers: { authorization: `Bear<PERSON> ${token}` } }
    )) as any;
}

export async function createDevice(
  directus: Directus<any>,
  token: string,
  identifier: string,
  model: string
) {
  return (await directus
    .items("device")
    .createOne(
      { identifier, model },
      { headers: { authorization: `Bearer ${token}` } }
    )) as any;
}

export async function createPropertyDevice(
  directus: Directus<any>,
  token: string,
  propertyId: string,
  deviceId: string
) {
  return (await directus
    .items("property_device")
    .createOne(
      { property: propertyId, device: deviceId },
      { headers: { authorization: `Bearer ${token}` } }
    )) as any;
}

export async function createWaterPump(
  directus: Directus<any>,
  token: string,
  propertyId: string,
  controllerId: string,
  type: string,
  label: string
) {
  return (await directus
    .items("water_pump")
    .createOne(
      {
        property: propertyId,
        water_pump_controller: controllerId,
        pump_type: type,
        label,
      },
      { headers: { authorization: `Bearer ${token}` } }
    )) as any;
}

export async function createReservoir(
  directus: Directus<any>,
  token: string,
  propertyId: string,
  name: string,
  rmId?: string,
  pumpId?: string
) {
  const data: any = { property: propertyId, name };
  if (rmId) data.reservoir_monitor = rmId;
  if (pumpId) data.water_pump = pumpId;
  return (await directus
    .items("reservoir")
    .createOne(data, { headers: { authorization: `Bearer ${token}` } })) as any;
}

export async function createProject(
  directus: Directus<any>,
  token: string,
  propertyId: string,
  name: string,
  licId?: string,
  irrigationPumpId?: string
) {
  const data: any = { property: propertyId, name };
  if (licId) data.localized_irrigation_controller = licId;
  if (irrigationPumpId) data.irrigation_water_pump = irrigationPumpId;
  return (await directus
    .items("project")
    .createOne(data, { headers: { authorization: `Bearer ${token}` } })) as any;
}

export async function createSector(
  directus: Directus<any>,
  token: string,
  projectId: string,
  name: string,
  vcId: string,
  output: number
) {
  return (await directus
    .items("sector")
    .createOne(
      {
        project: projectId,
        name,
        valve_controller: vcId,
        valve_controller_output: output,
      },
      { headers: { authorization: `Bearer ${token}` } }
    )) as any;
}

export async function createMeshDeviceMapping(
  directus: Directus<any>,
  token: string,
  meshPropertyDeviceId: string,
  licPropertyDeviceId: string
) {
  return (await directus
    .items("mesh_device_mapping")
    .createOne(
      {
        mesh_property_device: meshPropertyDeviceId,
        lic_property_device: licPropertyDeviceId,
      },
      { headers: { authorization: `Bearer ${token}` } }
    )) as any;
}
