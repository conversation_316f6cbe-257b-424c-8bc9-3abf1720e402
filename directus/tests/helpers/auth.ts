import { Directus } from "@directus/sdk";

export function getDirectus() {
  return new Directus("http://localhost:8055");
}

export async function login(
  directus: Directus<any>,
  email: string,
  password: string
) {
  if (directus.auth.token) return;
  await directus.auth.login({ email, password });
}

export async function getAdmin(directus: Directus<any>) {
  await login(directus, "<EMAIL>", "password");
  return {
    access_token: directus.auth.token,
  };
}

export async function getRegular(directus: Directus<any>) {
  await login(directus, "<EMAIL>", "password");
  return {
    access_token: directus.auth.token,
  };
}
