import { test, describe, expect } from "bun:test";
import { getAdmin, getDirectus, getRegular, login } from "./helpers/auth";
import {
  createDevice,
  createProperty,
  createPropertyDevice,
  createWaterPump,
  createReservoir,
  createProject,
  createSector,
  createMeshDeviceMapping,
} from "./helpers/data";

describe("Mesh Network Constraints", async () => {
  const directus = getDirectus();
  const admin = await getAdmin(directus);
  const regular = await getRegular(directus);

  // Setup: Create common resources
  const property = await createProperty(
    directus,
    admin.access_token,
    "Test Property"
  );

  // LICs
  const lic1Device = await createDevice(
    directus,
    admin.access_token,
    "LIC-1",
    "LIC"
  );
  const lic1PropDevice = await createPropertyDevice(
    directus,
    admin.access_token,
    property.id,
    lic1Device.id
  );
  const lic2Device = await createDevice(
    directus,
    admin.access_token,
    "LIC-2",
    "LIC"
  );
  const lic2PropDevice = await createPropertyDevice(
    directus,
    admin.access_token,
    property.id,
    lic2Device.id
  );

  // RMs
  const rm1Device = await createDevice(
    directus,
    admin.access_token,
    "RM-1",
    "RM"
  );
  const rm1PropDevice = await createPropertyDevice(
    directus,
    admin.access_token,
    property.id,
    rm1Device.id
  );
  const rm2Device = await createDevice(
    directus,
    admin.access_token,
    "RM-2",
    "RM"
  );
  const rm2PropDevice = await createPropertyDevice(
    directus,
    admin.access_token,
    property.id,
    rm2Device.id
  );

  // Pumps
  const pump1Controller = await createDevice(
    directus,
    admin.access_token,
    "PUMP-CTRL-1",
    "WPC-PL10"
  );
  const pump1PropDevice = await createPropertyDevice(
    directus,
    admin.access_token,
    property.id,
    pump1Controller.id
  );
  const pump1 = await createWaterPump(
    directus,
    admin.access_token,
    property.id,
    pump1Controller.id,
    "SERVICE",
    "Pump 1"
  );

  const pump2Controller = await createDevice(
    directus,
    admin.access_token,
    "PUMP-CTRL-2",
    "WPC-PL10"
  );
  const pump2PropDevice = await createPropertyDevice(
    directus,
    admin.access_token,
    property.id,
    pump2Controller.id
  );
  const pump2 = await createWaterPump(
    directus,
    admin.access_token,
    property.id,
    pump2Controller.id,
    "SERVICE",
    "Pump 2"
  );

  const irrigationPumpController = await createDevice(
    directus,
    admin.access_token,
    "IRR-PUMP-CTRL",
    "WPC-PL50"
  );
  const irrigationPumpPropDevice = await createPropertyDevice(
    directus,
    admin.access_token,
    property.id,
    irrigationPumpController.id
  );
  const irrigationPump = await createWaterPump(
    directus,
    admin.access_token,
    property.id,
    irrigationPumpController.id,
    "IRRIGATION",
    "Irrigation Pump"
  );

  // VCs
  const vc1Device = await createDevice(
    directus,
    admin.access_token,
    "VC-1",
    "VC"
  );
  const vc1PropDevice = await createPropertyDevice(
    directus,
    admin.access_token,
    property.id,
    vc1Device.id
  );

  // Mesh Mappings
  // Network 1
  await createMeshDeviceMapping(
    directus,
    admin.access_token,
    rm1PropDevice.id,
    lic1PropDevice.id
  );
  await createMeshDeviceMapping(
    directus,
    admin.access_token,
    pump1PropDevice.id,
    lic1PropDevice.id
  );
  await createMeshDeviceMapping(
    directus,
    admin.access_token,
    irrigationPumpPropDevice.id,
    lic1PropDevice.id
  );
  await createMeshDeviceMapping(
    directus,
    admin.access_token,
    vc1PropDevice.id,
    lic1PropDevice.id
  );
  // Network 2
  await createMeshDeviceMapping(
    directus,
    admin.access_token,
    rm2PropDevice.id,
    lic2PropDevice.id
  );
  await createMeshDeviceMapping(
    directus,
    admin.access_token,
    pump2PropDevice.id,
    lic2PropDevice.id
  );

  test("Reservoir: should succeed if RM and pump are in the same mesh network", async () => {
    const reservoir = await createReservoir(
      directus,
      admin.access_token,
      property.id,
      "Same Mesh Reservoir",
      rm1Device.id,
      pump1.id
    );
    expect(reservoir).toBeDefined();
  });

  test("Reservoir: should fail if RM and pump are in different mesh networks", async () => {
    try {
      await createReservoir(
        directus,
        admin.access_token,
        property.id,
        "Different Mesh Reservoir",
        rm1Device.id,
        pump2.id
      );
    } catch (e: any) {
      expect(e.response.data.errors[0].message).toContain(
        "Reservoir Monitor and Service Water Pump must be in the same mesh network."
      );
    }
  });

  test("Project: should succeed if pumps are in the same mesh network as LIC", async () => {
    const project = await createProject(
      directus,
      admin.access_token,
      property.id,
      "Same Mesh Project",
      lic1Device.id,
      irrigationPump.id
    );
    expect(project).toBeDefined();
  });

  test("Project: should fail if irrigation pump is in a different mesh network from LIC", async () => {
    try {
      await createProject(
        directus,
        admin.access_token,
        property.id,
        "Different Mesh Project",
        lic2Device.id,
        irrigationPump.id
      );
    } catch (e: any) {
      expect(e.response.data.errors[0].message).toContain(
        "Irrigation pump must be in the same mesh network as the project LIC."
      );
    }
  });

  test("Sector: should succeed if VC is in the same mesh network as project LIC", async () => {
    const project = await createProject(
      directus,
      admin.access_token,
      property.id,
      "Sector Project",
      lic1Device.id,
      irrigationPump.id
    );
    const sector = await createSector(
      directus,
      admin.access_token,
      project.id,
      "Same Mesh Sector",
      vc1Device.id,
      1
    );
    expect(sector).toBeDefined();
  });

  test("Sector: should fail if VC is in a different mesh network from project LIC", async () => {
    const project = await createProject(
      directus,
      admin.access_token,
      property.id,
      "Sector Project Diff Mesh",
      lic2Device.id
    );
    try {
      await createSector(
        directus,
        admin.access_token,
        project.id,
        "Different Mesh Sector",
        vc1Device.id,
        1
      );
    } catch (e: any) {
      expect(e.response.data.errors[0].message).toContain(
        "Valve Controller must be in the same mesh network as the project LIC."
      );
    }
  });
});
