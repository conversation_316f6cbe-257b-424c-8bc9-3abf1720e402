/**
 * This migration file is a placeholder for the up migration.
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    await tx.raw(
      "CREATE TYPE day_of_week_enum AS ENUM ('Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun');"
    );
    await tx.schema.createTable("irrigation_plan", (table) => {
      table
        .uuid("id")
        .primary()
        .notNullable()
        .defaultTo(tx.raw("gen_random_uuid()"));
      table
        .uuid("project")
        .notNullable()
        .references("id")
        .inTable("project")
        .onDelete("RESTRICT")
        .onUpdate("RESTRICT");
      table.string("name", 255).notNullable();
      table.text("description").nullable();
      table.time("start_time").notNullable();
      table
        .jsonb("days_of_week")
        .defaultTo('["MON", "TUE", "WED", "THU", "FRI", "SAT", "SUN"]')
        .notNullable();
      table.boolean("is_enabled").notNullable().defaultTo(false);
      table.boolean("fertigation_enabled").notNullable().defaultTo(false);
      table.integer("total_irrigation_duration").notNullable().defaultTo(0);
      table.date("start_date").nullable();
      table.date("end_date").nullable();
      table
        .timestamp("date_created", { useTz: true })
        .notNullable()
        .defaultTo(tx.fn.now());
      table
        .uuid("user_created")
        .nullable()
        .references("id")
        .inTable("directus_users")
        .onDelete("RESTRICT")
        .onUpdate("RESTRICT");
      table
        .timestamp("date_updated", { useTz: true })
        .notNullable()
        .defaultTo(tx.fn.now());
      table
        .uuid("user_updated")
        .nullable()
        .references("id")
        .inTable("directus_users")
        .onDelete("RESTRICT")
        .onUpdate("RESTRICT");
      table.jsonb("metadata").nullable();
      table.text("notes").nullable();
      table.unique(["project", "name"], {
        indexName: "irrigation_plan_project_name_unq",
      });
    });

    await tx.raw(`
      CREATE TRIGGER set_irrigation_plan_date_updated
      BEFORE UPDATE ON irrigation_plan
      FOR EACH ROW
      EXECUTE FUNCTION update_timestamp_column();
    `);
  });
}

/**
 * This migration file is a placeholder for the down migration.
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    await tx.raw(
      "DROP TRIGGER IF EXISTS set_irrigation_plan_date_updated ON irrigation_plan;"
    );
    await tx.schema.dropTableIfExists("irrigation_plan");
    await tx.raw("DROP TYPE IF EXISTS day_of_week_enum;");
  });
}
