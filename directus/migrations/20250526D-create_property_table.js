/**
 * This migration file is a placeholder for the up migration.
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    await tx.schema.createTable("property", (table) => {
      table
        .uuid("id")
        .primary()
        .notNullable()
        .defaultTo(tx.raw("gen_random_uuid()"));
      table
        .uuid("account")
        .notNullable()
        .references("id")
        .inTable("account")
        .onDelete("RESTRICT")
        .onUpdate("RESTRICT");
      table.string("name", 255).notNullable();

      table
        .integer("backwash_duration_minutes")
        .nullable()
        .comment("Time in minutes for backwashing operations");
      table
        .integer("backwash_period_minutes")
        .nullable()
        .comment("Time in minutes between backwashing operations");
      table
        .integer("backwash_delay_seconds")
        .nullable()
        .comment("Time in seconds before backwashing operations starts");

      table.boolean("rain_gauge_enabled").defaultTo(false).notNullable();
      table
        .float("rain_gauge_resolution_mm")
        .defaultTo(0.2)
        .nullable()
        .comment("Resolution of the rain gauge in millimeters");
      table
        .float("precipitation_volume_limit_mm")
        .defaultTo(2)
        .nullable()
        .comment(
          "Maximum precipitation volume in millimeters before irrigation is suspended"
        );
      table
        .float("precipitation_suspended_duration_hours")
        .defaultTo(24)
        .nullable()
        .comment(
          "Duration in hours to suspend the irrigation after precipitation limit is reached"
        );

      table
        .string("timezone", 255)
        .notNullable()
        .defaultTo("America/Sao_Paulo");
      table.specificType("point", "geometry(Point,4326)").nullable();
      table.string("address_postal_code", 255).nullable();
      table.string("address_street_name", 255).nullable();
      table.string("address_street_number", 255).nullable();
      table.string("address_complement", 255).nullable();
      table.string("address_neighborhood", 255).nullable();
      table.string("address_city", 255).nullable();
      table.string("address_state", 255).nullable();
      table.string("address_country", 255).nullable().defaultTo("Brasil");
      table.text("notes").nullable();
      table
        .timestamp("date_created", { useTz: true })
        .notNullable()
        .defaultTo(tx.fn.now());
      table
        .uuid("user_created")
        .nullable()
        .references("id")
        .inTable("directus_users")
        .onDelete("RESTRICT")
        .onUpdate("RESTRICT");
      table
        .timestamp("date_updated", { useTz: true })
        .notNullable()
        .defaultTo(tx.fn.now());
      table
        .uuid("user_updated")
        .nullable()
        .references("id")
        .inTable("directus_users")
        .onDelete("RESTRICT")
        .onUpdate("RESTRICT");
      table.jsonb("metadata").nullable();
      table.unique(["account", "name"], {
        indexName: "property_account_name_unq",
      });
    });
    await tx.raw(`
      CREATE TRIGGER set_property_date_updated
      BEFORE UPDATE ON property
      FOR EACH ROW
      EXECUTE FUNCTION update_timestamp_column();
    `);
  });
}

/**
 * This migration file is a placeholder for the down migration.
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    await tx.raw(
      "DROP TRIGGER IF EXISTS set_property_date_updated ON property;"
    );
    await tx.schema.dropTableIfExists("property");
  });
}
