/**
 * This migration file is a placeholder for the up migration.
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    await tx.schema.createTable("irrigation_plan_step", (table) => {
      table
        .uuid("id")
        .primary()
        .notNullable()
        .defaultTo(tx.raw("gen_random_uuid()"));
      table
        .uuid("irrigation_plan")
        .notNullable()
        .references("id")
        .inTable("irrigation_plan")
        .onDelete("RESTRICT")
        .onUpdate("RESTRICT");
      table
        .uuid("sector")
        .notNullable()
        .references("id")
        .inTable("sector")
        .onDelete("RESTRICT")
        .onUpdate("RESTRICT");
      table.text("description").nullable();
      table.integer("order").notNullable();
      table.integer("duration_seconds").notNullable();
      table.integer("fertigation_start_delay_seconds").nullable();
      table.integer("fertigation_duration_seconds").nullable();
      table
        .timestamp("date_created", { useTz: true })
        .notNullable()
        .defaultTo(tx.fn.now());
      table
        .uuid("user_created")
        .nullable()
        .references("id")
        .inTable("directus_users")
        .onDelete("RESTRICT")
        .onUpdate("RESTRICT");
      table
        .timestamp("date_updated", { useTz: true })
        .notNullable()
        .defaultTo(tx.fn.now());
      table
        .uuid("user_updated")
        .nullable()
        .references("id")
        .inTable("directus_users")
        .onDelete("RESTRICT")
        .onUpdate("RESTRICT");
      table.jsonb("metadata").nullable();
      table.text("notes").nullable();

      // Constraints
      table.unique(["irrigation_plan", "order"], {
        name: "irrigation_plan_step_irrigation_plan_order_unique",
        deferrable: "deferred",
      });
      table.unique(["irrigation_plan", "sector"], {
        name: "irrigation_plan_step_irrigation_plan_sector_unique",
      });
      table.check(
        "duration_seconds > 0",
        undefined,
        "irrigation_plan_step_duration_positive"
      );
      table.check(
        "fertigation_start_delay_seconds >= 0",
        undefined,
        "irrigation_plan_step_fertigation_delay_nonnegative"
      );
      table.check(
        "fertigation_duration_seconds > 0",
        undefined,
        "irrigation_plan_step_fertigation_duration_positive"
      );
    });
    await tx.raw(`
      CREATE TRIGGER set_irrigation_plan_step_date_updated
      BEFORE UPDATE ON irrigation_plan_step
      FOR EACH ROW
      EXECUTE FUNCTION update_timestamp_column();
    `);
  });
}

/**
 * This migration file is a placeholder for the down migration.
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    await tx.raw(
      "DROP TRIGGER IF EXISTS set_irrigation_plan_step_date_updated ON irrigation_plan_step;"
    );
    await tx.schema.dropTableIfExists("irrigation_plan_step");
  });
}
