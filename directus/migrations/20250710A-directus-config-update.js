/**
 * Applies the current state of the Directus config to the database.
 * This migration file is auto-generated and should not be manually edited.
 * @module directus/migration/gen
 * @version 2025-07-10T15:41:48.017Z
 * @description This migration applies the current state of the Directus config to the database.
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    // Insert 6 records into directus_fields
    await tx.batchInsert("directus_fields", [
      {
        collection: "irrigation_plan",
        field: "fertigation_enabled",
        special: "cast-boolean",
        interface: "boolean",
        options: null,
        display: "boolean",
        display_options: null,
        readonly: false,
        hidden: false,
        sort: 6,
        width: "half",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      {
        collection: "irrigation_plan_step",
        field: "fertigation_start_delay_seconds",
        special: null,
        interface: "input",
        options: { min: 0 },
        display: "raw",
        display_options: null,
        readonly: false,
        hidden: false,
        sort: 10,
        width: "half",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      {
        collection: "irrigation_plan_step",
        field: "fertigation_duration_seconds",
        special: null,
        interface: "input",
        options: { min: 0 },
        display: "raw",
        display_options: null,
        readonly: false,
        hidden: false,
        sort: 11,
        width: "half",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      {
        collection: "project",
        field: "pipe_wash_time_seconds",
        special: null,
        interface: "input",
        options: { min: 0 },
        display: "raw",
        display_options: null,
        readonly: false,
        hidden: false,
        sort: 13,
        width: "full",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      {
        collection: "project",
        field: "backwash_duration_seconds",
        special: null,
        interface: "input",
        options: { min: 0 },
        display: "raw",
        display_options: null,
        readonly: false,
        hidden: false,
        sort: 14,
        width: "half",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      {
        collection: "project",
        field: "backwash_period_seconds",
        special: null,
        interface: "input",
        options: { min: 0 },
        display: "raw",
        display_options: null,
        readonly: false,
        hidden: false,
        sort: 15,
        width: "half",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
    ]);

    // Update 33 records in directus_fields
    await tx("directus_fields")
      .where({
        collection: "irrigation_plan",
        field: "total_irrigation_duration",
      })
      .update({ sort: 4 });
    await tx("directus_fields")
      .where({ collection: "irrigation_plan", field: "date_created" })
      .update({ sort: 11 });
    await tx("directus_fields")
      .where({ collection: "irrigation_plan", field: "start_time" })
      .update({ sort: 7 });
    await tx("directus_fields")
      .where({ collection: "irrigation_plan", field: "start_date" })
      .update({ sort: 9 });
    await tx("directus_fields")
      .where({ collection: "irrigation_plan", field: "user_created" })
      .update({ sort: 12 });
    await tx("directus_fields")
      .where({ collection: "irrigation_plan", field: "date_updated" })
      .update({ sort: 13 });
    await tx("directus_fields")
      .where({ collection: "irrigation_plan", field: "user_updated" })
      .update({ sort: 14 });
    await tx("directus_fields")
      .where({ collection: "irrigation_plan", field: "description" })
      .update({ sort: 16 });
    await tx("directus_fields")
      .where({ collection: "irrigation_plan", field: "metadata" })
      .update({ sort: 17 });
    await tx("directus_fields")
      .where({ collection: "irrigation_plan", field: "notes" })
      .update({ sort: 18 });
    await tx("directus_fields")
      .where({ collection: "irrigation_plan_step", field: "description" })
      .update({ sort: 12 });
    await tx("directus_fields")
      .where({ collection: "irrigation_plan", field: "name" })
      .update({ width: "full" });
    await tx("directus_fields")
      .where({ collection: "irrigation_plan", field: "project" })
      .update({ sort: 3 });
    await tx("directus_fields")
      .where({ collection: "irrigation_plan", field: "end_date" })
      .update({ sort: 10 });
    await tx("directus_fields")
      .where({ collection: "irrigation_plan_step", field: "date_created" })
      .update({ sort: 2 });
    await tx("directus_fields")
      .where({ collection: "irrigation_plan_step", field: "user_created" })
      .update({ sort: 3 });
    await tx("directus_fields")
      .where({ collection: "irrigation_plan_step", field: "date_updated" })
      .update({ sort: 4 });
    await tx("directus_fields")
      .where({ collection: "irrigation_plan_step", field: "user_updated" })
      .update({ sort: 5 });
    await tx("directus_fields")
      .where({ collection: "irrigation_plan_step", field: "irrigation_plan" })
      .update({ sort: 6 });
    await tx("directus_fields")
      .where({ collection: "irrigation_plan_step", field: "sector" })
      .update({ sort: 7 });
    await tx("directus_fields")
      .where({ collection: "irrigation_plan_step", field: "order" })
      .update({ sort: 8, width: "half" });
    await tx("directus_fields")
      .where({ collection: "irrigation_plan_step", field: "duration_seconds" })
      .update({ sort: 9 });
    await tx("directus_fields")
      .where({ collection: "irrigation_plan_step", field: "metadata" })
      .update({ sort: 13 });
    await tx("directus_fields")
      .where({ collection: "irrigation_plan_step", field: "notes" })
      .update({ sort: 14 });
    await tx("directus_fields")
      .where({ collection: "irrigation_plan_step", field: "id" })
      .update({ sort: 1 });
    await tx("directus_fields")
      .where({ collection: "project", field: "irrigation_plans" })
      .update({ sort: 17 });
    await tx("directus_fields")
      .where({ collection: "project", field: "description" })
      .update({ sort: 18 });
    await tx("directus_fields")
      .where({ collection: "project", field: "metadata" })
      .update({ sort: 19 });
    await tx("directus_fields")
      .where({ collection: "project", field: "notes" })
      .update({ sort: 20 });
    await tx("directus_fields")
      .where({ collection: "project", field: "sectors" })
      .update({ sort: 16 });
    await tx("directus_fields")
      .where({ collection: "irrigation_plan", field: "is_enabled" })
      .update({ sort: 5 });
    await tx("directus_fields")
      .where({ collection: "irrigation_plan", field: "days_of_week" })
      .update({ sort: 8 });
    await tx("directus_fields")
      .where({ collection: "irrigation_plan", field: "steps" })
      .update({ sort: 15 });

    // Update 6 records in directus_permissions
    await tx("directus_permissions")
      .where({
        collection: "irrigation_plan",
        action: "create",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        fields:
          "name,is_enabled,start_time,days_of_week,start_date,end_date,project,steps,description,metadata,notes,fertigation_enabled",
      });
    await tx("directus_permissions")
      .where({
        collection: "irrigation_plan",
        action: "update",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        fields:
          "name,is_enabled,start_time,days_of_week,start_date,end_date,project,steps,description,metadata,notes,fertigation_enabled",
      });
    await tx("directus_permissions")
      .where({
        collection: "irrigation_plan_step",
        action: "create",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        fields:
          "irrigation_plan,sector,description,order,duration_seconds,delay_seconds_after,metadata,notes,fertigation_start_delay_seconds,fertigation_duration_seconds",
      });
    await tx("directus_permissions")
      .where({
        collection: "irrigation_plan_step",
        action: "update",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        fields:
          "irrigation_plan,sector,description,order,duration_seconds,delay_seconds_after,metadata,notes,fertigation_start_delay_seconds,fertigation_duration_seconds",
      });
    await tx("directus_permissions")
      .where({
        collection: "project",
        action: "create",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        fields:
          "property,name,localized_irrigation_controller,start_date,end_date,irrigation_water_pump,fertigation_water_pump,sectors,irrigation_plans,description,metadata,notes,pipe_wash_time_seconds,backwash_duration_seconds,backwash_period_seconds",
      });
    await tx("directus_permissions")
      .where({
        collection: "project",
        action: "update",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        fields:
          "property,name,localized_irrigation_controller,start_date,end_date,irrigation_water_pump,fertigation_water_pump,sectors,irrigation_plans,description,metadata,notes,backwash_period_seconds,backwash_duration_seconds,pipe_wash_time_seconds",
      });
  });
}

/**
 * Reverts the changes made by the up migration.
 * This migration file is auto-generated and should not be manually edited.
 * @module directus/migration/gen
 * @version 2025-07-10T15:41:48.017Z
 * @description This migration reverts the changes made by the up migration.
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    // Revert 33 updates in directus_fields
    await tx("directus_fields")
      .where({
        collection: "irrigation_plan",
        field: "total_irrigation_duration",
      })
      .update({
        collection: "irrigation_plan",
        field: "total_irrigation_duration",
        special: null,
        interface: "input",
        options: null,
        display: "raw",
        display_options: null,
        readonly: false,
        hidden: false,
        sort: 9,
        width: "half",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      });
    await tx("directus_fields")
      .where({ collection: "irrigation_plan", field: "date_created" })
      .update({
        collection: "irrigation_plan",
        field: "date_created",
        special: "date-created",
        interface: "datetime",
        options: null,
        display: "datetime",
        display_options: null,
        readonly: false,
        hidden: false,
        sort: 10,
        width: "half",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      });
    await tx("directus_fields")
      .where({ collection: "irrigation_plan", field: "start_time" })
      .update({
        collection: "irrigation_plan",
        field: "start_time",
        special: null,
        interface: "datetime",
        options: null,
        display: "datetime",
        display_options: { use24: true, format: "short" },
        readonly: false,
        hidden: false,
        sort: 4,
        width: "half",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      });
    await tx("directus_fields")
      .where({ collection: "irrigation_plan", field: "start_date" })
      .update({
        collection: "irrigation_plan",
        field: "start_date",
        special: null,
        interface: "datetime",
        options: null,
        display: "datetime",
        display_options: null,
        readonly: false,
        hidden: false,
        sort: 6,
        width: "half",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      });
    await tx("directus_fields")
      .where({ collection: "irrigation_plan", field: "user_created" })
      .update({
        collection: "irrigation_plan",
        field: "user_created",
        special: "user-created",
        interface: "select-dropdown-m2o",
        options: null,
        display: "user",
        display_options: null,
        readonly: false,
        hidden: false,
        sort: 11,
        width: "half",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      });
    await tx("directus_fields")
      .where({ collection: "irrigation_plan", field: "date_updated" })
      .update({
        collection: "irrigation_plan",
        field: "date_updated",
        special: "date-updated",
        interface: "datetime",
        options: null,
        display: "datetime",
        display_options: null,
        readonly: false,
        hidden: false,
        sort: 12,
        width: "half",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      });
    await tx("directus_fields")
      .where({ collection: "irrigation_plan", field: "user_updated" })
      .update({
        collection: "irrigation_plan",
        field: "user_updated",
        special: "user-updated",
        interface: "select-dropdown-m2o",
        options: null,
        display: "user",
        display_options: null,
        readonly: false,
        hidden: false,
        sort: 13,
        width: "half",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      });
    await tx("directus_fields")
      .where({ collection: "irrigation_plan", field: "description" })
      .update({
        collection: "irrigation_plan",
        field: "description",
        special: null,
        interface: null,
        options: null,
        display: null,
        display_options: null,
        readonly: false,
        hidden: false,
        sort: 15,
        width: "full",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      });
    await tx("directus_fields")
      .where({ collection: "irrigation_plan", field: "metadata" })
      .update({
        collection: "irrigation_plan",
        field: "metadata",
        special: "cast-json",
        interface: null,
        options: null,
        display: null,
        display_options: null,
        readonly: false,
        hidden: false,
        sort: 16,
        width: "full",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      });
    await tx("directus_fields")
      .where({ collection: "irrigation_plan", field: "notes" })
      .update({
        collection: "irrigation_plan",
        field: "notes",
        special: null,
        interface: null,
        options: null,
        display: null,
        display_options: null,
        readonly: false,
        hidden: false,
        sort: 17,
        width: "full",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      });
    await tx("directus_fields")
      .where({ collection: "irrigation_plan_step", field: "description" })
      .update({
        collection: "irrigation_plan_step",
        field: "description",
        special: null,
        interface: null,
        options: null,
        display: null,
        display_options: null,
        readonly: false,
        hidden: false,
        sort: null,
        width: "full",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      });
    await tx("directus_fields")
      .where({ collection: "irrigation_plan", field: "name" })
      .update({
        collection: "irrigation_plan",
        field: "name",
        special: null,
        interface: "input",
        options: null,
        display: "raw",
        display_options: null,
        readonly: false,
        hidden: false,
        sort: 2,
        width: "half",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      });
    await tx("directus_fields")
      .where({ collection: "irrigation_plan", field: "project" })
      .update({
        collection: "irrigation_plan",
        field: "project",
        special: null,
        interface: "select-dropdown-m2o",
        options: null,
        display: "related-values",
        display_options: null,
        readonly: false,
        hidden: false,
        sort: 8,
        width: "half",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      });
    await tx("directus_fields")
      .where({ collection: "irrigation_plan", field: "end_date" })
      .update({
        collection: "irrigation_plan",
        field: "end_date",
        special: null,
        interface: "datetime",
        options: null,
        display: "datetime",
        display_options: null,
        readonly: false,
        hidden: false,
        sort: 7,
        width: "half",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      });
    await tx("directus_fields")
      .where({ collection: "irrigation_plan_step", field: "date_created" })
      .update({
        collection: "irrigation_plan_step",
        field: "date_created",
        special: "date-created",
        interface: "datetime",
        options: null,
        display: "datetime",
        display_options: null,
        readonly: false,
        hidden: false,
        sort: null,
        width: "half",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      });
    await tx("directus_fields")
      .where({ collection: "irrigation_plan_step", field: "user_created" })
      .update({
        collection: "irrigation_plan_step",
        field: "user_created",
        special: "user-created",
        interface: "select-dropdown-m2o",
        options: null,
        display: "user",
        display_options: null,
        readonly: false,
        hidden: false,
        sort: null,
        width: "half",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      });
    await tx("directus_fields")
      .where({ collection: "irrigation_plan_step", field: "date_updated" })
      .update({
        collection: "irrigation_plan_step",
        field: "date_updated",
        special: "date-updated",
        interface: "datetime",
        options: null,
        display: "datetime",
        display_options: null,
        readonly: false,
        hidden: false,
        sort: null,
        width: "half",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      });
    await tx("directus_fields")
      .where({ collection: "irrigation_plan_step", field: "user_updated" })
      .update({
        collection: "irrigation_plan_step",
        field: "user_updated",
        special: "user-updated",
        interface: "select-dropdown-m2o",
        options: null,
        display: "user",
        display_options: null,
        readonly: false,
        hidden: false,
        sort: null,
        width: "half",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      });
    await tx("directus_fields")
      .where({ collection: "irrigation_plan_step", field: "irrigation_plan" })
      .update({
        collection: "irrigation_plan_step",
        field: "irrigation_plan",
        special: null,
        interface: "select-dropdown-m2o",
        options: null,
        display: "related-values",
        display_options: null,
        readonly: false,
        hidden: false,
        sort: null,
        width: "half",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      });
    await tx("directus_fields")
      .where({ collection: "irrigation_plan_step", field: "sector" })
      .update({
        collection: "irrigation_plan_step",
        field: "sector",
        special: null,
        interface: "select-dropdown-m2o",
        options: null,
        display: "related-values",
        display_options: null,
        readonly: false,
        hidden: false,
        sort: null,
        width: "half",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      });
    await tx("directus_fields")
      .where({ collection: "irrigation_plan_step", field: "order" })
      .update({
        collection: "irrigation_plan_step",
        field: "order",
        special: null,
        interface: "input",
        options: null,
        display: "raw",
        display_options: null,
        readonly: false,
        hidden: false,
        sort: null,
        width: "full",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      });
    await tx("directus_fields")
      .where({ collection: "irrigation_plan_step", field: "duration_seconds" })
      .update({
        collection: "irrigation_plan_step",
        field: "duration_seconds",
        special: null,
        interface: "input",
        options: null,
        display: "raw",
        display_options: null,
        readonly: false,
        hidden: false,
        sort: null,
        width: "half",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      });
    await tx("directus_fields")
      .where({ collection: "irrigation_plan_step", field: "metadata" })
      .update({
        collection: "irrigation_plan_step",
        field: "metadata",
        special: "cast-json",
        interface: null,
        options: null,
        display: null,
        display_options: null,
        readonly: false,
        hidden: false,
        sort: null,
        width: "full",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      });
    await tx("directus_fields")
      .where({ collection: "irrigation_plan_step", field: "notes" })
      .update({
        collection: "irrigation_plan_step",
        field: "notes",
        special: null,
        interface: null,
        options: null,
        display: null,
        display_options: null,
        readonly: false,
        hidden: false,
        sort: null,
        width: "full",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      });
    await tx("directus_fields")
      .where({ collection: "irrigation_plan_step", field: "id" })
      .update({
        collection: "irrigation_plan_step",
        field: "id",
        special: "uuid",
        interface: null,
        options: null,
        display: null,
        display_options: null,
        readonly: false,
        hidden: false,
        sort: null,
        width: "full",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      });
    await tx("directus_fields")
      .where({ collection: "project", field: "irrigation_plans" })
      .update({
        collection: "project",
        field: "irrigation_plans",
        special: "o2m",
        interface: "list-o2m",
        options: null,
        display: "related-values",
        display_options: null,
        readonly: false,
        hidden: false,
        sort: 14,
        width: "full",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      });
    await tx("directus_fields")
      .where({ collection: "project", field: "description" })
      .update({
        collection: "project",
        field: "description",
        special: null,
        interface: "input-multiline",
        options: null,
        display: "raw",
        display_options: null,
        readonly: false,
        hidden: false,
        sort: 15,
        width: "full",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      });
    await tx("directus_fields")
      .where({ collection: "project", field: "metadata" })
      .update({
        collection: "project",
        field: "metadata",
        special: "cast-json",
        interface: null,
        options: null,
        display: null,
        display_options: null,
        readonly: false,
        hidden: false,
        sort: 16,
        width: "full",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      });
    await tx("directus_fields")
      .where({ collection: "project", field: "notes" })
      .update({
        collection: "project",
        field: "notes",
        special: null,
        interface: null,
        options: null,
        display: null,
        display_options: null,
        readonly: false,
        hidden: false,
        sort: 17,
        width: "full",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      });
    await tx("directus_fields")
      .where({ collection: "project", field: "sectors" })
      .update({
        collection: "project",
        field: "sectors",
        special: "o2m",
        interface: "list-o2m",
        options: null,
        display: "related-values",
        display_options: null,
        readonly: false,
        hidden: false,
        sort: 13,
        width: "full",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      });
    await tx("directus_fields")
      .where({ collection: "irrigation_plan", field: "is_enabled" })
      .update({
        collection: "irrigation_plan",
        field: "is_enabled",
        special: "cast-boolean",
        interface: "boolean",
        options: null,
        display: "boolean",
        display_options: null,
        readonly: false,
        hidden: false,
        sort: 3,
        width: "half",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      });
    await tx("directus_fields")
      .where({ collection: "irrigation_plan", field: "days_of_week" })
      .update({
        collection: "irrigation_plan",
        field: "days_of_week",
        special: '{"cast-json"}',
        interface: "select-multiple-dropdown",
        options: {
          choices: [
            { text: "Monday", value: "MON" },
            { text: "Tuesday", value: "TUE" },
            { text: "Wednesday", value: "WED" },
            { text: "Thursday", value: "THU" },
            { text: "Friday", value: "FRI" },
            { text: "Saturday", value: "SAT" },
            { text: "Sunday", value: "SUN" },
          ],
        },
        display: "labels",
        display_options: null,
        readonly: false,
        hidden: false,
        sort: 4,
        width: "half",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      });
    await tx("directus_fields")
      .where({ collection: "irrigation_plan", field: "steps" })
      .update({
        collection: "irrigation_plan",
        field: "steps",
        special: "o2m",
        interface: "list-o2m",
        options: null,
        display: "related-values",
        display_options: null,
        readonly: false,
        hidden: false,
        sort: 14,
        width: "full",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      });

    // Revert 6 updates in directus_permissions
    await tx("directus_permissions")
      .where({
        collection: "irrigation_plan",
        action: "create",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        collection: "irrigation_plan",
        action: "create",
        permissions: null,
        validation: null,
        presets: null,
        fields:
          "name,is_enabled,start_time,days_of_week,start_date,end_date,project,steps,description,metadata,notes",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      });
    await tx("directus_permissions")
      .where({
        collection: "irrigation_plan",
        action: "update",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        collection: "irrigation_plan",
        action: "update",
        permissions: {
          _and: [
            {
              project: {
                property: {
                  account: { users: { user: { _eq: "$CURRENT_USER" } } },
                },
              },
            },
            {
              project: {
                property: { account: { users: { role: { _eq: "admin" } } } },
              },
            },
            {
              _or: [
                {
                  project: {
                    property: {
                      account: { users: { start_date: { _null: true } } },
                    },
                  },
                },
                {
                  project: {
                    property: {
                      account: { users: { start_date: { _lte: "$NOW" } } },
                    },
                  },
                },
              ],
            },
            {
              _or: [
                {
                  project: {
                    property: {
                      account: { users: { end_date: { _null: true } } },
                    },
                  },
                },
                {
                  project: {
                    property: {
                      account: { users: { end_date: { _gt: "$NOW" } } },
                    },
                  },
                },
              ],
            },
          ],
        },
        validation: null,
        presets: null,
        fields:
          "name,is_enabled,start_time,days_of_week,start_date,end_date,project,steps,description,metadata,notes",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      });
    await tx("directus_permissions")
      .where({
        collection: "irrigation_plan_step",
        action: "create",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        collection: "irrigation_plan_step",
        action: "create",
        permissions: null,
        validation: null,
        presets: null,
        fields:
          "irrigation_plan,sector,description,order,duration_seconds,delay_seconds_after,metadata,notes",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      });
    await tx("directus_permissions")
      .where({
        collection: "irrigation_plan_step",
        action: "update",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        collection: "irrigation_plan_step",
        action: "update",
        permissions: {
          _and: [
            {
              irrigation_plan: {
                project: {
                  property: {
                    account: { users: { user: { _eq: "$CURRENT_USER" } } },
                  },
                },
              },
            },
            {
              irrigation_plan: {
                project: {
                  property: { account: { users: { role: { _eq: "admin" } } } },
                },
              },
            },
            {
              _or: [
                {
                  irrigation_plan: {
                    project: {
                      property: {
                        account: { users: { start_date: { _null: true } } },
                      },
                    },
                  },
                },
                {
                  irrigation_plan: {
                    project: {
                      property: {
                        account: { users: { start_date: { _lte: "$NOW" } } },
                      },
                    },
                  },
                },
              ],
            },
            {
              _or: [
                {
                  irrigation_plan: {
                    project: {
                      property: {
                        account: { users: { end_date: { _null: true } } },
                      },
                    },
                  },
                },
                {
                  irrigation_plan: {
                    project: {
                      property: {
                        account: { users: { end_date: { _gt: "$NOW" } } },
                      },
                    },
                  },
                },
              ],
            },
          ],
        },
        validation: null,
        presets: null,
        fields:
          "irrigation_plan,sector,description,order,duration_seconds,delay_seconds_after,metadata,notes",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      });
    await tx("directus_permissions")
      .where({
        collection: "project",
        action: "create",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        collection: "project",
        action: "create",
        permissions: null,
        validation: null,
        presets: null,
        fields:
          "property,name,localized_irrigation_controller,start_date,end_date,irrigation_water_pump,fertigation_water_pump,sectors,irrigation_plans,description,metadata,notes",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      });
    await tx("directus_permissions")
      .where({
        collection: "project",
        action: "update",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        collection: "project",
        action: "update",
        permissions: {
          _and: [
            {
              property: {
                account: { users: { user: { _eq: "$CURRENT_USER" } } },
              },
            },
            { property: { account: { users: { role: { _eq: "admin" } } } } },
            {
              _or: [
                {
                  property: {
                    account: { users: { start_date: { _null: true } } },
                  },
                },
                {
                  property: {
                    account: { users: { start_date: { _lte: "$NOW" } } },
                  },
                },
              ],
            },
            {
              _or: [
                {
                  property: {
                    account: { users: { end_date: { _null: true } } },
                  },
                },
                {
                  property: {
                    account: { users: { end_date: { _gt: "$NOW" } } },
                  },
                },
              ],
            },
          ],
        },
        validation: null,
        presets: null,
        fields:
          "property,name,localized_irrigation_controller,start_date,end_date,irrigation_water_pump,fertigation_water_pump,sectors,irrigation_plans,description,metadata,notes",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      });

    // Delete 6 records from directus_fields (reverse of insert)
    await tx("directus_fields")
      .where({ collection: "irrigation_plan", field: "fertigation_enabled" })
      .del();
    await tx("directus_fields")
      .where({
        collection: "irrigation_plan_step",
        field: "fertigation_start_delay_seconds",
      })
      .del();
    await tx("directus_fields")
      .where({
        collection: "irrigation_plan_step",
        field: "fertigation_duration_seconds",
      })
      .del();
    await tx("directus_fields")
      .where({ collection: "project", field: "pipe_wash_time_seconds" })
      .del();
    await tx("directus_fields")
      .where({ collection: "project", field: "backwash_duration_seconds" })
      .del();
    await tx("directus_fields")
      .where({ collection: "project", field: "backwash_period_seconds" })
      .del();
  });
}
