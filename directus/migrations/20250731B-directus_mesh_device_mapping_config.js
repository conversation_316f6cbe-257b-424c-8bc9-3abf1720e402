/**
 * Applies Directus configuration for mesh_device_mapping collection and updates property_device current_mesh_device_mapping field.
 * Mirrors patterns used in 20250625B-reservoir-directus-config.js
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    // 1) Add mesh_device_mapping collection
    await tx.batchInsert("directus_collections", [
      {
        collection: "mesh_device_mapping",
        icon: "hub",
        note: "Association between a mesh property_device (WPC, VC, RM) and a LIC property_device over a time interval. Constraints/logic enforced by DB triggers.",
        display_template:
          "{{mesh_property_device.device.identifier}} → {{lic_property_device.device.identifier}} ({{start_date}} → {{end_date}})",
        hidden: false,
        singleton: false,
        translations: null,
        archive_field: null,
        archive_app_filter: true,
        archive_value: null,
        unarchive_value: null,
        sort_field: null,
        accountability: "all",
        color: null,
        item_duplication_fields: null,
        sort: null,
        group: null,
        collapse: "open",
        preview_url: null,
        versioning: false,
      },
    ]);

    // 2) Add mesh_device_mapping fields
    await tx.batchInsert("directus_fields", [
      // id
      {
        collection: "mesh_device_mapping",
        field: "id",
        special: "uuid",
        interface: "input",
        options: null,
        display: "raw",
        display_options: null,
        readonly: true,
        hidden: true,
        sort: 1,
        width: "full",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      // mesh_property_device (M2O -> property_device)
      {
        collection: "mesh_device_mapping",
        field: "mesh_property_device",
        special: null,
        interface: "select-dropdown-m2o",
        options: JSON.stringify({
          // optional UI filter hinting mesh models; DB trigger already enforces allowed models
          filter: {
            device: {
              model: { _in: ["WPC-PL10", "WPC-PL50", "VC", "RM"] },
            },
          },
        }),
        display: "related-values",
        display_options: JSON.stringify({
          template:
            "{{device.identifier}} ({{device.model}}) @ {{property.name}}",
        }),
        readonly: false,
        hidden: false,
        sort: 2,
        width: "half",
        translations: null,
        note: "Mesh property_device. Must be active at association time and in allowed models. Same property as LIC property_device.",
        conditions: null,
        required: true,
        group: null,
        validation: null,
        validation_message: null,
      },
      // lic_property_device (M2O -> property_device)
      {
        collection: "mesh_device_mapping",
        field: "lic_property_device",
        special: null,
        interface: "select-dropdown-m2o",
        options: JSON.stringify({
          filter: {
            device: { model: { _eq: "LIC" } },
          },
        }),
        display: "related-values",
        display_options: JSON.stringify({
          template:
            "{{device.identifier}} ({{device.model}}) @ {{property.name}}",
        }),
        readonly: false,
        hidden: false,
        sort: 3,
        width: "half",
        translations: null,
        note: "LIC property_device. Must be active at association time. Must be same property as mesh property_device.",
        conditions: null,
        required: true,
        group: null,
        validation: null,
        validation_message: null,
      },
      // start_date
      {
        collection: "mesh_device_mapping",
        field: "start_date",
        special: null,
        interface: "datetime",
        options: null,
        display: "datetime",
        display_options: JSON.stringify({ relative: true }),
        readonly: false,
        hidden: false,
        sort: 4,
        width: "half",
        translations: null,
        note: "Association start. Overlap accommodation is enforced by DB trigger.",
        conditions: null,
        required: true,
        group: null,
        validation: null,
        validation_message: null,
      },
      // end_date
      {
        collection: "mesh_device_mapping",
        field: "end_date",
        special: null,
        interface: "datetime",
        options: JSON.stringify({
          allowNull: true,
        }),
        display: "datetime",
        display_options: JSON.stringify({ relative: true }),
        readonly: false,
        hidden: false,
        sort: 5,
        width: "half",
        translations: null,
        note: "Optional end. Null means open/infinite interval.",
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      // system fields
      {
        collection: "mesh_device_mapping",
        field: "date_created",
        special: "date-created",
        interface: "datetime",
        options: null,
        display: "datetime",
        display_options: JSON.stringify({ relative: true }),
        readonly: true,
        hidden: true,
        sort: 98,
        width: "half",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      {
        collection: "mesh_device_mapping",
        field: "user_created",
        special: null,
        interface: "select-dropdown-m2o",
        options: null,
        display: "user",
        display_options: null,
        readonly: true,
        hidden: true,
        sort: 99,
        width: "half",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      {
        collection: "mesh_device_mapping",
        field: "date_updated",
        special: "date-updated",
        interface: "datetime",
        options: null,
        display: "datetime",
        display_options: JSON.stringify({ relative: true }),
        readonly: true,
        hidden: true,
        sort: 100,
        width: "half",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      {
        collection: "mesh_device_mapping",
        field: "user_updated",
        special: "user-updated",
        interface: "select-dropdown-m2o",
        options: null,
        display: "user",
        display_options: null,
        readonly: true,
        hidden: true,
        sort: 101,
        width: "half",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
    ]);

    // 3) Relations for mesh_device_mapping -> property_device
    await tx.batchInsert("directus_relations", [
      {
        many_collection: "mesh_device_mapping",
        many_field: "mesh_property_device",
        one_collection: "property_device",
        one_field: null,
        one_collection_field: null,
        one_allowed_collections: null,
        junction_field: null,
        sort_field: null,
        one_deselect_action: "nullify",
      },
      {
        many_collection: "mesh_device_mapping",
        many_field: "lic_property_device",
        one_collection: "property_device",
        one_field: null,
        one_collection_field: null,
        one_allowed_collections: null,
        junction_field: null,
        sort_field: null,
        one_deselect_action: "nullify",
      },
    ]);

    // 4) Add current_mesh_device_mapping to property_device
    await tx.batchInsert("directus_fields", [
      {
        collection: "property_device",
        field: "current_mesh_device_mapping",
        special: null,
        interface: "select-dropdown-m2o",
        options: null,
        display: "related-values",
        display_options: JSON.stringify({
          template:
            "{{mesh_property_device.device.identifier}} → {{lic_property_device.device.identifier}}",
        }),
        readonly: true, // set readonly since DB function maintains it
        hidden: false,
        sort: 200,
        width: "full",
        translations: null,
        note: "Active mapping at the current time. Maintained by DB function im_update_current_mesh_device_mapping().",
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
    ]);

    // 5) Relation property_device.current_mesh_device_mapping -> mesh_device_mapping
    await tx.batchInsert("directus_relations", [
      {
        many_collection: "property_device",
        many_field: "current_mesh_device_mapping",
        one_collection: "mesh_device_mapping",
        one_field: null,
        one_collection_field: null,
        one_allowed_collections: null,
        junction_field: null,
        sort_field: null,
        one_deselect_action: "nullify",
      },
    ]);

    // 6) Permissions
    // Reusing the same policy UUID used in other migrations
    const policyId = "b6e0a767-dc9a-478d-b588-61aa700a519a";

    // READ permission for mesh_device_mapping - aligned with property-level scoping
    await tx.batchInsert("directus_permissions", [
      {
        collection: "mesh_device_mapping",
        action: "read",
        permissions: {
          _and: [
            {
              mesh_property_device: {
                property: {
                  account: { users: { user: { _eq: "$CURRENT_USER" } } },
                },
              },
            },
            {
              mesh_property_device: {
                property: { account: { users: { role: { _eq: "admin" } } } },
              },
            },
          ],
        },
        validation: null,
        presets: null,
        fields: "*",
        policy: policyId,
      },
    ]);

    // CREATE permission for mesh_device_mapping
    await tx.batchInsert("directus_permissions", [
      {
        collection: "mesh_device_mapping",
        action: "create",
        permissions: null,
        validation: null,
        presets: null,
        fields: "mesh_property_device,lic_property_device,start_date,end_date",
        policy: policyId,
      },
    ]);

    // UPDATE permission for mesh_device_mapping
    await tx.batchInsert("directus_permissions", [
      {
        collection: "mesh_device_mapping",
        action: "update",
        permissions: {
          _and: [
            {
              mesh_property_device: {
                property: {
                  account: { users: { user: { _eq: "$CURRENT_USER" } } },
                },
              },
            },
            {
              mesh_property_device: {
                property: { account: { users: { role: { _eq: "admin" } } } },
              },
            },
          ],
        },
        validation: null,
        presets: null,
        fields: "mesh_property_device,lic_property_device,start_date,end_date",
        policy: policyId,
      },
    ]);

    // DELETE permission for mesh_device_mapping
    await tx.batchInsert("directus_permissions", [
      {
        collection: "mesh_device_mapping",
        action: "delete",
        permissions: {
          _and: [
            {
              mesh_property_device: {
                property: {
                  account: { users: { user: { _eq: "$CURRENT_USER" } } },
                },
              },
            },
            {
              mesh_property_device: {
                property: { account: { users: { role: { _eq: "admin" } } } },
              },
            },
          ],
        },
        validation: null,
        presets: null,
        fields: null,
        policy: policyId,
      },
    ]);

    // 7) Update property_device read permission to include current_mesh_device_mapping field if such permission exists
    // In case multiple policies exist in future, we target the known policyId.
    const existing = await tx("directus_permissions")
      .select("*")
      .where({
        collection: "property_device",
        action: "read",
        policy: policyId,
      })
      .first();

    if (existing) {
      const fields =
        existing.fields === "*" || existing.fields == null
          ? "*"
          : String(existing.fields);

      if (fields !== "*") {
        // Ensure field is included
        const setFields = new Set(
          fields
            .split(",")
            .map((f) => f.trim())
            .filter((f) => !!f)
        );
        setFields.add("current_mesh_device_mapping");
        await tx("directus_permissions")
          .where({ id: existing.id })
          .update({ fields: Array.from(setFields).join(",") });
      }
    }
  });
}

/**
 * Reverts the changes made by the up migration.
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    const policyId = "b6e0a767-dc9a-478d-b588-61aa700a519a";

    // Remove permissions
    await tx("directus_permissions")
      .where({
        collection: "mesh_device_mapping",
        action: "read",
        policy: policyId,
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "mesh_device_mapping",
        action: "create",
        policy: policyId,
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "mesh_device_mapping",
        action: "update",
        policy: policyId,
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "mesh_device_mapping",
        action: "delete",
        policy: policyId,
      })
      .del();

    // Remove relation property_device.current_mesh_device_mapping
    await tx("directus_relations")
      .where({
        many_collection: "property_device",
        many_field: "current_mesh_device_mapping",
        one_collection: "mesh_device_mapping",
      })
      .del();

    // Remove field from property_device
    await tx("directus_fields")
      .where({
        collection: "property_device",
        field: "current_mesh_device_mapping",
      })
      .del();

    // Remove relations from mesh_device_mapping
    await tx("directus_relations")
      .where({
        many_collection: "mesh_device_mapping",
        many_field: "mesh_property_device",
      })
      .del();
    await tx("directus_relations")
      .where({
        many_collection: "mesh_device_mapping",
        many_field: "lic_property_device",
      })
      .del();

    // Remove fields for mesh_device_mapping
    await tx("directus_fields")
      .where({ collection: "mesh_device_mapping" })
      .del();

    // Remove collection
    await tx("directus_collections")
      .where({ collection: "mesh_device_mapping" })
      .del();

    // Optional: attempt to revert property_device read permission field list by removing current_mesh_device_mapping
    const existing = await tx("directus_permissions")
      .select("*")
      .where({
        collection: "property_device",
        action: "read",
        policy: policyId,
      })
      .first();

    if (existing && existing.fields && existing.fields !== "*") {
      const parts = String(existing.fields)
        .split(",")
        .map((f) => f.trim())
        .filter((f) => !!f && f !== "current_mesh_device_mapping");
      await tx("directus_permissions")
        .where({ id: existing.id })
        .update({
          fields: parts.length ? parts.join(",") : "*",
        });
    }
  });
}
