/**
 * Migration to create the property_device association table.
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    await tx.schema.createTable("property_device", (table) => {
      table
        .uuid("id")
        .primary()
        .notNullable()
        .defaultTo(tx.raw("gen_random_uuid()"));
      table
        .uuid("device")
        .notNullable()
        .references("id")
        .inTable("device")
        .onDelete("RESTRICT")
        .onUpdate("RESTRICT");
      table
        .uuid("property")
        .notNullable()
        .references("id")
        .inTable("property")
        .onDelete("RESTRICT")
        .onUpdate("RESTRICT");
      table.timestamp("start_date", { useTz: true }).notNullable();
      table.timestamp("end_date", { useTz: true }).nullable();
      table
        .timestamp("date_created", { useTz: true })
        .notNullable()
        .defaultTo(tx.fn.now());
      table
        .uuid("user_created")
        .nullable()
        .references("id")
        .inTable("directus_users")
        .onDelete("RESTRICT")
        .onUpdate("RESTRICT");
      table
        .timestamp("date_updated", { useTz: true })
        .notNullable()
        .defaultTo(tx.fn.now());
      table
        .uuid("user_updated")
        .nullable()
        .references("id")
        .inTable("directus_users")
        .onDelete("RESTRICT")
        .onUpdate("RESTRICT");
      table.jsonb("metadata").nullable();
      table.text("notes").nullable();

      table.index("property");
      table.index("device");
    });

    // Add exclusion constraint to prevent overlapping device associations
    await tx.raw(`
      ALTER TABLE property_device
      ADD CONSTRAINT property_device_no_overlap
      EXCLUDE USING gist (
        device WITH =,
        tstzrange(start_date, end_date) WITH &&
      );
    `);

    await tx.raw(`
      CREATE TRIGGER set_property_device_date_updated
      BEFORE UPDATE ON property_device
      FOR EACH ROW
      EXECUTE FUNCTION update_timestamp_column();
    `);
  });
}

/**
 * Drops the property_device association table.
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    await tx.raw(
      "DROP TRIGGER IF EXISTS set_property_device_date_updated ON property_device;"
    );
    await tx.schema.dropTableIfExists("property_device");
  });
}
