/**
 * Applies the current state of the Directus config to the database.
 * This migration file is auto-generated and should not be manually edited.
 * @module directus/migration/gen
 * @version 2025-07-05T04:05:31.904Z
 * @description This migration applies the current state of the Directus config to the database.
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    // Insert 9 records into directus_permissions
    await tx.batchInsert("directus_permissions", [
      {
        collection: "account_user",
        action: "update",
        permissions: {
          _and: [
            { user: { _eq: "$CURRENT_USER" } },
            { role: { _eq: "admin" } },
            {
              _or: [
                { start_date: { _null: true } },
                { start_date: { _lte: "$NOW" } },
              ],
            },
            {
              _or: [
                { end_date: { _null: true } },
                { end_date: { _gt: "$NOW" } },
              ],
            },
          ],
        },
        validation: null,
        presets: null,
        fields: "account,user,role,start_date,end_date,notes,metadata",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      },
      {
        collection: "irrigation_plan",
        action: "update",
        permissions: {
          _and: [
            {
              project: {
                property: {
                  account: { users: { user: { _eq: "$CURRENT_USER" } } },
                },
              },
            },
            {
              project: {
                property: { account: { users: { role: { _eq: "admin" } } } },
              },
            },
            {
              _or: [
                {
                  project: {
                    property: {
                      account: { users: { start_date: { _null: true } } },
                    },
                  },
                },
                {
                  project: {
                    property: {
                      account: { users: { start_date: { _lte: "$NOW" } } },
                    },
                  },
                },
              ],
            },
            {
              _or: [
                {
                  project: {
                    property: {
                      account: { users: { end_date: { _null: true } } },
                    },
                  },
                },
                {
                  project: {
                    property: {
                      account: { users: { end_date: { _gt: "$NOW" } } },
                    },
                  },
                },
              ],
            },
          ],
        },
        validation: null,
        presets: null,
        fields:
          "name,is_enabled,start_time,days_of_week,start_date,end_date,project,steps,description,metadata,notes",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      },
      {
        collection: "irrigation_plan_step",
        action: "update",
        permissions: {
          _and: [
            {
              irrigation_plan: {
                project: {
                  property: {
                    account: { users: { user: { _eq: "$CURRENT_USER" } } },
                  },
                },
              },
            },
            {
              irrigation_plan: {
                project: {
                  property: { account: { users: { role: { _eq: "admin" } } } },
                },
              },
            },
            {
              _or: [
                {
                  irrigation_plan: {
                    project: {
                      property: {
                        account: { users: { start_date: { _null: true } } },
                      },
                    },
                  },
                },
                {
                  irrigation_plan: {
                    project: {
                      property: {
                        account: { users: { start_date: { _lte: "$NOW" } } },
                      },
                    },
                  },
                },
              ],
            },
            {
              _or: [
                {
                  irrigation_plan: {
                    project: {
                      property: {
                        account: { users: { end_date: { _null: true } } },
                      },
                    },
                  },
                },
                {
                  irrigation_plan: {
                    project: {
                      property: {
                        account: { users: { end_date: { _gt: "$NOW" } } },
                      },
                    },
                  },
                },
              ],
            },
          ],
        },
        validation: null,
        presets: null,
        fields:
          "irrigation_plan,sector,description,order,duration_seconds,delay_seconds_after,metadata,notes",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      },
      {
        collection: "project",
        action: "update",
        permissions: {
          _and: [
            {
              property: {
                account: { users: { user: { _eq: "$CURRENT_USER" } } },
              },
            },
            { property: { account: { users: { role: { _eq: "admin" } } } } },
            {
              _or: [
                {
                  property: {
                    account: { users: { start_date: { _null: true } } },
                  },
                },
                {
                  property: {
                    account: { users: { start_date: { _lte: "$NOW" } } },
                  },
                },
              ],
            },
            {
              _or: [
                {
                  property: {
                    account: { users: { end_date: { _null: true } } },
                  },
                },
                {
                  property: {
                    account: { users: { end_date: { _gt: "$NOW" } } },
                  },
                },
              ],
            },
          ],
        },
        validation: null,
        presets: null,
        fields:
          "property,name,localized_irrigation_controller,start_date,end_date,irrigation_water_pump,fertigation_water_pump,sectors,irrigation_plans,description,metadata,notes",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      },
      {
        collection: "property",
        action: "update",
        permissions: {
          _and: [
            { account: { users: { user: { _eq: "$CURRENT_USER" } } } },
            { account: { users: { role: { _eq: "admin" } } } },
            {
              _or: [
                { account: { users: { start_date: { _null: true } } } },
                { account: { users: { start_date: { _lte: "$NOW" } } } },
              ],
            },
            {
              _or: [
                { account: { users: { end_date: { _null: true } } } },
                { account: { users: { end_date: { _gt: "$NOW" } } } },
              ],
            },
          ],
        },
        validation: null,
        presets: null,
        fields:
          "account,name,timezone,point,address_postal_code,address_street_name,address_street_number,address_complement,address_neighborhood,address_city,address_state,address_country,notes,projects,devices,water_pumps,metadata,backwash_duration_minutes,backwash_period_minutes,backwash_delay_seconds,rain_gauge_enabled,rain_gauge_resolution_mm,precipitation_volume_limit_mm,precipitation_suspended_duration_hours",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      },
      {
        collection: "sector",
        action: "update",
        permissions: {
          _and: [
            {
              project: {
                property: {
                  account: { users: { user: { _eq: "$CURRENT_USER" } } },
                },
              },
            },
            {
              project: {
                property: { account: { users: { role: { _eq: "admin" } } } },
              },
            },
            {
              _or: [
                {
                  project: {
                    property: {
                      account: { users: { start_date: { _null: true } } },
                    },
                  },
                },
                {
                  project: {
                    property: {
                      account: { users: { start_date: { _lte: "$NOW" } } },
                    },
                  },
                },
              ],
            },
            {
              _or: [
                {
                  project: {
                    property: {
                      account: { users: { end_date: { _null: true } } },
                    },
                  },
                },
                {
                  project: {
                    property: {
                      account: { users: { end_date: { _gt: "$NOW" } } },
                    },
                  },
                },
              ],
            },
          ],
        },
        validation: null,
        presets: null,
        fields:
          "project,name,valve_controller,valve_controller_output,area,polygon,steps,description,metadata,notes",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      },
      {
        collection: "water_pump",
        action: "update",
        permissions: {
          _and: [
            {
              property: {
                account: { users: { user: { _eq: "$CURRENT_USER" } } },
              },
            },
            { property: { account: { users: { role: { _eq: "admin" } } } } },
            {
              _or: [
                {
                  property: {
                    account: { users: { start_date: { _null: true } } },
                  },
                },
                {
                  property: {
                    account: { users: { start_date: { _lte: "$NOW" } } },
                  },
                },
              ],
            },
            {
              _or: [
                {
                  property: {
                    account: { users: { end_date: { _null: true } } },
                  },
                },
                {
                  property: {
                    account: { users: { end_date: { _gt: "$NOW" } } },
                  },
                },
              ],
            },
          ],
        },
        validation: null,
        presets: null,
        fields:
          "identifier,label,pump_type,pump_model,has_frequency_inverter,monitor_operation,property,water_pump_controller,notes,metadata",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      },
      {
        collection: "reservoir",
        action: "update",
        permissions: {
          _and: [
            {
              property: {
                account: { users: { user: { _eq: "$CURRENT_USER" } } },
              },
            },
            { property: { account: { users: { role: { _eq: "admin" } } } } },
            {
              _or: [
                {
                  property: {
                    account: { users: { start_date: { _null: true } } },
                  },
                },
                {
                  property: {
                    account: { users: { start_date: { _lte: "$NOW" } } },
                  },
                },
              ],
            },
            {
              _or: [
                {
                  property: {
                    account: { users: { end_date: { _null: true } } },
                  },
                },
                {
                  property: {
                    account: { users: { end_date: { _gt: "$NOW" } } },
                  },
                },
              ],
            },
          ],
        },
        validation: null,
        presets: null,
        fields:
          "property,name,reservoir_monitor,water_pump,description,capacity,location,enabled,notes,metadata",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      },
      {
        collection: "device",
        action: "update",
        permissions: {
          _and: [
            {
              properties: {
                property: {
                  account: { users: { user: { _eq: "$CURRENT_USER" } } },
                },
              },
            },
            {
              properties: {
                property: { account: { users: { role: { _eq: "admin" } } } },
              },
            },
            {
              _or: [
                {
                  properties: {
                    property: {
                      account: { users: { start_date: { _null: true } } },
                    },
                  },
                },
                {
                  properties: {
                    property: {
                      account: { users: { start_date: { _lte: "$NOW" } } },
                    },
                  },
                },
              ],
            },
            {
              _or: [
                {
                  properties: {
                    property: {
                      account: { users: { end_date: { _null: true } } },
                    },
                  },
                },
                {
                  properties: {
                    property: {
                      account: { users: { end_date: { _gt: "$NOW" } } },
                    },
                  },
                },
              ],
            },
            {
              _or: [
                { properties: { start_date: { _null: true } } },
                { properties: { start_date: { _lte: "$NOW" } } },
              ],
            },
            {
              _or: [
                { properties: { end_date: { _null: true } } },
                { properties: { end_date: { _gt: "$NOW" } } },
              ],
            },
          ],
        },
        validation: null,
        presets: null,
        fields: "identifier,model,metadata,notes",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      },
      {
        collection: "property_device",
        action: "update",
        permissions: {
          _and: [
            {
              property: {
                account: { users: { user: { _eq: "$CURRENT_USER" } } },
              },
            },
            { property: { account: { users: { role: { _eq: "admin" } } } } },
            {
              _or: [
                {
                  property: {
                    account: { users: { start_date: { _null: true } } },
                  },
                },
                {
                  property: {
                    account: { users: { start_date: { _lte: "$NOW" } } },
                  },
                },
              ],
            },
            {
              _or: [
                {
                  property: {
                    account: { users: { end_date: { _null: true } } },
                  },
                },
                {
                  property: {
                    account: { users: { end_date: { _gt: "$NOW" } } },
                  },
                },
              ],
            },
            {
              _or: [
                { start_date: { _null: true } },
                { start_date: { _lte: "$NOW" } },
              ],
            },
            {
              _or: [
                { end_date: { _null: true } },
                { end_date: { _gt: "$NOW" } },
              ],
            },
          ],
        },
        validation: null,
        presets: null,
        fields: "start_date,end_date,property,device,metadata,notes",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      },
    ]);
  });
}

/**
 * Reverts the changes made by the up migration.
 * This migration file is auto-generated and should not be manually edited.
 * @module directus/migration/gen
 * @version 2025-07-05T04:05:31.904Z
 * @description This migration reverts the changes made by the up migration.
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    // Delete 9 records from directus_permissions (reverse of insert)
    await tx("directus_permissions")
      .where({
        collection: "device",
        action: "update",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "property_device",
        action: "update",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "account_user",
        action: "update",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "irrigation_plan",
        action: "update",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "irrigation_plan_step",
        action: "update",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "project",
        action: "update",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "property",
        action: "update",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "sector",
        action: "update",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "water_pump",
        action: "update",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "reservoir",
        action: "update",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
  });
}
