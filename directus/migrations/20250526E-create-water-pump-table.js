/**
 *
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    await tx.schema.createTable("water_pump", (table) => {
      table
        .uuid("id")
        .primary()
        .notNullable()
        .defaultTo(tx.raw("gen_random_uuid()"));
      table
        .uuid("property")
        .notNullable()
        .references("id")
        .inTable("property")
        .onDelete("RESTRICT")
        .onUpdate("RESTRICT");
      table
        .uuid("water_pump_controller")
        .nullable()
        .references("id")
        .inTable("device")
        .onDelete("RESTRICT")
        .onUpdate("RESTRICT");
      table.string("label", 255).notNullable();
      table.string("identifier", 255).notNullable();
      table
        .string("pump_type", 255)
        .notNullable()
        .checkIn(
          ["IRRIGATION", "FERTIGATION", "SERVICE"],
          "water_pump_type_check"
        );
      table.string("pump_model", 255).nullable();
      table.boolean("has_frequency_inverter").notNullable().defaultTo(false);
      table.boolean("monitor_operation").notNullable().defaultTo(false);
      table.text("notes").nullable();
      table.jsonb("metadata").nullable();
      table
        .timestamp("date_created", { useTz: true })
        .notNullable()
        .defaultTo(tx.fn.now());
      table
        .uuid("user_created")
        .nullable()
        .references("id")
        .inTable("directus_users")
        .onDelete("RESTRICT")
        .onUpdate("RESTRICT");
      table
        .timestamp("date_updated", { useTz: true })
        .notNullable()
        .defaultTo(tx.fn.now());
      table
        .uuid("user_updated")
        .nullable()
        .references("id")
        .inTable("directus_users")
        .onDelete("RESTRICT")
        .onUpdate("RESTRICT");
      table.unique(["property", "identifier"]);
      table.unique(["property", "label"]);
    });

    await tx.raw(`
      CREATE TRIGGER set_water_pump_date_updated
      BEFORE UPDATE ON water_pump
      FOR EACH ROW
      EXECUTE FUNCTION update_timestamp_column();
    `);
  });
}

/**
 *
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    await tx.schema.dropTableIfExists("water_pump");
  });
}
