/**
 * Applies the current state of the Directus config to the database.
 * This migration file is auto-generated and should not be manually edited.
 * @module directus/migration/gen
 * @version 2025-07-29T19:11:28.414Z
 * @description This migration applies the current state of the Directus config to the database.
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  // if (true) return; // Skip migration if already applied
  await knex.transaction(async (tx) => {
    // Insert 1 records into directus_fields
    await tx.batchInsert("directus_fields", [
      {
        collection: "water_pump",
        field: "flow_rate_lh",
        special: null,
        interface: "input",
        options: null,
        display: "raw",
        display_options: null,
        readonly: false,
        hidden: false,
        sort: 12,
        width: "full",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
    ]);

    // Update 11 records in directus_fields
    await tx("directus_fields")
      .where({ collection: "water_pump", field: "property" })
      .update({ sort: 7 });
    await tx("directus_fields")
      .where({ collection: "water_pump", field: "water_pump_controller" })
      .update({ sort: 8 });
    await tx("directus_fields")
      .where({ collection: "water_pump", field: "monitor_operation" })
      .update({ sort: 9 });
    await tx("directus_fields")
      .where({ collection: "water_pump", field: "date_updated" })
      .update({ sort: 15 });
    await tx("directus_fields")
      .where({ collection: "water_pump", field: "user_updated" })
      .update({ sort: 16 });
    await tx("directus_fields")
      .where({ collection: "water_pump", field: "notes" })
      .update({ sort: 17 });
    await tx("directus_fields")
      .where({ collection: "water_pump", field: "metadata" })
      .update({ sort: 18 });
    await tx("directus_fields")
      .where({ collection: "water_pump", field: "irrigation_projects" })
      .update({ sort: 10 });
    await tx("directus_fields")
      .where({ collection: "water_pump", field: "fertigation_projects" })
      .update({ sort: 11 });
    await tx("directus_fields")
      .where({ collection: "water_pump", field: "date_created" })
      .update({ sort: 13 });
    await tx("directus_fields")
      .where({ collection: "water_pump", field: "user_created" })
      .update({ sort: 14 });

    // Update 2 records in directus_permissions
    await tx("directus_permissions")
      .where({
        collection: "water_pump",
        action: "create",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        fields:
          "identifier,label,pump_type,pump_model,has_frequency_inverter,monitor_operation,property,water_pump_controller,notes,metadata,flow_rate_lh",
      });
    await tx("directus_permissions")
      .where({
        collection: "water_pump",
        action: "update",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        fields:
          "identifier,label,pump_type,pump_model,has_frequency_inverter,monitor_operation,property,water_pump_controller,notes,metadata,flow_rate_lh",
      });
  });
}

/**
 * Reverts the changes made by the up migration.
 * This migration file is auto-generated and should not be manually edited.
 * @module directus/migration/gen
 * @version 2025-07-29T19:11:28.414Z
 * @description This migration reverts the changes made by the up migration.
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    // Revert 11 updates in directus_fields
    await tx("directus_fields")
      .where({ collection: "water_pump", field: "property" })
      .update({
        collection: "water_pump",
        field: "property",
        special: null,
        interface: "select-dropdown-m2o",
        options: null,
        display: "related-values",
        display_options: null,
        readonly: false,
        hidden: false,
        sort: 6,
        width: "half",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      });
    await tx("directus_fields")
      .where({ collection: "water_pump", field: "water_pump_controller" })
      .update({
        collection: "water_pump",
        field: "water_pump_controller",
        special: null,
        interface: "select-dropdown-m2o",
        options: null,
        display: "related-values",
        display_options: null,
        readonly: false,
        hidden: false,
        sort: 7,
        width: "half",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      });
    await tx("directus_fields")
      .where({ collection: "water_pump", field: "monitor_operation" })
      .update({
        collection: "water_pump",
        field: "monitor_operation",
        special: null,
        interface: "boolean",
        options: { label: "Monitor Operation" },
        display: "boolean",
        display_options: null,
        readonly: false,
        hidden: false,
        sort: 8,
        width: "half",
        translations: null,
        note: "Indicates whether the water pump controller should monitor this pump's operation status",
        conditions: null,
        required: true,
        group: null,
        validation: null,
        validation_message: null,
      });
    await tx("directus_fields")
      .where({ collection: "water_pump", field: "date_updated" })
      .update({
        collection: "water_pump",
        field: "date_updated",
        special: "date-updated",
        interface: "datetime",
        options: null,
        display: "datetime",
        display_options: null,
        readonly: false,
        hidden: false,
        sort: 12,
        width: "half",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      });
    await tx("directus_fields")
      .where({ collection: "water_pump", field: "user_updated" })
      .update({
        collection: "water_pump",
        field: "user_updated",
        special: "user-updated",
        interface: "select-dropdown-m2o",
        options: null,
        display: "user",
        display_options: null,
        readonly: false,
        hidden: false,
        sort: 13,
        width: "half",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      });
    await tx("directus_fields")
      .where({ collection: "water_pump", field: "notes" })
      .update({
        collection: "water_pump",
        field: "notes",
        special: null,
        interface: null,
        options: null,
        display: null,
        display_options: null,
        readonly: false,
        hidden: false,
        sort: 14,
        width: "full",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      });
    await tx("directus_fields")
      .where({ collection: "water_pump", field: "metadata" })
      .update({
        collection: "water_pump",
        field: "metadata",
        special: "cast-json",
        interface: null,
        options: null,
        display: null,
        display_options: null,
        readonly: false,
        hidden: false,
        sort: 15,
        width: "full",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      });
    await tx("directus_fields")
      .where({ collection: "water_pump", field: "irrigation_projects" })
      .update({
        collection: "water_pump",
        field: "irrigation_projects",
        special: "o2m",
        interface: "list-o2m",
        options: null,
        display: "related-values",
        display_options: null,
        readonly: false,
        hidden: false,
        sort: 8,
        width: "half",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      });
    await tx("directus_fields")
      .where({ collection: "water_pump", field: "fertigation_projects" })
      .update({
        collection: "water_pump",
        field: "fertigation_projects",
        special: "o2m",
        interface: "list-o2m",
        options: null,
        display: "related-values",
        display_options: null,
        readonly: false,
        hidden: false,
        sort: 9,
        width: "half",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      });
    await tx("directus_fields")
      .where({ collection: "water_pump", field: "date_created" })
      .update({
        collection: "water_pump",
        field: "date_created",
        special: "date-created",
        interface: "datetime",
        options: null,
        display: "datetime",
        display_options: null,
        readonly: false,
        hidden: false,
        sort: 10,
        width: "half",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      });
    await tx("directus_fields")
      .where({ collection: "water_pump", field: "user_created" })
      .update({
        collection: "water_pump",
        field: "user_created",
        special: null,
        interface: "select-dropdown-m2o",
        options: null,
        display: "user",
        display_options: null,
        readonly: false,
        hidden: false,
        sort: 11,
        width: "half",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      });

    // Revert 2 updates in directus_permissions
    await tx("directus_permissions")
      .where({
        collection: "water_pump",
        action: "create",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        collection: "water_pump",
        action: "create",
        permissions: null,
        validation: null,
        presets: null,
        fields:
          "identifier,label,pump_type,pump_model,has_frequency_inverter,monitor_operation,property,water_pump_controller,notes,metadata",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      });
    await tx("directus_permissions")
      .where({
        collection: "water_pump",
        action: "update",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        collection: "water_pump",
        action: "update",
        permissions: {
          _and: [
            {
              property: {
                account: { users: { user: { _eq: "$CURRENT_USER" } } },
              },
            },
            { property: { account: { users: { role: { _eq: "admin" } } } } },
            {
              _or: [
                {
                  property: {
                    account: { users: { start_date: { _null: true } } },
                  },
                },
                {
                  property: {
                    account: { users: { start_date: { _lte: "$NOW" } } },
                  },
                },
              ],
            },
            {
              _or: [
                {
                  property: {
                    account: { users: { end_date: { _null: true } } },
                  },
                },
                {
                  property: {
                    account: { users: { end_date: { _gt: "$NOW" } } },
                  },
                },
              ],
            },
          ],
        },
        validation: null,
        presets: null,
        fields:
          "identifier,label,pump_type,pump_model,has_frequency_inverter,monitor_operation,property,water_pump_controller,notes,metadata",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      });

    // Delete 1 records from directus_fields (reverse of insert)
    await tx("directus_fields")
      .where({ collection: "water_pump", field: "flow_rate_lh" })
      .del();
  });
}
