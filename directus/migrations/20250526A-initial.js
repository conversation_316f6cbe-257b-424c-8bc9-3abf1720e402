/**
 * This migration file is a placeholder for the up migration.
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    await tx.raw(`
      CREATE OR REPLACE FUNCTION update_timestamp_column()
      RETURNS TRIGGER AS $$
      BEGIN
        NEW.date_updated = NOW();
        RETURN NEW;
      END;
      $$ language 'plpgsql';
    `);
  });
}

/**
 * This migration file is a placeholder for the down migration.
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    await tx.raw("DROP FUNCTION IF EXISTS update_timestamp_column();");
  });
}
