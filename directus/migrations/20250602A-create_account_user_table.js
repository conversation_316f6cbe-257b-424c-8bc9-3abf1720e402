/**
 * Migration to create the account_device association table.
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    await tx.schema.createTable("account_user", (table) => {
      table
        .uuid("id")
        .primary()
        .notNullable()
        .defaultTo(tx.raw("gen_random_uuid()"));
      table
        .uuid("account")
        .notNullable()
        .references("id")
        .inTable("account")
        .onDelete("RESTRICT")
        .onUpdate("RESTRICT");
      table
        .uuid("user")
        .notNullable()
        .references("id")
        .inTable("directus_users")
        .onDelete("RESTRICT")
        .onUpdate("RESTRICT");
      table
        .string("role", 255)
        .notNullable()
        .checkIn(["admin", "user", "guest"])
        .defaultTo("admin");
      table.timestamp("start_date", { useTz: true }).notNullable();
      table.timestamp("end_date", { useTz: true }).nullable();
      table
        .timestamp("date_created", { useTz: true })
        .notNullable()
        .defaultTo(tx.fn.now());
      table
        .uuid("user_created")
        .nullable()
        .references("id")
        .inTable("directus_users")
        .onDelete("RESTRICT")
        .onUpdate("RESTRICT");
      table
        .timestamp("date_updated", { useTz: true })
        .notNullable()
        .defaultTo(tx.fn.now());
      table
        .uuid("user_updated")
        .nullable()
        .references("id")
        .inTable("directus_users")
        .onDelete("RESTRICT")
        .onUpdate("RESTRICT");
      table.jsonb("metadata").nullable();
      table.text("notes").nullable();

      table.index("account");
      table.index("user");
      table.unique(["account", "user"], {
        name: "account_user_unq",
      });
    });

    await tx.raw(`
      CREATE TRIGGER set_account_user_date_updated
      BEFORE UPDATE ON account_user
      FOR EACH ROW
      EXECUTE FUNCTION update_timestamp_column();
    `);
  });
}

/**
 * Drops the account_user association table.
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    await tx.raw(
      "DROP TRIGGER IF EXISTS set_account_user_date_updated ON account_user;"
    );
    await tx.schema.dropTableIfExists("account_user");
  });
}
