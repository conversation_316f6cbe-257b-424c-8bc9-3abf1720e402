/**
 * Applies the current state of the Directus config to the database.
 * This migration file is auto-generated and should not be manually edited.
 * @module directus/migration/gen
 * @version 2025-07-04T13:50:59.587Z
 * @description This migration applies the current state of the Directus config to the database.
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  // if (true) return; // Skip migration if already applied
  await knex.transaction(async (tx) => {
    // Insert 1 records into directus_flows
    await tx.batchInsert("directus_flows", [
      {
        id: "6ad7b8b1-8fcc-40d0-a851-a96aa29b63a4",
        name: "User Creation",
        icon: "bolt",
        color: null,
        description: null,
        status: "active",
        trigger: "event",
        accountability: "all",
        options: {
          type: "filter",
          scope: ["items.create"],
          return: "$last",
          collections: ["directus_users"],
        },
        operation: "3d8ded9a-25ef-4889-811c-3915f1103194",
        date_created: "2025-07-02T14:54:36.302+00:00",
        user_created: null,
      },
    ]);

    // Insert 1 records into directus_operations
    await tx.batchInsert("directus_operations", [
      {
        id: "3d8ded9a-25ef-4889-811c-3915f1103194",
        name: "Run Script",
        key: "exec_sjpxy",
        type: "exec",
        position_x: 23,
        position_y: 1,
        options: {
          code: "module.exports = async function(data) {\n\tdata.$trigger.payload.account = {\n        create: [\n            {\n            }\n        ]\n    };\n\treturn data.$trigger.payload;\n}",
        },
        resolve: null,
        reject: null,
        flow: "6ad7b8b1-8fcc-40d0-a851-a96aa29b63a4",
        date_created: "2025-07-02T16:02:19.842+00:00",
        user_created: null,
      },
    ]);
  });
}

/**
 * Reverts the changes made by the up migration.
 * This migration file is auto-generated and should not be manually edited.
 * @module directus/migration/gen
 * @version 2025-07-04T13:50:59.587Z
 * @description This migration reverts the changes made by the up migration.
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    // Delete 1 records from directus_operations (reverse of insert)
    await tx("directus_operations")
      .where({ id: "3d8ded9a-25ef-4889-811c-3915f1103194" })
      .del();

    // Delete 1 records from directus_flows (reverse of insert)
    await tx("directus_flows")
      .where({ id: "6ad7b8b1-8fcc-40d0-a851-a96aa29b63a4" })
      .del();
  });
}
