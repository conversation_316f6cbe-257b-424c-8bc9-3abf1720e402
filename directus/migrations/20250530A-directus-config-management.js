/**
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    await tx.from("directus_settings").then(async (r) => {
      if (!r || r.length === 0) {
        await tx
          .insert({
            project_name: "Irrigação Localizada",
          })
          .into("directus_settings");
      }
    });

    await tx.schema.createView("directus_data_config_json", (builder) => {
      builder.columns(["config"]);
      builder.as(
        tx.select({
          config: tx.raw(`json_build_object(
	'collections', COALESCE(( SELECT json_agg(to_json(c.*)) AS json_agg FROM directus_collections c), '[]'::json),
	'fields', COALESCE(( SELECT json_agg(to_json(c.*)) AS json_agg FROM directus_fields c), '[]'::json),
	'permissions', COALESCE(( SELECT json_agg(to_json(c.*)) AS json_agg FROM directus_permissions c), '[]'::json),
	'policies', COALESCE(( SELECT json_agg(to_json(c.*)) AS json_agg FROM directus_policies c where c.name <> 'Administrator'), '[]'::json),
	'access', COALESCE(( SELECT json_agg(to_json(c.*)) AS json_agg FROM directus_access c left join directus_policies p on p.id = c."policy" where p.name <> 'Administrator'), '[]'::json),
	'roles', COALESCE(( SELECT json_agg(to_json(c.*)) AS json_agg FROM directus_roles c WHERE c.name::text <> 'Administrator'::text), '[]'::json),
	'relations', COALESCE(( SELECT json_agg(to_json(c.*)) AS json_agg FROM directus_relations c), '[]'::json),
	'translations', COALESCE(( SELECT json_agg(to_json(c.*)) AS json_agg FROM directus_translations c), '[]'::json),
	'flows', COALESCE(( SELECT json_agg(to_json(c.*)) AS json_agg FROM directus_flows c), '[]'::json),
	'operations', COALESCE(( SELECT json_agg(to_json(c.*)) AS json_agg FROM directus_operations c), '[]'::json)
)`),
        })
      );
    });

    await tx.schema.createSchema("directus_migration");
    await tx.schema.createTable(
      "directus_migration.directus_config",
      (builder) => {
        builder.increments("id", { primaryKey: true });
        builder
          .timestamp("date_created", { useTz: true })
          .defaultTo(tx.fn.now());
        builder.json("config").nullable();
        builder.text("name").nullable();
      }
    );
    await tx.schema
      .raw(`CREATE OR REPLACE FUNCTION public.insert_directus_config()
    RETURNS trigger
    LANGUAGE 'plpgsql'
    COST 100
    VOLATILE NOT LEAKPROOF
AS $BODY$
BEGIN
    INSERT INTO directus_migration.directus_config(config, name)
    SELECT config, NEW.version FROM directus_data_config_json;
    RETURN NEW;
END;
$BODY$;`);
    await tx.schema.raw(`CREATE TRIGGER tg_insert_directus_config
    AFTER INSERT
    ON public.directus_migrations
    FOR EACH ROW
    EXECUTE FUNCTION public.insert_directus_config();`);

    await tx.schema
      .raw(`CREATE OR REPLACE FUNCTION public.delete_directus_config()
    RETURNS trigger
    LANGUAGE 'plpgsql'
    COST 100
    VOLATILE NOT LEAKPROOF
AS $BODY$
BEGIN
    RAISE LOG 'REMOVING migration: %', OLD.version;
    DELETE FROM directus_migration.directus_config WHERE name = OLD.version;
    RAISE LOG 'LAST MIGRATION: %', (SELECT version FROM directus_migrations order by timestamp desc limit 1);
    RETURN NEW;
END;
$BODY$;`);
    await tx.schema.raw(`CREATE TRIGGER tg_delete_directus_config
    AFTER DELETE
    ON public.directus_migrations
    FOR EACH ROW
    EXECUTE FUNCTION public.delete_directus_config();`);
  });
}

/**
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    await tx.schema.raw(
      "DROP TRIGGER tg_delete_directus_config ON public.directus_migrations"
    );
    await tx.schema.raw(
      "DROP FUNCTION IF EXISTS public.delete_directus_config()"
    );

    await tx.schema.raw(
      "DROP TRIGGER tg_insert_directus_config ON public.directus_migrations"
    );
    await tx.schema.raw(
      "DROP FUNCTION IF EXISTS public.insert_directus_config()"
    );
    await tx.schema.dropTable("directus_migration.directus_config");
    await tx.schema.dropSchema("directus_migration");
    await tx.schema.dropView("directus_data_config_json");
  });
}
