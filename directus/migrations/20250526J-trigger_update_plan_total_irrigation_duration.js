/**
 * Migration to create a trigger and function that keeps plan.total_irrigation_duration updated.
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    // Create the function to update total_irrigation_duration
    await tx.raw(`
      CREATE OR REPLACE FUNCTION update_plan_total_irrigation_duration()
      RETURNS TRIGGER AS $$
      DECLARE
        affected_plan UUID;
      BEGIN
        IF (TG_OP = 'DELETE') THEN
          affected_plan := OLD.irrigation_plan;
        ELSIF (TG_OP = 'UPDATE' AND NEW.irrigation_plan <> OLD.irrigation_plan) THEN
          -- If plan FK changed, update both old and new
          UPDATE irrigation_plan
            SET total_irrigation_duration = COALESCE((
              SELECT SUM(duration_seconds)
              FROM irrigation_plan_step
              WHERE irrigation_plan = OLD.irrigation_plan
            ), 0)
            WHERE id = OLD.irrigation_plan;
          affected_plan := NEW.irrigation_plan;
        ELSE
          affected_plan := NEW.irrigation_plan;
        END IF;

        UPDATE irrigation_plan
          SET total_irrigation_duration = COALESCE((
            SELECT SUM(duration_seconds)
            FROM irrigation_plan_step
            WHERE irrigation_plan = affected_plan
          ), 0)
          WHERE id = affected_plan;

        RETURN NULL;
      END;
      $$ LANGUAGE plpgsql;
    `);

    // Create the trigger on irrigation_plan_step
    await tx.raw(`
      CREATE TRIGGER trg_update_plan_total_irrigation_duration
      AFTER INSERT OR UPDATE OF duration_seconds, irrigation_plan OR DELETE
      ON irrigation_plan_step
      FOR EACH ROW
      EXECUTE FUNCTION update_plan_total_irrigation_duration();
    `);
  });
}

export async function down(knex) {
  await knex.transaction(async (tx) => {
    await tx.raw(
      "DROP TRIGGER IF EXISTS trg_update_plan_total_irrigation_duration ON irrigation_plan_step;"
    );
    await tx.raw(
      "DROP FUNCTION IF EXISTS update_plan_total_irrigation_duration();"
    );
  });
}
