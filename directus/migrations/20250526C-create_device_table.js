/**
 * This migration file is a placeholder for the up migration.
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    await tx.schema.createTable("device", (table) => {
      table
        .uuid("id")
        .primary()
        .notNullable()
        .defaultTo(tx.raw("gen_random_uuid()"));
      table.string("identifier", 255).notNullable().unique();
      table
        .string("model", 16)
        .notNullable()
        .checkIn(
          ["LIC", "WPC-PL10", "WPC-PL50", "VC", "RM"],
          "device_model_check"
        );
      table
        .timestamp("date_created", { useTz: true })
        .notNullable()
        .defaultTo(tx.fn.now());
      table
        .uuid("user_created")
        .nullable()
        .references("id")
        .inTable("directus_users")
        .onDelete("RESTRICT")
        .onUpdate("RESTRICT");
      table
        .timestamp("date_updated", { useTz: true })
        .notNullable()
        .defaultTo(tx.fn.now());
      table
        .uuid("user_updated")
        .nullable()
        .references("id")
        .inTable("directus_users")
        .onDelete("RESTRICT")
        .onUpdate("RESTRICT");
      table.jsonb("metadata").nullable();
      table.text("notes").nullable();
    });
    await tx.raw(`
      CREATE TRIGGER set_device_date_updated
      BEFORE UPDATE ON device
      FOR EACH ROW
      EXECUTE FUNCTION update_timestamp_column();
    `);
  });
}

/**
 * This migration file is a placeholder for the down migration.
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    await tx.raw("DROP TRIGGER IF EXISTS set_device_date_updated ON device;");
    await tx.schema.dropTableIfExists("device");
  });
}
