/**
 * This migration file is a placeholder for the up migration.
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    await tx.schema.raw("CREATE EXTENSION IF NOT EXISTS btree_gist;");
    await tx.schema.createTable("project", (table) => {
      table
        .uuid("id")
        .primary()
        .notNullable()
        .defaultTo(tx.raw("gen_random_uuid()"))
        .comment("Unique identifier for the project (UUID)");
      table
        .uuid("property")
        .notNullable()
        .references("id")
        .inTable("property")
        .onDelete("RESTRICT")
        .onUpdate("RESTRICT")
        .comment("ID of the property to which this project belongs");
      table.string("name", 255).notNullable().comment("Name of the project");
      table
        .uuid("irrigation_water_pump")
        .notNullable()
        .references("id")
        .inTable("water_pump")
        .onDelete("RESTRICT")
        .onUpdate("RESTRICT")
        .comment("ID of the irrigation water pump used by this project");
      table
        .uuid("fertigation_water_pump")
        .nullable()
        .references("id")
        .inTable("water_pump")
        .onDelete("RESTRICT")
        .onUpdate("RESTRICT")
        .comment(
          "Optional ID of the fertigation water pump used by this project"
        );
      table
        .uuid("localized_irrigation_controller")
        .notNullable()
        .references("id")
        .inTable("device")
        .onDelete("RESTRICT")
        .onUpdate("RESTRICT")
        .comment(
          "ID of the localized irrigation controller responsible for managing all devices within the project"
        );
      table
        .integer("pipe_wash_time_seconds")
        .nullable()
        .comment("Time in minutes to wash pipes after fertigation");
      table
        .integer("backwash_duration_seconds")
        .nullable()
        .comment("Time in minutes for backwashing operations");
      table
        .integer("backwash_period_seconds")
        .nullable()
        .comment("Time in minutes between backwashing operations");
      table
        .text("description")
        .nullable()
        .comment("Optional description of the project");
      table
        .date("start_date")
        .nullable()
        .comment(
          "Start date of the project - projects using the same irrigation water pump cannot have overlapping date ranges"
        );
      table.date("end_date").nullable().comment("End date of the project");
      table
        .timestamp("date_created", { useTz: true })
        .notNullable()
        .defaultTo(tx.fn.now())
        .comment("Timestamp when the project was created");
      table
        .uuid("user_created")
        .nullable()
        .references("id")
        .inTable("directus_users")
        .onDelete("RESTRICT")
        .onUpdate("RESTRICT")
        .comment("User who created the project");
      table
        .timestamp("date_updated", { useTz: true })
        .notNullable()
        .defaultTo(tx.fn.now())
        .comment("Timestamp when the project was last updated");
      table
        .uuid("user_updated")
        .nullable()
        .references("id")
        .inTable("directus_users")
        .onDelete("RESTRICT")
        .onUpdate("RESTRICT")
        .comment("User who last updated the project");
      table
        .jsonb("metadata")
        .nullable()
        .comment("Additional metadata for the project (JSON)");
      table
        .text("notes")
        .nullable()
        .comment("General notes or description for the project");
      table.unique(["property", "name"], {
        indexName: "project_property_name_unq",
      });
    });
    await tx.raw(`
      ALTER TABLE project
      ADD CONSTRAINT project_irrigation_water_pump_no_overlap
      EXCLUDE USING gist (
        irrigation_water_pump WITH =,
        tsrange(start_date, end_date) WITH &&
      );
    `);
    await tx.raw(`
      ALTER TABLE project
      ADD CONSTRAINT project_fertigation_water_pump_no_overlap
      EXCLUDE USING gist (
        fertigation_water_pump WITH =,
        tsrange(start_date, end_date) WITH &&
      );
    `);
    await tx.raw(`
      CREATE TRIGGER set_project_date_updated
      BEFORE UPDATE ON project
      FOR EACH ROW
      EXECUTE FUNCTION update_timestamp_column();
    `);
  });
}

/**
 * This migration file is a placeholder for the down migration.
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    await tx.raw("DROP TRIGGER IF EXISTS set_project_date_updated ON project;");
    await tx.schema.dropTableIfExists("project");
  });
}
