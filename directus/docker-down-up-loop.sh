#!/usr/bin/env bash

#===============================================================================
# Docker Development Loop Script
#===============================================================================
# 
# DESCRIPTION:
#   This script automates the Docker development workflow for Directus.
#   It runs 'docker compose up --build', monitors for server startup,
#   automatically runs database seeding, and provides a convenient way to
#   restart the entire process using SIGQUIT (Ctrl+\).
#
# USAGE:
#   ./docker-down-up-loop.sh
#
# SIGNAL HANDLING:
#   - SIGQUIT (Ctrl+\): Gracefully stops containers, runs docker:down, 
#     and restarts the entire setup process
#   - SIGINT (Ctrl+C): Terminates the script normally
#   - SIGTERM: Terminates the script normally
#
# WORKFLOW:
#   1. Starts Docker containers with 'docker compose up --build'
#   2. Monitors output for "INFO: Server started" message
#   3. When detected, automatically runs 'bun run seed:populate'
#   4. Continues monitoring until interrupted or containers stop
#   5. On SIGQUIT, runs cleanup and restarts the process
#
# DEPENDENCIES:
#   - Docker and Docker Compose
#   - Bun package manager
#   - package.json with 'docker:down' and 'seed:populate' scripts
#
# EXIT CODES:
#   - 0: Normal completion
#   - 131: SIGQUIT received (handled gracefully)
#   - Other: Docker or Bun command failures
#
#===============================================================================

# Exit immediately if any command fails (except where handled)
set -e

# Control variable for the main loop
rerun=true

# Signal handler for SIGQUIT (Ctrl+\)
# This allows developers to quickly restart the entire Docker setup
trap 'echo "\n[INFO] Caught SIGQUIT, re-running..."; rerun=true; bun run docker:down &; return' SIGQUIT

#===============================================================================
# FUNCTION: docker_up
#===============================================================================
# 
# DESCRIPTION:
#   Starts Docker containers and monitors their output for automatic seeding.
#   This function handles the core development workflow automation.
#
# BEHAVIOR:
#   1. Runs 'docker compose up --build' to start/rebuild containers
#   2. Pipes output through a monitoring loop that watches for server startup
#   3. When "INFO: Server started" is detected, runs database seeding
#   4. Handles exit codes appropriately, treating SIGQUIT (131) as expected
#
# RETURNS:
#   - 0: Success or graceful SIGQUIT interruption
#   - Other: Docker command failure codes
#
#===============================================================================
docker_up() {
    echo "[INFO] Running: docker compose up --build"
    seed_ran=false
    
    # Run Docker Compose and monitor output line by line
    # This allows us to detect when the server is ready for seeding
    docker compose up --build | while IFS= read -r line; do
        echo "$line"
        
        # Auto-seed detection: Look for server startup message
        # Only run seeding once per container startup
        if ! $seed_ran && [[ "$line" == *"INFO: Server started"* ]]; then
            echo "[INFO] Detected 'INFO: Server started', running: bun run seed:populate"
            bun run seed:populate
            echo "[INFO] Seed population completed. SIGQUIT (CTRL + \\) will re-run the setup."
            seed_ran=true
        fi
    done
    
    # Capture the exit code from docker compose
    local exit_code=$?
    echo "[INFO] docker:down-up finished with exit code: $exit_code"
    
    # Handle SIGQUIT exit code (131) as expected behavior
    # This happens when Ctrl+\ is pressed to restart the setup
    if [ $exit_code -eq 131 ]; then
        echo "[INFO] Docker was interrupted by SIGQUIT signal"
        return 0
    fi
    
    # Return the actual exit code for other scenarios
    return $exit_code
}

#===============================================================================
# MAIN EXECUTION LOOP
#===============================================================================
# 
# This loop provides the restart functionality when SIGQUIT is received.
# The 'rerun' variable is set to true by the signal handler, causing the
# loop to continue and restart the Docker setup process.
#
# LOOP BEHAVIOR:
#   1. Sets rerun=false at start of each iteration
#   2. Calls docker_up() to start containers and monitor
#   3. If docker_up() succeeds, reports success and exits loop
#   4. If docker_up() fails with non-SIGQUIT error, exits with error
#   5. If SIGQUIT received, rerun=true is set by trap, loop continues
#
#===============================================================================

echo "[INFO] Starting Docker development loop..."
echo "[INFO] Press Ctrl+\\ (SIGQUIT) to restart Docker setup"
echo "[INFO] Press Ctrl+C (SIGINT) to exit"

while $rerun; do
    rerun=false
    
    if docker_up; then
        echo "[INFO] Docker setup completed successfully"
    else
        exit_code=$?
        # Only treat non-SIGQUIT exit codes as actual errors
        if [ $exit_code -ne 131 ]; then
            echo "[ERROR] Docker setup failed with exit code: $exit_code"
            exit $exit_code
        fi
    fi
done

echo "[INFO] Docker development loop terminated"
