// Generate a random hexadecimal string of given length
function generateHexId(length: number): string {
  let result = "";
  const hexChars = "0123456789ABCDEF";
  for (let i = 0; i < length; i++) {
    result += hexChars[Math.floor(Math.random() * 16)];
  }
  return result;
}

// Generate device identifier based on model
function generateDeviceIdentifier(model: DeviceData["model"]): string {
  if (model === "LIC") {
    return generateHexId(12);
  } else {
    return generateHexId(6);
  }
}
import argon2 from "argon2";
import { program } from "commander";
import { randomUUID } from "crypto";
import knex, { Knex } from "knex";

const USER_ROLE_ID = "ac5ba5cb-1d74-4df4-99d3-6758fb49255c";

const DAYS_OF_WEEK = ["MON", "TUE", "WED", "THU", "FRI", "SAT", "SUN"];

function randomDaysOfWeek(): string[] {
  const daysCount = Math.floor(Math.random() * DAYS_OF_WEEK.length) + 1;
  const selectedDays: string[] = [];
  const availableDays = [...DAYS_OF_WEEK];
  for (let i = 0; i < daysCount; i++) {
    const randomIndex = Math.floor(Math.random() * availableDays.length);
    selectedDays.push(availableDays[randomIndex]!);
    availableDays.splice(randomIndex, 1);
  }
  return selectedDays;
}

function generateHash(stringToHash: string): Promise<string> {
  return argon2.hash(stringToHash);
}

// Date utility helpers
function daysAgo(n: number): Date {
  const d = new Date();
  d.setDate(d.getDate() - n);
  return d;
}

// Type definitions for function parameters
interface DeviceData {
  model: "LIC" | "WPC-PL10" | "WPC-PL50" | "VC" | "RM";
  identifier: string;
  notes?: string;
}

interface PropertyDeviceData {
  device: string;
  property: string;
  start_date?: Date;
}

interface WaterPumpData {
  property: string;
  water_pump_controller: string;
  label: string;
  identifier: string;
  pump_type: string;
  pump_model: string;
  has_frequency_inverter: boolean;
  monitor_operation: boolean;
}

interface ReservoirData {
  property: string;
  name: string;
  reservoir_monitor?: string;
  water_pump?: string;
  description?: string;
  capacity?: number;
  enabled: boolean;
  notes?: string;
}

interface UserData {
  first_name: string;
  last_name: string;
  email: string;
  password: string;
}

interface PropertyData {
  account: string;
  name: string;
  timezone?: string;
  address_postal_code?: string;
  address_street_name?: string;
  address_street_number?: string;
  address_city?: string;
  address_state?: string;
  address_country?: string;
  backwash_duration_minutes?: number;
  backwash_period_minutes?: number;
  backwash_delay_seconds?: number;
  rain_gauge_enabled?: boolean;
  rain_gauge_resolution_mm?: number;
  precipitation_volume_limit_mm?: number;
  precipitation_suspended_duration_hours?: number;
}

interface ProjectData {
  property: string;
  name: string;
  description?: string;
  irrigation_water_pump: string;
  fertigation_water_pump?: string;
  localized_irrigation_controller: string;
  pipe_wash_time_seconds?: number;
  backwash_duration_seconds?: number;
  backwash_period_seconds?: number;
  start_date?: string;
  end_date?: string;
}

interface SectorCreationData {
  project: string;
  property: string;
  projectName: string;
  propertyName: string;
  projectIndex: number;
  sectorsCount: number;
  vdDevicesData: DeviceData[];
  sectorsData: Array<{
    name: string;
    description: string;
    valveControllerIndex: number;
    valve_controller_output: number;
    area: number;
  }>;
}

interface IrrigationPlanData {
  project: string;
  sectorIds: string[];
  plansData: Array<{
    name: string;
    description: string;
    start_time: string;
    days_of_week: string[];
    steps: Array<{
      description: string;
      order: number;
      duration_seconds: number;
      fertigation_start_delay_seconds?: number;
      fertigation_duration_seconds?: number;
    }>;
  }>;
}

interface ProjectCreationData {
  property: string;
  propertyName: string;
  projectIndex: number;
  projectData: {
    name: string;
    description: string;
  };
  licDeviceData: DeviceData;
  irrigationWpcDeviceData: DeviceData;
  irrigationWaterPumpData: Omit<
    WaterPumpData,
    "property" | "water_pump_controller"
  >;
  fertigationWpcDeviceData?: DeviceData;
  fertigationWaterPumpData?: Omit<
    WaterPumpData,
    "property" | "water_pump_controller"
  >;
  sectorsData: SectorCreationData;
  irrigationPlansData: Omit<IrrigationPlanData, "project" | "sectorIds">;
}

interface AccountUserData {
  account: string;
  user: string;
  role?: "admin" | "user" | "guest";
  start_date?: Date;
}

interface ProjectsForPropertyData {
  property: string;
  propertyName: string;
  projectsData: Array<ProjectCreationData>;
}

interface UsersAndAccountsData {
  passwordHash: string;
  usersData: UserData[];
}

interface PropertiesData {
  joaquimAccountId: string;
  mariaAccountId: string;
  propertiesData: PropertyData[];
}

interface TestUserData {
  joaquimAccountId: string;
  mariaAccountId: string;
  userData: UserData;
}

// Helper functions with consistent signature pattern
async function createDevice(trx: Knex, data: DeviceData): Promise<string> {
  const [device] = await trx("device")
    .insert({
      model: data.model,
      identifier: data.identifier,
      notes: data.notes || null,
    })
    .returning("id");
  return device.id || device;
}

async function associateDeviceWithProperty(
  trx: Knex,
  data: PropertyDeviceData
): Promise<string> {
  const [pd] = await trx("property_device")
    .insert({
      device: data.device,
      property: data.property,
      // Backdate default start_date to ensure activity for historical mappings
      start_date: data.start_date || daysAgo(60),
    })
    .returning("id");
  return pd.id || pd;
}

async function createWaterPump(
  trx: Knex,
  data: WaterPumpData
): Promise<string> {
  const [waterPump] = await trx("water_pump")
    .insert({
      property: data.property,
      water_pump_controller: data.water_pump_controller,
      label: data.label,
      identifier: data.identifier,
      pump_type: data.pump_type,
      pump_model: data.pump_model,
      has_frequency_inverter: data.has_frequency_inverter,
      monitor_operation: data.monitor_operation,
    })
    .returning("id");
  return waterPump.id || waterPump;
}

async function createReservoir(
  trx: Knex,
  data: ReservoirData
): Promise<string> {
  const [reservoir] = await trx("reservoir")
    .insert({
      property: data.property,
      name: data.name,
      reservoir_monitor: data.reservoir_monitor,
      water_pump: data.water_pump,
      description: data.description,
      capacity: data.capacity,
      enabled: data.enabled,
      notes: data.notes,
    })
    .returning("id");
  return reservoir.id || reservoir;
}

async function createUser(trx: Knex, data: UserData): Promise<string> {
  const [user] = await trx("directus_users")
    .insert({
      id: randomUUID(),
      first_name: data.first_name,
      last_name: data.last_name,
      email: data.email,
      password: data.password,
      role: USER_ROLE_ID,
      status: "active",
      provider: "default",
    })
    .returning("id");
  return user.id || user;
}

async function createAccount(
  trx: Knex,
  data: { ownerId: string }
): Promise<string> {
  const [account] = await trx("account")
    .insert({ owner: data.ownerId })
    .returning("id");
  return account.id || account;
}

async function createProperty(trx: Knex, data: PropertyData): Promise<string> {
  const [property] = await trx("property")
    .insert({
      account: data.account,
      name: data.name,
      timezone: data.timezone || "America/Sao_Paulo",
      address_postal_code: data.address_postal_code,
      address_street_name: data.address_street_name,
      address_street_number: data.address_street_number,
      address_city: data.address_city,
      address_state: data.address_state,
      address_country: data.address_country || "Brasil",
      backwash_duration_minutes: data.backwash_duration_minutes,
      backwash_period_minutes: data.backwash_period_minutes,
      backwash_delay_seconds: data.backwash_delay_seconds,
      rain_gauge_enabled: data.rain_gauge_enabled ?? false,
      rain_gauge_resolution_mm: data.rain_gauge_resolution_mm,
      precipitation_volume_limit_mm: data.precipitation_volume_limit_mm,
      precipitation_suspended_duration_hours:
        data.precipitation_suspended_duration_hours,
    })
    .returning("id");
  return property.id || property;
}

async function createProject(trx: Knex, data: ProjectData): Promise<string> {
  const [project] = await trx("project")
    .insert({
      property: data.property,
      name: data.name,
      description: data.description,
      irrigation_water_pump: data.irrigation_water_pump,
      fertigation_water_pump: data.fertigation_water_pump,
      localized_irrigation_controller: data.localized_irrigation_controller,
      pipe_wash_time_seconds: data.pipe_wash_time_seconds,
      backwash_duration_seconds: data.backwash_duration_seconds,
      backwash_period_seconds: data.backwash_period_seconds,
      start_date: data.start_date || "2024-01-01",
      end_date: data.end_date || "2024-12-31",
    })
    .returning("id");
  return project.id || project;
}

async function createAccountUser(
  trx: Knex,
  data: AccountUserData
): Promise<void> {
  await trx("account_user").insert({
    account: data.account,
    user: data.user,
    role: data.role || "admin",
    start_date: data.start_date || new Date(),
  });
}

async function createSectorsForProject(
  trx: Knex,
  data: SectorCreationData
): Promise<string[]> {
  const vdDevices = [];

  // Create VD devices
  for (const vdDeviceData of data.vdDevicesData) {
    const vdDeviceId = await createDevice(trx, vdDeviceData);
    vdDevices.push(vdDeviceId);

    await associateDeviceWithProperty(trx, {
      device: vdDeviceId,
      property: data.property,
    });
  }

  // Create sectors
  const sectors = [];
  for (const sectorData of data.sectorsData) {
    const [sector] = await trx("sector")
      .insert({
        project: data.project,
        name: sectorData.name,
        description: sectorData.description,
        valve_controller: vdDevices[sectorData.valveControllerIndex],
        valve_controller_output: sectorData.valve_controller_output,
        area: sectorData.area,
      })
      .returning("id");
    sectors.push(sector.id || sector);
  }

  return sectors;
}

async function createIrrigationPlansForProject(
  trx: Knex,
  data: IrrigationPlanData
): Promise<void> {
  for (const planData of data.plansData) {
    const [irrigationPlan] = await trx("irrigation_plan")
      .insert({
        project: data.project,
        name: planData.name,
        description: planData.description,
        start_time: planData.start_time,
        days_of_week: JSON.stringify(planData.days_of_week),
        is_enabled: true,
        fertigation_enabled: Math.random() > 0.5, // Randomly enable fertigation for some plans
        start_date: "2024-01-01",
        end_date: "2024-12-31",
      })
      .returning("id");
    const irrigationPlanId = irrigationPlan.id || irrigationPlan;

    // Create irrigation plan steps
    for (const stepData of planData.steps) {
      await trx("irrigation_plan_step").insert({
        irrigation_plan: irrigationPlanId,
        sector: data.sectorIds[stepData.order - 1], // Use order to get correct sector
        description: stepData.description,
        order: stepData.order,
        duration_seconds: stepData.duration_seconds,
        fertigation_start_delay_seconds:
          stepData.fertigation_start_delay_seconds || null,
        fertigation_duration_seconds:
          stepData.fertigation_duration_seconds || null,
      });
    }
  }
}

async function createProjectForProperty(
  trx: Knex,
  data: ProjectCreationData
): Promise<{
  projectId: string;
  licPropertyDeviceId: string;
  irrigationWpcPropertyDeviceId: string;
  fertigationWpcPropertyDeviceId?: string | null;
}> {
  // Create LIC device
  const licDeviceId = await createDevice(trx, data.licDeviceData);
  const licPdId = await associateDeviceWithProperty(trx, {
    device: licDeviceId,
    property: data.property,
  });

  // Create irrigation water pump and its controller
  const irrigationWpcDeviceId = await createDevice(
    trx,
    data.irrigationWpcDeviceData
  );
  const irrigationWpcPdId = await associateDeviceWithProperty(trx, {
    device: irrigationWpcDeviceId,
    property: data.property,
  });

  const irrigationWaterPumpId = await createWaterPump(trx, {
    ...data.irrigationWaterPumpData,
    property: data.property,
    water_pump_controller: irrigationWpcDeviceId,
  });

  // Create fertigation water pump if data provided
  let fertigationWaterPumpId = null;
  let fertigationWpcPdId: string | null = null;
  if (data.fertigationWpcDeviceData && data.fertigationWaterPumpData) {
    const fertigationWpcDeviceId = await createDevice(
      trx,
      data.fertigationWpcDeviceData
    );
    fertigationWpcPdId = await associateDeviceWithProperty(trx, {
      device: fertigationWpcDeviceId,
      property: data.property,
    });

    fertigationWaterPumpId = await createWaterPump(trx, {
      ...data.fertigationWaterPumpData,
      property: data.property,
      water_pump_controller: fertigationWpcDeviceId,
    });
  }

  // Create the project
  const projectId = await createProject(trx, {
    property: data.property,
    name: data.projectData.name,
    description: data.projectData.description,
    irrigation_water_pump: irrigationWaterPumpId,
    fertigation_water_pump: fertigationWaterPumpId!,
    localized_irrigation_controller: licDeviceId,
    pipe_wash_time_seconds: fertigationWaterPumpId
      ? 5 + Math.floor(Math.random() * 10)
      : undefined, // 5-15 minutes if fertigation enabled
    backwash_duration_seconds: 10 + Math.floor(Math.random() * 20), // 10-30 minutes
    backwash_period_seconds: 240 + Math.floor(Math.random() * 480), // 4-12 hours (240-720 minutes)
  });

  // Create sectors for the project
  const sectorIds = await createSectorsForProject(trx, {
    ...data.sectorsData,
    project: projectId,
  });

  // Create irrigation plans for the project
  await createIrrigationPlansForProject(trx, {
    ...data.irrigationPlansData,
    project: projectId,
    sectorIds,
  });

  return {
    projectId,
    licPropertyDeviceId: licPdId,
    irrigationWpcPropertyDeviceId: irrigationWpcPdId,
    fertigationWpcPropertyDeviceId: fertigationWpcPdId,
  };
}

async function createProjectsForProperty(
  trx: Knex,
  data: ProjectsForPropertyData
): Promise<{
  projectIds: string[];
  licPdIds: string[];
  meshPdIds: string[];
}> {
  const projectIds: string[] = [];
  const licPdIds: string[] = [];
  const meshPdIds: string[] = [];

  for (const projectData of data.projectsData) {
    const created = await createProjectForProperty(trx, projectData);
    projectIds.push(created.projectId);
    licPdIds.push(created.licPropertyDeviceId);
    meshPdIds.push(created.irrigationWpcPropertyDeviceId);
    if (created.fertigationWpcPropertyDeviceId) {
      meshPdIds.push(created.fertigationWpcPropertyDeviceId);
    }
  }
  return { projectIds, licPdIds, meshPdIds };
}

async function createReservoirsForProperty(
  trx: Knex,
  data: {
    property: string;
    propertyName: string;
    rmDeviceIds: string[];
    servicePumpIds: string[];
  }
): Promise<string[]> {
  const reservoirIds = [];
  const availableServicePumps = [...data.servicePumpIds]; // Copy array to track available pumps
  const availableRmDevices = [...data.rmDeviceIds]; // Copy array to track available RM devices

  // Create 1-3 reservoirs per property
  const reservoirCount = Math.floor(Math.random() * 3) + 1;

  for (let i = 0; i < reservoirCount; i++) {
    const reservoirName = `Reservatório ${i + 1} - ${data.propertyName}`;

    // Randomly assign RM device (some reservoirs may not have monitors)
    let rmDevice: string | undefined;
    if (Math.random() > 0.3 && availableRmDevices.length > 0) {
      const rmIndex = Math.floor(Math.random() * availableRmDevices.length);
      rmDevice = availableRmDevices[rmIndex];
      availableRmDevices.splice(rmIndex, 1); // Remove from available RM devices
    } else {
      rmDevice = undefined;
    }

    // Assign service pump ensuring each pump is only used once (unique constraint)
    let servicePump = undefined;
    if (Math.random() > 0.4 && availableServicePumps.length > 0) {
      const pumpIndex = Math.floor(
        Math.random() * availableServicePumps.length
      );
      servicePump = availableServicePumps[pumpIndex];
      availableServicePumps.splice(pumpIndex, 1); // Remove from available pumps
    }

    // Generate realistic capacity (1000L to 50000L)
    const capacity = Math.floor(Math.random() * 49000) + 1000;

    const reservoirId = await createReservoir(trx, {
      property: data.property,
      name: reservoirName,
      reservoir_monitor: rmDevice,
      water_pump: servicePump,
      description: `Reservatório de água para armazenamento na propriedade ${data.propertyName}`,
      capacity,
      enabled: Math.random() > 0.1, // 90% chance of being enabled
      notes:
        Math.random() > 0.5 ? `Observações para ${reservoirName}` : undefined,
    });

    reservoirIds.push(reservoirId);
  }

  return reservoirIds;
}

async function createUsersAndAccounts(trx: Knex, data: UsersAndAccountsData) {
  const userIds = [];

  // Create users
  for (const userData of data.usersData) {
    const userId = await createUser(trx, userData);
    userIds.push(userId);
  }

  // Create accounts
  const accountIds = [];
  for (const userId of userIds) {
    const accountId = await createAccount(trx, { ownerId: userId });
    accountIds.push(accountId);
  }

  return {
    users: { joaquimUserId: userIds[0], mariaUserId: userIds[1] },
    accounts: {
      joaquimAccountId: accountIds[0],
      mariaAccountId: accountIds[1],
    },
  };
}

async function createProperties(trx: Knex, data: PropertiesData) {
  const propertyIds = [];

  for (const propertyData of data.propertiesData) {
    const propertyId = await createProperty(trx, propertyData);
    propertyIds.push(propertyId);
  }

  return propertyIds.map((id, index) => ({
    id,
    name: data.propertiesData[index]!.name,
  }));
}

async function createAccountUserRelationships(
  trx: Knex,
  data: {
    relationshipsData: AccountUserData[];
  }
) {
  for (const relationshipData of data.relationshipsData) {
    await createAccountUser(trx, relationshipData);
  }
}

async function createTestUser(trx: Knex, data: TestUserData) {
  const testUserId = await createUser(trx, data.userData);

  await createAccountUser(trx, {
    account: data.joaquimAccountId,
    user: testUserId,
  });

  await createAccountUser(trx, {
    account: data.mariaAccountId,
    user: testUserId,
  });
}

// New: create a mesh_device_mapping record
async function createMeshDeviceMapping(
  trx: Knex,
  data: {
    mesh_property_device: string;
    lic_property_device: string;
    start_date: Date;
    end_date?: Date | null;
  }
): Promise<string> {
  const [row] = await trx("mesh_device_mapping")
    .insert({
      mesh_property_device: data.mesh_property_device,
      lic_property_device: data.lic_property_device,
      start_date: data.start_date,
      end_date: data.end_date ?? null,
    })
    .returning("id");
  return row.id || row;
}

/**
 * Seed the database with initial data for development purposes.
 * @param trx
 */
async function seedDatabase(trx: Knex) {
  // 1. Check for existing users
  const existingUsers = await trx("directus_users").whereIn("email", [
    "<EMAIL>",
    "<EMAIL>",
  ]);
  if (existingUsers.length > 0) {
    console.log("Seed users already exist. Aborting seeding.");
    throw new Error("Seed users already exist.");
  }

  // 2. Create users and accounts
  const passwordHash = await generateHash("password123");
  const { users, accounts } = await createUsersAndAccounts(trx, {
    passwordHash,
    usersData: [
      {
        first_name: "Joaquim",
        last_name: "Silva",
        email: "<EMAIL>",
        password: passwordHash,
      },
      {
        first_name: "Maria",
        last_name: "Oliveira",
        email: "<EMAIL>",
        password: passwordHash,
      },
    ],
  });

  // 3. Create properties
  const properties = await createProperties(trx, {
    joaquimAccountId: accounts.joaquimAccountId!,
    mariaAccountId: accounts.mariaAccountId!,
    propertiesData: [
      {
        account: accounts.joaquimAccountId!,
        name: "Fazenda Sol Nascente",
        address_postal_code: "12345-678",
        address_street_name: "Estrada Rural",
        address_street_number: "100",
        address_city: "Cidade Sol",
        address_state: "SP",
        backwash_duration_minutes: 15,
        backwash_period_minutes: 480, // 8 hours
        backwash_delay_seconds: 30,
        rain_gauge_enabled: true,
        rain_gauge_resolution_mm: 0.2,
        precipitation_volume_limit_mm: 2.5,
        precipitation_suspended_duration_hours: 12,
      },
      {
        account: accounts.mariaAccountId!,
        name: "Sítio Bela Vista",
        address_postal_code: "23456-789",
        address_street_name: "Rua das Flores",
        address_street_number: "200",
        address_city: "Florada",
        address_state: "MG",
        backwash_duration_minutes: 20,
        backwash_period_minutes: 720, // 12 hours
        backwash_delay_seconds: 45,
        rain_gauge_enabled: false,
        rain_gauge_resolution_mm: 0.2,
        precipitation_volume_limit_mm: 3.0,
        precipitation_suspended_duration_hours: 18,
      },
      {
        account: accounts.mariaAccountId!,
        name: "Chácara das Flores",
        address_postal_code: "34567-890",
        address_street_name: "Alameda das Rosas",
        address_street_number: "300",
        address_city: "Jardim",
        address_state: "RJ",
        backwash_duration_minutes: 10,
        backwash_period_minutes: 360, // 6 hours
        backwash_delay_seconds: 60,
        rain_gauge_enabled: true,
        rain_gauge_resolution_mm: 0.1,
        precipitation_volume_limit_mm: 1.5,
        precipitation_suspended_duration_hours: 24,
      },
    ],
  });

  // 4. Create projects for each property
  // Track PDs per property to respect mapping constraints
  const perPropertyLicPdIds: Record<string, string[]> = {};
  const perPropertyMeshPdIds: Record<string, string[]> = {};

  for (const property of properties) {
    // Generate project data for this property
    const projectsData: ProjectCreationData[] = [];
    const projectPlaceKinds = [
      "Lago",
      "Lagoa",
      "Represa",
      "Açude",
      "Córrego",
      "Rio",
      "Riacho",
    ];
    const projectPlaces = [
      "ao Norte",
      "ao Sul",
      "ao Leste",
      "ao Oeste",
      "perto do Pasto",
      "perto da Mata",
      "perto da Casa",
      "perto do Pomar",
      "perto da Horta",
      "perto do Viveiro",
      "perto do Curral",
      "perto do Brejo",
      "perto do Poço",
    ];
    const projectNames = new Set<string>();
    for (let i = 0; i < 2; i++) {
      let projectName: string;
      // Generate a unique project name for the property
      do {
        const projectPlaceKind =
          projectPlaceKinds[
            Math.floor(Math.random() * projectPlaceKinds.length)
          ]!;
        const projectPlace =
          projectPlaces[Math.floor(Math.random() * projectPlaces.length)]!;
        projectName = `${projectPlaceKind} ${projectPlace}`;
      } while (projectNames.has(projectName));
      projectNames.add(projectName);

      // Create VC devices data (1 per 4 sectors)
      const sectorsCount = 6;
      const vdDevicesData: DeviceData[] = [];
      for (let vcIndex = 0; vcIndex < Math.ceil(sectorsCount / 4); vcIndex++) {
        vdDevicesData.push({
          model: "VC",
          identifier: generateDeviceIdentifier("VC"),
          notes: `Controlador de válvulas ${vcIndex + 1} para ${projectName}`,
        });
      }

      // Create sectors data
      const sectorsData: Array<{
        name: string;
        description: string;
        valveControllerIndex: number;
        valve_controller_output: number;
        area: number;
      }> = [];
      for (let sectorIndex = 0; sectorIndex < sectorsCount; sectorIndex++) {
        sectorsData.push({
          name: `Setor ${sectorIndex + 1}`,
          description: `Área de irrigação ${sectorIndex + 1} do ${projectName}`,
          valveControllerIndex: Math.floor(sectorIndex / 4),
          valve_controller_output: (sectorIndex % 4) + 1,
          area: 1.5 + sectorIndex * 0.3,
        });
      }

      // Generate irrigation plans data
      const plansCount = Math.ceil(sectorsCount / 4);
      const plansData = [];
      for (let planIndex = 0; planIndex < plansCount; planIndex++) {
        const steps = [];
        const sectorsForThisPlan = Math.min(4, sectorsCount - planIndex * 4);

        for (let stepIndex = 0; stepIndex < sectorsForThisPlan; stepIndex++) {
          const hasFerti = Math.random() > 0.5; // Randomly enable fertigation for some steps
          steps.push({
            description: `Irrigação do setor ${stepIndex + 1} do plano ${
              planIndex + 1
            }`,
            order: stepIndex + 1,
            duration_seconds: 1800 + stepIndex * 300,
            fertigation_start_delay_seconds: hasFerti
              ? 300 + Math.floor(Math.random() * 300)
              : undefined, // 5-10 minutes delay
            fertigation_duration_seconds: hasFerti
              ? 600 + Math.floor(Math.random() * 600)
              : undefined, // 10-20 minutes duration
          });
        }

        plansData.push({
          name: `Plano Irrigação ${planIndex + 1}`,
          description: `Plano de irrigação automatizada ${
            planIndex + 1
          } para ${projectName}`,
          start_time: `0${6 + planIndex}:00:00`,
          days_of_week: randomDaysOfWeek(),
          steps,
        });
      }

      const projectData: ProjectCreationData = {
        property: property.id,
        propertyName: property.name,
        projectIndex: i,
        projectData: {
          name: projectName,
          description: `Sistema de irrigação automatizada para a área ${
            i === 0 ? "norte" : "sul"
          } da propriedade`,
        },
        licDeviceData: {
          model: "LIC",
          identifier: generateDeviceIdentifier("LIC"),
          notes: `Controlador de irrigação localizado para ${projectName}`,
        },
        irrigationWpcDeviceData: {
          model: "WPC-PL10",
          identifier: generateDeviceIdentifier("WPC-PL10"),
          notes: `Controlador da bomba de irrigação para ${projectName}`,
        },
        irrigationWaterPumpData: {
          label: `Bomba Irrigação ${i + 1}`,
          identifier: generateHexId(6),
          pump_type: "IRRIGATION",
          pump_model: "Modelo ABC-123",
          has_frequency_inverter: Math.random() > 0.5,
          monitor_operation: Math.random() > 0.3,
        },
        sectorsData: {
          project: "", // Filled in createProjectForProperty
          property: property.id,
          projectName,
          propertyName: property.name,
          projectIndex: i,
          sectorsCount,
          vdDevicesData,
          sectorsData,
        },
        irrigationPlansData: {
          plansData,
        },
      };

      // Add fertigation data only for first project
      if (i === 0) {
        projectData.fertigationWpcDeviceData = {
          model: "WPC-PL50",
          identifier: generateDeviceIdentifier("WPC-PL50"),
          notes: `Controlador da bomba de fertirrigação para ${projectName}`,
        };
        projectData.fertigationWaterPumpData = {
          label: `Bomba Fertirrigação ${i + 1}`,
          identifier: generateHexId(6),
          pump_type: "FERTIGATION",
          pump_model: "Modelo XYZ-456",
          has_frequency_inverter: Math.random() > 0.5,
          monitor_operation: Math.random() > 0.3,
        };
      }

      projectsData.push(projectData);
    }

    const created = await createProjectsForProperty(trx, {
      property: property.id,
      propertyName: property.name,
      projectsData,
    });

    // Save LIC and mesh PD ids grouped by property
    perPropertyLicPdIds[property.id] = [
      ...(perPropertyLicPdIds[property.id] || []),
      ...created.licPdIds,
    ];
    perPropertyMeshPdIds[property.id] = [
      ...(perPropertyMeshPdIds[property.id] || []),
      ...created.meshPdIds,
    ];

    // Create RM devices for reservoir monitoring
    const rmDeviceIds = [];
    const rmDeviceCount = Math.floor(Math.random() * 3) + 1; // 1-3 RM devices per property

    for (let i = 0; i < rmDeviceCount; i++) {
      const rmDeviceId = await createDevice(trx, {
        model: "RM",
        identifier: generateDeviceIdentifier("RM"),
        notes: `Monitor de reservatório ${i + 1} para ${property.name}`,
      });

      await associateDeviceWithProperty(trx, {
        device: rmDeviceId,
        property: property.id,
      });

      rmDeviceIds.push(rmDeviceId);
    }

    // Create SERVICE water pumps for reservoirs
    const servicePumpIds = [];
    const servicePumpCount = Math.floor(Math.random() * 2) + 1; // 1-2 service pumps per property

    for (let i = 0; i < servicePumpCount; i++) {
      // Create WPC device for service pump
      const serviceWpcDeviceId = await createDevice(trx, {
        model: "WPC-PL10",
        identifier: generateDeviceIdentifier("WPC-PL10"),
        notes: `Controlador da bomba de serviço ${i + 1} para ${property.name}`,
      });

      await associateDeviceWithProperty(trx, {
        device: serviceWpcDeviceId,
        property: property.id,
      });

      // Create service water pump
      const servicePumpId = await createWaterPump(trx, {
        property: property.id,
        water_pump_controller: serviceWpcDeviceId,
        label: `Bomba Serviço ${i + 1}`,
        identifier: generateHexId(6),
        pump_type: "SERVICE",
        pump_model: "Modelo SRV-789",
        has_frequency_inverter: Math.random() > 0.6,
        monitor_operation: Math.random() > 0.4,
      });

      servicePumpIds.push(servicePumpId);
    }

    // Create reservoirs for the property
    await createReservoirsForProperty(trx, {
      property: property.id,
      propertyName: property.name,
      rmDeviceIds,
      servicePumpIds,
    });
  }

  // 5. Create account user relationships
  await createAccountUserRelationships(trx, {
    relationshipsData: [
      {
        account: accounts.joaquimAccountId!,
        user: users.joaquimUserId!,
      },
      {
        account: accounts.mariaAccountId!,
        user: users.mariaUserId!,
      },
      {
        account: accounts.mariaAccountId!,
        user: users.joaquimUserId!,
      },
      {
        account: accounts.joaquimAccountId!,
        user: users.mariaUserId!,
      },
    ],
  });

  // 6. Create test user
  await createTestUser(trx, {
    joaquimAccountId: accounts.joaquimAccountId!,
    mariaAccountId: accounts.mariaAccountId!,
    userData: {
      first_name: "Test",
      last_name: "User",
      email: "<EMAIL>",
      password: await generateHash("teste"),
    },
  });

  // 7. Seed mesh_device_mapping for mesh PDs to active LIC PDs
  // Strategy: pair within each property to satisfy trigger constraints
  const propertyIdsForMappings = Object.keys(perPropertyMeshPdIds);
  for (const propertyId of propertyIdsForMappings) {
    const meshList = perPropertyMeshPdIds[propertyId] || [];
    const licList = perPropertyLicPdIds[propertyId] || [];
    if (meshList.length === 0 || licList.length === 0) continue;

    const referenceStart = new Date();
    referenceStart.setDate(referenceStart.getDate() - 10); // active since 10 days ago

    const historyStart = new Date();
    historyStart.setDate(historyStart.getDate() - 30);
    const historyEnd = new Date();
    historyEnd.setDate(historyEnd.getDate() - 20);

    // Round-robin LICs within same property
    for (let i = 0; i < meshList.length; i++) {
      const meshPd = meshList[i]!;
      const licPd = licList[i % licList.length]!;

      // Historical mapping (ended before current period)
      await createMeshDeviceMapping(trx, {
        mesh_property_device: meshPd,
        lic_property_device: licPd,
        start_date: historyStart,
        end_date: historyEnd,
      });

      // Active mapping (no end_date -> indefinite)
      await createMeshDeviceMapping(trx, {
        mesh_property_device: meshPd,
        lic_property_device: licPd,
        start_date: referenceStart,
        end_date: null,
      });

      // Update current mesh mapping via DB function for each mesh PD
      await trx.raw(
        `SELECT im_update_current_mesh_device_mapping(ARRAY[?]::uuid[], now())`,
        [meshPd]
      );
    }
  }
}

/**
 * Cleanup the database by removing all seed data.
 * Do not erase data not created by the seed script.
 * @param knex
 */
async function cleanupSeedData(knex: Knex) {
  // 1. Find seed users
  const seedEmails = [
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
  ];
  const users = await knex("directus_users")
    .whereIn("email", seedEmails)
    .select("id");
  if (users.length === 0) {
    console.log("Seed users not found. No cleanup necessary.");
    return;
  }
  const userIds = users.map((u) => u.id);

  // 2. Find accounts owned by seed users
  const accounts = await knex("account").whereIn("owner", userIds).select("id");
  const accountIds = accounts.map((a) => a.id);

  // 3. Find properties belonging to seed accounts
  const properties = await knex("property")
    .whereIn("account", accountIds)
    .select("id");
  const propertyIds = properties.map((p) => p.id);

  // 4. Find projects belonging to seed properties
  const projects = await knex("project")
    .whereIn("property", propertyIds)
    .select("id");
  const projectIds = projects.map((p) => p.id);

  // 5. Find sectors belonging to seed projects
  const sectors = await knex("sector")
    .whereIn("project", projectIds)
    .select("id");
  const sectorIds = sectors.map((s) => s.id);

  // 6. Find irrigation plans belonging to seed projects
  const irrigationPlans = await knex("irrigation_plan")
    .whereIn("project", projectIds)
    .select("id");
  const irrigationPlanIds = irrigationPlans.map((ip) => ip.id);

  // 7. Find water pumps belonging to seed properties
  const waterPumps = await knex("water_pump")
    .whereIn("property", propertyIds)
    .select("id");
  const waterPumpIds = waterPumps.map((wp) => wp.id);

  // 8. Find reservoirs belonging to seed properties
  const reservoirs = await knex("reservoir")
    .whereIn("property", propertyIds)
    .select("id");
  const reservoirIds = reservoirs.map((r) => r.id);

  // 9. Find devices associated with seed properties
  const propertyDevices = await knex("property_device")
    .whereIn("property", propertyIds)
    .select("device");
  const deviceIds = propertyDevices.map((pd) => pd.device);

  // Additional cleanup for mesh_device_mapping referencing PDs from seed properties
  const seedPropertyDeviceIds = await knex("property_device")
    .whereIn("property", propertyIds)
    .select("id");
  const pdIds = seedPropertyDeviceIds.map((r) => r.id);

  console.log("Cleaning up mesh_device_mapping...");
  await knex("mesh_device_mapping")
    .whereIn("mesh_property_device", pdIds)
    .orWhereIn("lic_property_device", pdIds)
    .del();

  // Delete data in reverse order of dependencies
  console.log("Cleaning up irrigation_plan_step...");
  await knex("irrigation_plan_step")
    .whereIn("irrigation_plan", irrigationPlanIds)
    .del();

  console.log("Cleaning up irrigation_plan...");
  await knex("irrigation_plan").whereIn("id", irrigationPlanIds).del();

  console.log("Cleaning up sector...");
  await knex("sector").whereIn("id", sectorIds).del();

  console.log("Cleaning up project...");
  await knex("project").whereIn("id", projectIds).del();

  console.log("Cleaning up water_pump...");
  await knex("water_pump").whereIn("id", waterPumpIds).del();

  console.log("Cleaning up reservoir...");
  await knex("reservoir").whereIn("id", reservoirIds).del();

  console.log("Cleaning up property_device...");
  await knex("property_device").whereIn("property", propertyIds).del();

  console.log("Cleaning up device...");
  await knex("device").whereIn("id", deviceIds).del();

  console.log("Cleaning up property...");
  await knex("property").whereIn("id", propertyIds).del();

  console.log("Cleaning up account_user...");
  await knex("account_user").whereIn("account", accountIds).del();

  console.log("Cleaning up account...");
  await knex("account").whereIn("id", accountIds).del();

  console.log("Cleaning up directus_users...");
  await knex("directus_users").whereIn("id", userIds).del();
}

async function main() {
  program
    .option("--db-host <host>", "Database host")
    .option("--db-port <port>", "Database port")
    .option("--db-user <user>", "Database user")
    .option("--db-password <password>", "Database password")
    .option("--db-name <n>", "Database name")
    .option("--cleanup", "Cleanup seed data")
    .description("Seed the database with initial data for development purposes")
    .parse(process.argv);

  const options = program.opts();

  const knexInstance = knex({
    client: "pg",
    connection: {
      host: options.dbHost || process.env.DB_HOST,
      port: options.dbPort || parseInt(process.env.DB_PORT || "5432"),
      user: options.dbUser || process.env.DB_USER,
      password: options.dbPassword || process.env.DB_PASSWORD,
      database: options.dbName || process.env.DB_DATABASE,
    },
  });
  try {
    await knexInstance.transaction(async (trx) => {
      if (options.cleanup) {
        console.log("Cleaning up seed data...");
        await cleanupSeedData(trx);
        console.log("Seed data cleaned up successfully.");
        return;
      }
      console.log("Seeding database...");
      await seedDatabase(trx);
      console.log("Database seeded successfully.");
    });
  } catch (error) {
    console.error("Error seeding database:", error);
  } finally {
    await knexInstance.destroy();
  }
}

if (import.meta.main) {
  main();
}
