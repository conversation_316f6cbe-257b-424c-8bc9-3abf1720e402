import knex, { <PERSON><PERSON> } from "knex";
import { program } from "commander";
import * as fs from "fs/promises";

// Custom deep equal function
function isEqual(a: any, b: any): boolean {
  // Primitive types and same reference
  if (a === b) return true;

  // Handle null/undefined
  if (a == null || b == null) return a === b;

  // Check type
  if (a.constructor !== b.constructor) return false;

  // Handle arrays
  if (Array.isArray(a)) {
    if (a.length !== b.length) return false;
    for (let i = 0; i < a.length; i++) {
      if (!isEqual(a[i], b[i])) return false;
    }
    return true;
  }

  // Handle objects
  if (typeof a === "object") {
    const keysA = Object.keys(a);
    const keysB = Object.keys(b);
    if (keysA.length !== keysB.length) return false;

    for (const key of keysA) {
      if (!Object.prototype.hasOwnProperty.call(b, key)) return false;
      if (!isEqual(a[key], b[key])) return false;
    }
    return true;
  }

  // Handle other types
  return false;
}

function pick<T extends Record<string, any>, K extends keyof T>(
  obj: T,
  keys: K[]
): Pick<T, K> {
  const result = {} as Pick<T, K>;
  for (const key of keys) {
    if (key in obj) {
      result[key] = obj[key];
    }
  }
  return result;
}

function omit<T extends Record<string, any>, K extends keyof T>(
  obj: T,
  keys: K[]
): Omit<T, K> {
  const result = { ...obj };
  for (const key of keys) {
    delete result[key];
  }
  return result;
}

interface TableConfig {
  keyFields: string[];
  dependencies?: string[];
}

const TABLE_CONFIGS: Record<string, TableConfig> = {
  directus_collections: {
    keyFields: ["collection"],
    dependencies: [],
  },
  directus_fields: {
    keyFields: ["collection", "field"],
    dependencies: ["directus_collections"],
  },
  directus_permissions: {
    keyFields: ["collection", "action", "policy"],
    dependencies: ["directus_policies"],
  },
  directus_policies: {
    keyFields: ["id"],
    dependencies: [],
  },
  directus_access: {
    keyFields: ["policy", "role", "user"],
    dependencies: ["directus_policies", "directus_roles"],
  },
  directus_roles: {
    keyFields: ["id"],
    dependencies: [],
  },
  directus_relations: {
    keyFields: ["many_collection", "many_field"],
    dependencies: ["directus_collections"],
  },
  directus_translations: {
    keyFields: ["language", "key"],
    dependencies: [],
  },
  directus_flows: {
    keyFields: ["id"],
    dependencies: [],
  },
  directus_operations: {
    keyFields: ["id"],
    dependencies: ["directus_flows"],
  },
};

const INSERT_ORDER = [
  "directus_roles",
  "directus_policies",
  "directus_collections",
  "directus_flows",
  "directus_fields",
  "directus_relations",
  "directus_operations",
  "directus_permissions",
  "directus_access",
  "directus_translations",
];

const DELETE_ORDER = [...INSERT_ORDER].reverse();

function getRecordKey(record: any, keyFields: string[]): string {
  return keyFields.map((field) => record[field]).join("::");
}

function diffRecords(
  prevRecords: any[],
  currentRecords: any[],
  keyFields: string[]
) {
  const prevMap = new Map<string, any>();
  prevRecords.forEach((record) => {
    prevMap.set(getRecordKey(record, keyFields), record);
  });

  const currentMap = new Map<string, any>();
  currentRecords.forEach((record) => {
    currentMap.set(getRecordKey(record, keyFields), record);
  });

  const inserts: any[] = [];
  const updates: any[] = [];
  const deletes: any[] = [];

  // Find inserts and updates
  for (const [key, currentRecord] of currentMap.entries()) {
    const prevRecord = prevMap.get(key);
    if (!prevRecord) {
      inserts.push(currentRecord);
    } else {
      const changedFields = Object.keys(currentRecord).filter(
        (field) => !isEqual(currentRecord[field], prevRecord[field])
      );
      if (changedFields.length > 0) {
        const changes = changedFields.reduce((obj, field) => {
          obj[field] = currentRecord[field];
          return obj;
        }, {} as Record<string, any>);
        updates.push({
          key,
          keyFields,
          current: currentRecord,
          previous: prevRecord,
          changes,
        });
      }
    }
  }

  // Find deletes
  for (const [key, prevRecord] of prevMap.entries()) {
    if (!currentMap.has(key)) {
      deletes.push(prevRecord);
    }
  }

  return { inserts, updates, deletes };
}

function adjustConfigStateForMigration(state: any): any {
  // Adjust the config as needed for the migration
  state.flows?.forEach((flow: any) => {
    delete flow.user_created;
  });
  state.operations?.forEach((operation: any) => {
    delete operation.user_created;
  });
  return state;
}

async function generateMigration(
  knexInstance: Knex,
  outputPath?: string
): Promise<string> {
  // Fetch current state
  const currentStateResult = await knexInstance
    .select("config")
    .from("directus_data_config_json")
    .first();
  const currentState = currentStateResult?.config || {};

  // Fetch last saved state
  const lastStateRecord = await knexInstance
    .select("config")
    .from("directus_migration.directus_config")
    .orderBy("date_created", "desc")
    .first();
  const lastState = lastStateRecord?.config || {};

  const tableDiffs: Record<string, ReturnType<typeof diffRecords>> = {};

  // Compute diffs for each table
  for (const [table, config] of Object.entries(TABLE_CONFIGS)) {
    const stateKey = table.replace("directus_", "");
    const prevRecords = lastState[stateKey] || [];
    const currentRecords = currentState[stateKey] || [];
    tableDiffs[table] = diffRecords(
      prevRecords,
      currentRecords,
      config.keyFields
    );
  }

  // Generate migration content
  let migrationContent = `/**
 * Applies the current state of the Directus config to the database.
 * This migration file is auto-generated and should not be manually edited.
 * @module directus/migration/gen
 * @version ${new Date().toISOString()}
 * @description This migration applies the current state of the Directus config to the database.
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
// if (true) return; // Skip migration if already applied\n`;
  migrationContent += `  await knex.transaction(async (tx) => {\n`;

  // INSERTS & UPDATES
  for (const table of INSERT_ORDER) {
    const diff = tableDiffs[table];
    if (!diff) continue; // Skip if no diff found
    const { keyFields } = TABLE_CONFIGS[table] ?? {};
    if (!keyFields) continue; // Skip if no key fields defined

    if (diff.inserts.length > 0) {
      migrationContent += `    // Insert ${diff.inserts.length} records into ${table}\n`;
      migrationContent += `    await tx.batchInsert('${table}', [\n`;
      for (const record of diff.inserts) {
        const insertRecord = keyFields.includes("id")
          ? record
          : omit(record, ["id"]);
        migrationContent += `      ${JSON.stringify(insertRecord)},\n`;
      }
      migrationContent += `    ]);\n\n`;
    }

    if (diff.updates.length > 0) {
      migrationContent += `    // Update ${diff.updates.length} records in ${table}\n`;
      for (const update of diff.updates) {
        const updateRecord = keyFields.includes("id")
          ? update.changes
          : omit(update.changes, ["id"]);
        migrationContent += `    await tx('${table}').where(${JSON.stringify(
          pick(update.current, keyFields)
        )}).update(${JSON.stringify(updateRecord)});\n`;
      }
      migrationContent += `\n`;
    }
  }

  // DELETES
  for (const table of DELETE_ORDER) {
    const diff = tableDiffs[table];
    if (!diff) continue; // Skip if no diff found
    const { keyFields } = TABLE_CONFIGS[table] ?? {};
    if (!keyFields) continue; // Skip if no key fields defined

    if (diff.deletes.length > 0) {
      migrationContent += `    // Delete ${diff.deletes.length} records from ${table}\n`;
      for (const record of diff.deletes) {
        const whereClause = keyFields
          .map((field) => `${field}: ${JSON.stringify(record[field])}`)
          .join(", ");
        migrationContent += `    await tx('${table}').where(${JSON.stringify(
          pick(record, keyFields)
        )}).del();\n`;
      }
      migrationContent += `\n`;
    }
  }

  migrationContent += `  });\n`;
  migrationContent += `}\n\n`;

  // DOWN MIGRATION
  migrationContent += `/**
    * Reverts the changes made by the up migration.
    * This migration file is auto-generated and should not be manually edited.
    * @module directus/migration/gen
    * @version ${new Date().toISOString()}
    * @description This migration reverts the changes made by the up migration.
    * @param {import('knex').Knex} knex
    */
export async function down(knex) {\n`;
  migrationContent += `  await knex.transaction(async (tx) => {\n`;

  // REVERSE DELETES (INSERTS)
  for (const table of INSERT_ORDER) {
    const diff = tableDiffs[table];
    if (!diff) continue; // Skip if no diff found

    if (diff.deletes.length > 0) {
      migrationContent += `    // Re-insert ${diff.deletes.length} records into ${table}\n`;
      migrationContent += `    await tx.batchInsert('${table}', [\n`;
      for (const record of diff.deletes) {
        migrationContent += `      ${JSON.stringify(record)},\n`;
      }
      migrationContent += `    ]);\n\n`;
    }
  }

  // REVERSE UPDATES
  for (const table of INSERT_ORDER) {
    const diff = tableDiffs[table];
    if (!diff) continue; // Skip if no diff found
    const { keyFields } = TABLE_CONFIGS[table] ?? {};
    if (!keyFields) continue; // Skip if no key fields defined

    if (diff.updates.length > 0) {
      migrationContent += `    // Revert ${diff.updates.length} updates in ${table}\n`;
      for (const update of diff.updates) {
        migrationContent += `    await tx('${table}').where(${JSON.stringify(
          pick(update.current, keyFields)
        )}).update(${JSON.stringify(omit(update.previous, ["id"]))});\n`;
      }
      migrationContent += `\n`;
    }
  }

  // REVERSE INSERTS (DELETES)
  for (const table of DELETE_ORDER) {
    const diff = tableDiffs[table];
    if (!diff) continue; // Skip if no diff found
    const { keyFields } = TABLE_CONFIGS[table] ?? {};
    if (!keyFields) continue; // Skip if no key fields defined

    if (diff.inserts.length > 0) {
      migrationContent += `    // Delete ${diff.inserts.length} records from ${table} (reverse of insert)\n`;
      for (const record of diff.inserts) {
        const whereClause = keyFields
          .map((field) => `${field}: ${JSON.stringify(record[field])}`)
          .join(", ");
        migrationContent += `    await tx('${table}').where(${JSON.stringify(
          pick(record, keyFields)
        )}).del();\n`;
      }
      migrationContent += `\n`;
    }
  }

  migrationContent += `  });\n`;
  migrationContent += `}\n`;

  if (outputPath) {
    await fs.writeFile(outputPath, migrationContent);
    console.log(`Migration file generated at: ${outputPath}`);
  }

  return migrationContent;
}

async function main() {
  program
    .option("-o, --output <path>", "Output file path for the migration")
    .option("--db-host <host>", "Database host")
    .option("--db-port <port>", "Database port")
    .option("--db-user <user>", "Database user")
    .option("--db-password <password>", "Database password")
    .option("--db-name <name>", "Database name")
    .parse(process.argv);

  const options = program.opts();

  const knexInstance = knex({
    client: "pg",
    connection: {
      host: options.dbHost || process.env.DB_HOST,
      port: options.dbPort || parseInt(process.env.DB_PORT || "5432"),
      user: options.dbUser || process.env.DB_USER,
      password: options.dbPassword || process.env.DB_PASSWORD,
      database: options.dbName || process.env.DB_DATABASE,
    },
  });

  try {
    const migrationContent = await generateMigration(
      knexInstance,
      options.output
    );
    if (!options.output) {
      console.log(migrationContent);
    }
  } catch (error) {
    console.error("Error generating migration:", error);
    process.exit(1);
  } finally {
    await knexInstance.destroy();
  }
}

if (import.meta.main) {
  main();
}
