import { connect } from "mqtt";
import { handleMessage } from "./handle-message";
import { logger } from "./log";

const mqttBrokerUrl = process.env.MQTT_BROKER_URL;
const mqttUsername = process.env.MQTT_USERNAME;
const mqttPassword = process.env.MQTT_PASSWORD;

if (!mqttBrokerUrl || !mqttUsername || !mqttPassword) {
  throw new Error("MQTT configuration is missing in environment variables.");
}
logger.log("Connecting to MQTT broker:", mqttBrokerUrl);
logger.log("Using username:", mqttUsername);

const client = connect(mqttBrokerUrl, {
  username: mqttUsername,
  password: mqttPassword,
  protocolVersion: 5,
  clean: true, // Clean session
  reconnectPeriod: 1000, // Reconnect every second if disconnected
  connectTimeout: 30000, // 30 seconds timeout for connection
  reschedulePings: true, // Reschedule pings automatically
  will: {
    topic: "client/disconnected",
    payload: JSON.stringify({
      message: "Client disconnected unexpectedly",
      timestamp: new Date().toISOString(),
    }),
    qos: 1,
    retain: false,
  },
  properties: {
    sessionExpiryInterval: 3600, // Session expiry interval in seconds
  },

  keepalive: 60, // Keepalive interval in seconds
});

client.on("connect", () => {
  logger.log("Connected to MQTT broker");
});

client.on("error", (err) => {
  console.error("MQTT connection error:", err);
});

client.on("message", handleMessage);

client.subscribe("#", (err) => {
  if (err) {
    console.error("Failed to subscribe to topics:", err);
  } else {
    logger.log("Subscribed to all topics");
  }
});

// Handle graceful shutdown
const shutdown = () => {
  logger.log("Disconnecting from MQTT broker...");
  client.end(() => {
    logger.log("Disconnected from MQTT broker");
    process.exit(0);
  });
};

process.on("SIGINT", shutdown);
process.on("SIGTERM", shutdown);
process.on("exit", shutdown);
