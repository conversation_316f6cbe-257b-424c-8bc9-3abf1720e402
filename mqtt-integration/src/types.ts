export type MQTTTopicType = "report" | "downlink" | "unknown_codec_topic";

export type MQTTTopicInfo<T extends MQTTTopicType> =
  T extends "unknown_codec_topic"
    ? {
        topicType: "unknown_codec_topic";
        deviceId: string;
        topic: string;
      }
    : { topicType: T; deviceId: string };

export type MQTTTopicMessage<
  T extends MQTTTopicType,
  P = Buffer
> = MQTTTopicInfo<T> & {
  payload: P;
};
