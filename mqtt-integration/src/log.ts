class Logger {
  private prepareLogEntry(message: any): { formattedMessage: string } {
    if (typeof message !== "string") {
      message = JSON.stringify(message);
    }
    const timestamp = new Date().toISOString();
    const formattedMessage = `[${timestamp}] ${message}`;
    return { formattedMessage };
  }

  log(message?: any, ...optionalParams: any[]) {
    const { formattedMessage } = this.prepareLogEntry(message);
    console.log(formattedMessage, ...optionalParams);
  }

  error(message?: any, ...optionalParams: any[]) {
    const { formattedMessage } = this.prepareLogEntry(message);
    console.error(formattedMessage, ...optionalParams);
  }

  warn(message?: any, ...optionalParams: any[]) {
    const { formattedMessage } = this.prepareLogEntry(message);
    console.warn(formattedMessage, ...optionalParams);
  }

  debug(message?: any, ...optionalParams: any[]) {
    const { formattedMessage } = this.prepareLogEntry(message);
    console.debug(formattedMessage, ...optionalParams);
  }

  info(message?: any, ...optionalParams: any[]) {
    const { formattedMessage } = this.prepareLogEntry(message);
    console.info(formattedMessage, ...optionalParams);
  }

  trace(message?: any, ...optionalParams: any[]) {
    const { formattedMessage } = this.prepareLogEntry(message);
    console.trace(formattedMessage, ...optionalParams);
  }

  assert(condition: any, message?: any, ...optionalParams: any[]) {
    if (!condition) {
      const { formattedMessage } = this.prepareLogEntry(message);
      console.assert(condition, formattedMessage, ...optionalParams);
    }
  }
}

export const logger = new Logger();
