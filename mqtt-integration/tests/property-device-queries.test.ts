import { describe, it, expect } from "bun:test";
import {
  getCurrentPropertyForDevice,
  getDeviceMetadataForProperty,
  getPropertyDeviceMetadataForDevice,
} from "../src/db/queries/property-device-queries";
import { runInTransaction } from "./helpers/db";
import {
  insertAccount,
  insertProperty,
  insertDevice,
  insertPropertyDevice,
  insertUser,
} from "./helpers/fixtures";
import type { SQL } from "bun";

describe("property-device-queries", () => {
  describe("getCurrentPropertyForDevice", () => {
    it("should return property when device is associated with property at reference date", async () => {
      await runInTransaction(async (trx) => {
        // Create test data
        const user = await insertUser(trx);
        const account = await insertAccount(trx, user.id);
        const property = await insertProperty(trx, account.id, "Test Property");
        const device = await insertDevice(trx, "LIC", "TEST001");
        const startDate = new Date("2024-01-01");
        const endDate = new Date("2024-12-31");

        await insertPropertyDevice(
          trx,
          device.id,
          property.id,
          startDate,
          endDate
        );

        // Test the function
        const result = await getCurrentPropertyForDevice(
          trx,
          "TEST001",
          "LIC",
          new Date("2024-06-01")
        );

        expect(result).toBeDefined();
        expect(result.id).toBe(property.id);
        expect(result.name).toBe("Test Property");
      });
    });

    it("should return undefined when device is not associated with any property", async () => {
      await runInTransaction(async (trx) => {
        const result = await getCurrentPropertyForDevice(
          trx,
          "NONEXISTENT001",
          "LIC",
          new Date()
        );

        expect(result).toBeUndefined();
      });
    });

    it("should return undefined when device exists but is not associated at reference date", async () => {
      await runInTransaction(async (trx) => {
        // Create test data
        const user = await insertUser(trx);
        const account = await insertAccount(trx, user.id);
        const property = await insertProperty(trx, account.id, "Test Property");
        const device = await insertDevice(trx, "LIC", "TEST002");
        const startDate = new Date("2024-01-01");
        const endDate = new Date("2024-12-31");

        await insertPropertyDevice(
          trx,
          device.id,
          property.id,
          startDate,
          endDate
        );

        // Test with date outside the association period
        const result = await getCurrentPropertyForDevice(
          trx,
          "TEST002",
          "LIC",
          new Date("2023-01-01")
        );

        expect(result).toBeUndefined();
      });
    });

    it("should handle null end dates (ongoing associations)", async () => {
      await runInTransaction(async (trx) => {
        // Create test data
        const user = await insertUser(trx);
        const account = await insertAccount(trx, user.id);
        const property = await insertProperty(trx, account.id, "Test Property");
        const device = await insertDevice(trx, "LIC", "TEST003");
        const startDate = new Date("2024-01-01");

        await insertPropertyDevice(
          trx,
          device.id,
          property.id,
          startDate,
          null
        );

        // Test with future date
        const result = await getCurrentPropertyForDevice(
          trx,
          "TEST003",
          "LIC",
          new Date("2025-01-01")
        );

        expect(result).toBeDefined();
        expect(result.id).toBe(property.id);
      });
    });
  });

  describe("getDeviceMetadataForProperty", () => {
    it("should return device metadata when device is associated with property at reference date", async () => {
      await runInTransaction(async (trx) => {
        // Create test data
        const user = await insertUser(trx);
        const account = await insertAccount(trx, user.id);
        const property = await insertProperty(trx, account.id, "Test Property");
        const deviceMetadata = { serial: "ABC123", firmware: "1.0.0" };
        const device = await insertDevice(trx, "LIC", "TEST004");

        // Update device with metadata
        await trx`UPDATE device SET metadata = ${deviceMetadata} WHERE id = ${device.id}`;

        const startDate = new Date("2024-01-01");
        const endDate = new Date("2024-12-31");

        await insertPropertyDevice(
          trx,
          device.id,
          property.id,
          startDate,
          endDate
        );

        // Test the function
        const result = await getDeviceMetadataForProperty(
          trx,
          "TEST004",
          "LIC",
          new Date("2024-06-01")
        );

        expect(result).toBeDefined();
        expect(result).toEqual(deviceMetadata);
      });
    });

    it("should return null when device has no metadata", async () => {
      await runInTransaction(async (trx) => {
        // Create test data
        const user = await insertUser(trx);
        const account = await insertAccount(trx, user.id);
        const property = await insertProperty(trx, account.id, "Test Property");
        const device = await insertDevice(trx, "LIC", "TEST005");
        const startDate = new Date("2024-01-01");
        const endDate = new Date("2024-12-31");

        await insertPropertyDevice(
          trx,
          device.id,
          property.id,
          startDate,
          endDate
        );

        // Test the function
        const result = await getDeviceMetadataForProperty(
          trx,
          "TEST005",
          "LIC",
          new Date("2024-06-01")
        );

        expect(result).toBeNull();
      });
    });

    it("should return undefined when device is not associated at reference date", async () => {
      await runInTransaction(async (trx) => {
        const result = await getDeviceMetadataForProperty(
          trx,
          "NONEXISTENT002",
          "LIC",
          new Date()
        );

        expect(result).toBeUndefined();
      });
    });
  });

  describe("getPropertyDeviceMetadataForDevice", () => {
    it("should return property_device metadata when device is associated with property at reference date", async () => {
      await runInTransaction(async (trx) => {
        // Create test data
        const user = await insertUser(trx);
        const account = await insertAccount(trx, user.id);
        const property = await insertProperty(trx, account.id, "Test Property");
        const device = await insertDevice(trx, "LIC", "TEST006");
        const startDate = new Date("2024-01-01");
        const endDate = new Date("2024-12-31");

        const propertyDevice = await insertPropertyDevice(
          trx,
          device.id,
          property.id,
          startDate,
          endDate
        );

        // Update property_device with metadata
        const pdMetadata = {
          installation_notes: "Installed on rooftop",
          calibration_date: "2024-01-15",
        };
        await trx`UPDATE property_device SET metadata = ${pdMetadata} WHERE id = ${propertyDevice.id}`;

        // Test the function
        const result = await getPropertyDeviceMetadataForDevice(
          trx,
          "TEST006",
          "LIC",
          new Date("2024-06-01")
        );

        expect(result).toBeDefined();
        expect(result).toEqual(pdMetadata);
      });
    });

    it("should return null when property_device has no metadata", async () => {
      await runInTransaction(async (trx) => {
        // Create test data
        const user = await insertUser(trx);
        const account = await insertAccount(trx, user.id);
        const property = await insertProperty(trx, account.id, "Test Property");
        const device = await insertDevice(trx, "LIC", "TEST007");
        const startDate = new Date("2024-01-01");
        const endDate = new Date("2024-12-31");

        await insertPropertyDevice(
          trx,
          device.id,
          property.id,
          startDate,
          endDate
        );

        // Test the function
        const result = await getPropertyDeviceMetadataForDevice(
          trx,
          "TEST007",
          "LIC",
          new Date("2024-06-01")
        );

        expect(result).toBeNull();
      });
    });

    it("should return undefined when device is not associated at reference date", async () => {
      await runInTransaction(async (trx) => {
        const result = await getPropertyDeviceMetadataForDevice(
          trx,
          "NONEXISTENT003",
          "LIC",
          new Date()
        );

        expect(result).toBeUndefined();
      });
    });
  });
});
