import { describe, it, expect } from "bun:test";
import {
  listProjectsByLICIdentifier,
  listFullProjectsByLICIdentifier,
} from "../src/db/queries/project-queries";
import { runInTransaction } from "./helpers/db";
import {
  insertAccount,
  insertProperty,
  insertDevice,
  insertPropertyDevice,
  insertProject,
  insertUser,
  insertWaterPump,
  insertSector,
} from "./helpers/fixtures";

describe("project-queries", () => {
  describe("listProjectsByLICIdentifier", () => {
    it("should return projects for LIC device associated and active at reference date", async () => {
      await runInTransaction(async (trx) => {
        const user = await insertUser(trx);
        const account = await insertAccount(trx, user.id);
        const property = await insertProperty(trx, account.id, "Test Property");
        const device = await insertDevice(trx, "LIC", "LIC001");
        const startDate = new Date("2024-01-01");
        const endDate = new Date("2024-12-31");
        await insertPropertyDevice(
          trx,
          device.id,
          property.id,
          startDate,
          endDate
        );
        const waterPump = await insertWaterPump(trx, property.id);
        const project = await insertProject(
          trx,
          property.id,
          device.id,
          waterPump.id,
          startDate,
          endDate
        );

        const result = await listProjectsByLICIdentifier(
          trx,
          "LIC001",
          new Date("2024-06-01")
        );
        expect(result).toBeDefined();
        expect(Array.isArray(result)).toBe(true);
        expect(result.length).toBeGreaterThan(0);
        expect(result[0]).toBeDefined();
        expect(result[0]?.id).toBe(project.id);
      }).catch((error) => {
        console.error("Test error:", error);
      });
    });

    it("should return empty array when device is not associated at reference date", async () => {
      await runInTransaction(async (trx) => {
        const result = await listProjectsByLICIdentifier(
          trx,
          "NONEXISTENTLIC",
          new Date()
        );
        expect(result).toEqual([]);
      });
    });

    it("should return empty array when project is not active at reference date", async () => {
      await runInTransaction(async (trx) => {
        const user = await insertUser(trx);
        const account = await insertAccount(trx, user.id);
        const property = await insertProperty(trx, account.id, "Test Property");
        const device = await insertDevice(trx, "LIC", "LIC002");
        const startDate = new Date("2024-01-01");
        const endDate = new Date("2024-12-31");
        await insertPropertyDevice(
          trx,
          device.id,
          property.id,
          startDate,
          endDate
        );
        const waterPump = await insertWaterPump(trx, property.id);
        await insertProject(
          trx,
          property.id,
          device.id,
          waterPump.id,
          startDate,
          endDate
        );

        // Test with date outside project period
        const result = await listProjectsByLICIdentifier(
          trx,
          "LIC002",
          new Date("2023-01-01")
        );
        expect(result).toEqual([]);
      });
    });

    it("should handle null end dates for ongoing associations and projects", async () => {
      await runInTransaction(async (trx) => {
        const user = await insertUser(trx);
        const account = await insertAccount(trx, user.id);
        const property = await insertProperty(trx, account.id, "Test Property");
        const device = await insertDevice(trx, "LIC", "LIC003");
        const startDate = new Date("2024-01-01");
        await insertPropertyDevice(
          trx,
          device.id,
          property.id,
          startDate,
          null
        );
        const waterPump = await insertWaterPump(trx, property.id);
        const project = await insertProject(
          trx,
          property.id,
          device.id,
          waterPump.id,
          startDate,
          null
        );

        const result = await listProjectsByLICIdentifier(
          trx,
          "LIC003",
          new Date("2025-01-01")
        );
        expect(result).toBeDefined();
        expect(Array.isArray(result)).toBe(true);
        expect(result.length).toBeGreaterThan(0);
        expect(result[0]?.id).toBe(project.id);
      });
    });
  });

  describe("listFullProjectsByLICIdentifier", () => {
    it("should return full projects with all nested data for LIC device", async () => {
      await runInTransaction(async (trx) => {
        const user = await insertUser(trx);
        const account = await insertAccount(trx, user.id);
        const property = await insertProperty(trx, account.id, "Test Property");

        // Create LIC device
        const licDevice = await insertDevice(trx, "LIC", "LIC-FULL-001");
        const startDate = new Date("2024-01-01");
        const endDate = new Date("2024-12-31");
        await insertPropertyDevice(
          trx,
          licDevice.id,
          property.id,
          startDate,
          endDate
        );

        // Create water pump controllers
        const irrigationController = await insertDevice(
          trx,
          "WPC-PL10",
          "WPC-IRR-001"
        );
        const fertigationController = await insertDevice(
          trx,
          "WPC-PL50",
          "WPC-FERT-001"
        );

        // Create water pumps
        const irrigationPump = await insertWaterPump(
          trx,
          property.id,
          "IRRIGATION",
          "Irrigation Pump",
          "IRR-PUMP-001"
        );
        const fertigationPump = await insertWaterPump(
          trx,
          property.id,
          "FERTIGATION",
          "Fertigation Pump",
          "FERT-PUMP-001"
        );

        // Update water pumps to reference their controllers
        await trx`
          UPDATE water_pump 
          SET water_pump_controller = ${irrigationController.id}
          WHERE id = ${irrigationPump.id}
        `;
        await trx`
          UPDATE water_pump 
          SET water_pump_controller = ${fertigationController.id}
          WHERE id = ${fertigationPump.id}
        `;

        // Create valve controllers
        const valveController1 = await insertDevice(trx, "VC", "VC-001");
        const valveController2 = await insertDevice(trx, "VC", "VC-002");
        await insertPropertyDevice(
          trx,
          valveController1.id,
          property.id,
          startDate,
          endDate
        );
        await insertPropertyDevice(
          trx,
          valveController2.id,
          property.id,
          startDate,
          endDate
        );

        // Create project
        const project = await insertProject(
          trx,
          property.id,
          licDevice.id,
          irrigationPump.id,
          startDate,
          endDate,
          "Full Test Project",
          fertigationPump.id
        );

        // Create sectors
        const _sector1 = await insertSector(
          trx,
          project.id,
          valveController1.id,
          "Sector 1",
          1
        );
        const _sector2 = await insertSector(
          trx,
          project.id,
          valveController2.id,
          "Sector 2",
          2
        );

        const result = await listFullProjectsByLICIdentifier(
          trx,
          "LIC-FULL-001",
          new Date("2024-06-01")
        );

        expect(result).toBeDefined();
        expect(Array.isArray(result)).toBe(true);
        expect(result.length).toBe(1);

        const fullProject = result[0];
        expect(fullProject?.id).toBe(project.id);
        expect(fullProject?.name).toBe("Full Test Project");

        // Check LIC device
        expect(fullProject?.localized_irrigation_controller).toBeDefined();
        expect(fullProject?.localized_irrigation_controller.identifier).toBe(
          "LIC-FULL-001"
        );
        expect(fullProject?.localized_irrigation_controller.model).toBe("LIC");

        // Check irrigation water pump with controller
        expect(fullProject?.irrigation_water_pump).toBeDefined();
        expect(fullProject?.irrigation_water_pump?.label).toBe(
          "Irrigation Pump"
        );
        expect(fullProject?.irrigation_water_pump?.pump_type).toBe(
          "IRRIGATION"
        );
        expect(fullProject?.irrigation_water_pump?.controller).toBeDefined();
        expect(fullProject?.irrigation_water_pump?.controller.identifier).toBe(
          "WPC-IRR-001"
        );
        expect(fullProject?.irrigation_water_pump?.controller.model).toBe(
          "WPC-PL10"
        );

        // Check fertigation water pump with controller
        expect(fullProject?.fertigation_water_pump).toBeDefined();
        expect(fullProject?.fertigation_water_pump?.label).toBe(
          "Fertigation Pump"
        );
        expect(fullProject?.fertigation_water_pump?.pump_type).toBe(
          "FERTIGATION"
        );
        expect(fullProject?.fertigation_water_pump?.controller).toBeDefined();
        expect(fullProject?.fertigation_water_pump?.controller.identifier).toBe(
          "WPC-FERT-001"
        );
        expect(fullProject?.fertigation_water_pump?.controller.model).toBe(
          "WPC-PL50"
        );

        // Check sectors with valve controllers
        expect(fullProject?.sectors).toBeDefined();
        expect(Array.isArray(fullProject?.sectors)).toBe(true);
        expect(fullProject?.sectors.length).toBe(2);

        const sectors = fullProject?.sectors;
        const sectorNames = sectors?.map((s) => s.name).sort();
        expect(sectorNames).toEqual(["Sector 1", "Sector 2"]);

        // Check valve controller devices
        sectors?.forEach((sector) => {
          expect(sector.valve_controller_device).toBeDefined();
          expect(sector.valve_controller_device.model).toBe("VC");
          expect(["VC-001", "VC-002"]).toContain(
            sector.valve_controller_device.identifier
          );
        });
      });
    });

    it("should return project with null fertigation pump when not present", async () => {
      await runInTransaction(async (trx) => {
        const user = await insertUser(trx);
        const account = await insertAccount(trx, user.id);
        const property = await insertProperty(trx, account.id, "Test Property");

        const licDevice = await insertDevice(trx, "LIC", "LIC-FULL-002");
        const startDate = new Date("2024-01-01");
        await insertPropertyDevice(
          trx,
          licDevice.id,
          property.id,
          startDate,
          null
        );

        const irrigationPump = await insertWaterPump(trx, property.id);
        const _project = await insertProject(
          trx,
          property.id,
          licDevice.id,
          irrigationPump.id,
          startDate,
          null,
          "Project without Fertigation"
        );

        const result = await listFullProjectsByLICIdentifier(
          trx,
          "LIC-FULL-002",
          new Date("2024-06-01")
        );

        expect(result).toBeDefined();
        expect(result.length).toBe(1);

        const fullProject = result[0];
        expect(fullProject?.fertigation_water_pump).toBeNull();
        expect(fullProject?.irrigation_water_pump).toBeDefined();
      });
    });

    it("should return empty sectors array when no sectors exist", async () => {
      await runInTransaction(async (trx) => {
        const user = await insertUser(trx);
        const account = await insertAccount(trx, user.id);
        const property = await insertProperty(trx, account.id, "Test Property");

        const licDevice = await insertDevice(trx, "LIC", "LIC-FULL-003");
        const startDate = new Date("2024-01-01");
        await insertPropertyDevice(
          trx,
          licDevice.id,
          property.id,
          startDate,
          null
        );

        const irrigationPump = await insertWaterPump(trx, property.id);
        await insertProject(
          trx,
          property.id,
          licDevice.id,
          irrigationPump.id,
          startDate,
          null,
          "Project without Sectors"
        );

        const result = await listFullProjectsByLICIdentifier(
          trx,
          "LIC-FULL-003",
          new Date("2024-06-01")
        );

        expect(result).toBeDefined();
        expect(result.length).toBe(1);

        const fullProject = result[0];
        expect(fullProject?.sectors).toBeDefined();
        expect(Array.isArray(fullProject?.sectors)).toBe(true);
        expect(fullProject?.sectors.length).toBe(0);
      });
    });

    it("should return empty array when no projects match LIC identifier", async () => {
      await runInTransaction(async (trx) => {
        const result = await listFullProjectsByLICIdentifier(
          trx,
          "NONEXISTENT-LIC",
          new Date()
        );
        expect(result).toEqual([]);
      });
    });

    it("should handle multiple projects for same LIC device", async () => {
      await runInTransaction(async (trx) => {
        const user = await insertUser(trx);
        const account = await insertAccount(trx, user.id);
        const property = await insertProperty(trx, account.id, "Test Property");

        const licDevice = await insertDevice(trx, "LIC", "LIC-FULL-004");
        const startDate = new Date("2024-01-01");
        await insertPropertyDevice(
          trx,
          licDevice.id,
          property.id,
          startDate,
          null
        );

        // Create separate water pumps for each project to avoid constraint violation
        const irrigationPump1 = await insertWaterPump(
          trx,
          property.id,
          "IRRIGATION",
          "Pump 1"
        );
        const irrigationPump2 = await insertWaterPump(
          trx,
          property.id,
          "IRRIGATION",
          "Pump 2"
        );

        // Create two projects with different water pumps
        await insertProject(
          trx,
          property.id,
          licDevice.id,
          irrigationPump1.id,
          startDate,
          null,
          "Project 1"
        );
        await insertProject(
          trx,
          property.id,
          licDevice.id,
          irrigationPump2.id,
          startDate,
          null,
          "Project 2"
        );

        const result = await listFullProjectsByLICIdentifier(
          trx,
          "LIC-FULL-004",
          new Date("2024-06-01")
        );

        expect(result).toBeDefined();
        expect(result.length).toBe(2);
        expect(result.map((p) => p.name).sort()).toEqual([
          "Project 1",
          "Project 2",
        ]);
      });
    });

    it("should filter projects by reference date correctly", async () => {
      await runInTransaction(async (trx) => {
        const user = await insertUser(trx);
        const account = await insertAccount(trx, user.id);
        const property = await insertProperty(trx, account.id, "Test Property");

        const licDevice = await insertDevice(trx, "LIC", "LIC-FULL-005");
        await insertPropertyDevice(
          trx,
          licDevice.id,
          property.id,
          new Date("2024-01-01"),
          null
        );

        // Create separate water pumps to avoid constraint violations
        const irrigationPump1 = await insertWaterPump(
          trx,
          property.id,
          "IRRIGATION",
          "Early Pump"
        );
        const irrigationPump2 = await insertWaterPump(
          trx,
          property.id,
          "IRRIGATION",
          "Late Pump"
        );

        // Create projects with different date ranges and different pumps
        await insertProject(
          trx,
          property.id,
          licDevice.id,
          irrigationPump1.id,
          new Date("2024-01-01"),
          new Date("2024-06-30"),
          "Early Project"
        );
        await insertProject(
          trx,
          property.id,
          licDevice.id,
          irrigationPump2.id,
          new Date("2024-07-01"),
          new Date("2024-12-31"),
          "Late Project"
        );

        // Test with date that should match only the early project
        const resultEarly = await listFullProjectsByLICIdentifier(
          trx,
          "LIC-FULL-005",
          new Date("2024-03-01")
        );

        expect(resultEarly.length).toBe(1);
        expect(resultEarly[0]?.name).toBe("Early Project");

        // Test with date that should match only the late project
        const resultLate = await listFullProjectsByLICIdentifier(
          trx,
          "LIC-FULL-005",
          new Date("2024-09-01")
        );

        expect(resultLate.length).toBe(1);
        expect(resultLate[0]?.name).toBe("Late Project");

        // Test with date that shouldn't match any project
        const resultNone = await listFullProjectsByLICIdentifier(
          trx,
          "LIC-FULL-005",
          new Date("2023-01-01")
        );

        expect(resultNone.length).toBe(0);
      });
    });
  });
});
