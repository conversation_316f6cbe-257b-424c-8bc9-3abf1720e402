#!/bin/bash

# PWA Icon Generator Script
# This script generates all the required icon sizes for your PWA
# You'll need ImageMagick installed: sudo apt install imagemagick (Linux) or brew install imagemagick (Mac)

# Set source image path - replace with your actual logo file
SOURCE_IMAGE="../../src/assets/logo-symbol-transparent-180x219-removebg.png"
OUTPUT_DIR="."

# Check if source image exists
if [ ! -f "$SOURCE_IMAGE" ]; then
    echo "❌ Source image not found: $SOURCE_IMAGE"
    echo "Please update the SOURCE_IMAGE path in this script to point to your logo file"
    exit 1
fi

# Check if ImageMagick is installed
if ! command -v convert &> /dev/null; then
    echo "❌ ImageMagick not found. Please install it:"
    echo "  Ubuntu/Debian: sudo apt install imagemagick"
    echo "  macOS: brew install imagemagick"
    echo "  Windows: Download from https://imagemagick.org/script/download.php"
    exit 1
fi

echo "🎨 Generating PWA icons from $SOURCE_IMAGE..."

# Array of icon sizes needed for PWA
sizes=(72 96 128 144 152 192 384 512)

# Generate each icon size
for size in "${sizes[@]}"; do
    output_file="icon-${size}x${size}.png"
    echo "  📱 Generating ${output_file}..."
    
    # Use ImageMagick to resize and add padding if needed
    convert "$SOURCE_IMAGE" \
        -background transparent \
        -fill none \
        -resize "${size}x${size}" \
        -gravity center \
        -extent "${size}x${size}" \
        "$OUTPUT_DIR/$output_file"
    
    if [ $? -eq 0 ]; then
        echo "    ✅ Created $output_file"
    else
        echo "    ❌ Failed to create $output_file"
    fi
done

echo ""
echo "🎉 Icon generation complete!"
echo "📁 Icons saved in: $OUTPUT_DIR"
echo ""
echo "📋 Next steps:"
echo "  1. Review the generated icons in the icons folder"
echo "  2. Optionally create custom icons for better branding"
echo "  3. Generate screenshots for the manifest (take screenshots of your app)"
echo "  4. Test your PWA installation on mobile devices"
