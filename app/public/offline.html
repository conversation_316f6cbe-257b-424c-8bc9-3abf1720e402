<!DOCTYPE html>
<html lang="pt-BR">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Offline - Irriga+</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
          sans-serif;
        background: linear-gradient(135deg, #16a34a 0%, #15803d 100%);
        color: white;
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20px;
      }

      .container {
        text-align: center;
        max-width: 400px;
      }

      .icon {
        width: 80px;
        height: 80px;
        margin: 0 auto 30px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 36px;
      }

      h1 {
        font-size: 28px;
        margin-bottom: 16px;
        font-weight: 600;
      }

      p {
        font-size: 16px;
        line-height: 1.5;
        margin-bottom: 30px;
        opacity: 0.9;
      }

      .retry-btn {
        background: white;
        color: #16a34a;
        border: none;
        padding: 12px 24px;
        border-radius: 8px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: transform 0.2s;
      }

      .retry-btn:hover {
        transform: translateY(-2px);
      }

      .features {
        margin-top: 40px;
        text-align: left;
      }

      .feature {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        font-size: 14px;
        opacity: 0.8;
      }

      .feature-icon {
        width: 20px;
        height: 20px;
        margin-right: 12px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="icon">📡</div>

      <h1>Você está offline</h1>
      <p>
        Não foi possível conectar com o servidor. Verifique sua conexão com a
        internet e tente novamente.
      </p>

      <button class="retry-btn" onclick="window.location.reload()">
        Tentar Novamente
      </button>

      <div class="features">
        <div class="feature">
          <div class="feature-icon">📱</div>
          <span>O app funciona offline para funcionalidades básicas</span>
        </div>
        <div class="feature">
          <div class="feature-icon">💾</div>
          <span>Suas ações serão sincronizadas quando voltar online</span>
        </div>
        <div class="feature">
          <div class="feature-icon">🔄</div>
          <span>Dados em cache estão disponíveis</span>
        </div>
      </div>
    </div>

    <script>
      // Auto-retry when connection is restored
      window.addEventListener("online", () => {
        window.location.reload();
      });

      // Check connection periodically
      setInterval(() => {
        if (navigator.onLine) {
          fetch("/")
            .then(() => {
              window.location.reload();
            })
            .catch(() => {
              // Still offline
            });
        }
      }, 30000); // Check every 30 seconds
    </script>
  </body>
</html>
