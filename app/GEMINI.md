# Gemini Project Brief: <PERSON><PERSON><PERSON>

This document provides the essential context for the Gemini AI assistant to effectively contribute to the Irriga Mais project.

## 1. Project Overview & Domain

Irriga Mais is a Progressive Web App (PWA) for managing agricultural irrigation systems. It's built for mobile-first use by small to medium property owners. The system automates irrigation using a combination of a central controller and remote hardware.

A detailed description of the project's purpose, target audience, and the underlying data model (Accounts, Properties, Projects, Sectors, etc.) can be found in the following documents:

- `docs/000-DESCRIPTION.md`
- `docs/001-ENTITIES.md`

## 2. Tech Stack

The project uses a modern tech stack centered around Bun, React, and TypeScript.

For a complete and detailed breakdown of the technology stack, architecture, and dependencies, please refer to:

- `.github/instructions/tech-stack.instructions.md`

## 3. Development Commands

- **Install Dependencies**: `bun install`
- **Run Dev Server**: `bun run dev`
- **Build for Production**: `bun run build`
- **Run Production Server**: `bun run start`

## 4. Architecture & Structure

The source code is organized in the `src/` directory, with clear separation for API services, reusable components, pages, state management, and hooks.

- **Path Aliases**: `@/*` is mapped to `./src/*`.
- **Custom UI Components**: The project uses custom `Modal`, `ConfirmModal`, and `Toast` components to replace native browser dialogs. Their usage is documented in `docs/003-MODAL_COMPONENTS.md`.

## 5. Key User Flows

The application has well-defined user flows for core processes like authentication, account selection, and property management.

For a visual and descriptive breakdown of these flows, please consult:

- `docs/002-USER_FLOW.md`
- `docs/005-AUTH_AND_ACCOUNT_SELECTION.md`

## 6. Design System & UI/UX Rules

All UI/UX development must strictly adhere to the project's design system. This includes a mobile-first approach and consistent use of the defined color palette, typography, and spacing.

The primary sources of truth for design and UI implementation are:

- **Design Tokens & Specs**: `docs/design.json`
- **Implementation Rules**: `.github/instructions/design.instructions.md`

## 7. Coding Standards

The project enforces a strict set of coding standards for TypeScript, React, code style, and testing to ensure code quality and consistency.

All contributions must follow the guidelines detailed in:

- `.github/instructions/coding.instructions.md`
- `.github/instructions/react.instructions.md`
