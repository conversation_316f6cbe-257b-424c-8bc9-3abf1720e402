// Directus SDK client setup for Quasar + Pinia + TypeScript

import type { PropertyDevice } from "@/api/model/property-device";
import type { WaterPump } from "@/api/model/water-pump";
import { authentication, createDirectus, rest } from "@directus/sdk";
import type { Account } from "./model/account";
import type { AccountUser } from "./model/account-user";
import type { Device } from "./model/device";
import type { IrrigationPlan } from "./model/irrigation-plan";
import type { IrrigationPlanStep } from "./model/irrigation-plan-step";
import type { MeshDeviceMapping } from "./model/mesh-device-mapping";
import type { Project } from "./model/project";
import type { Property } from "./model/property";
import type { Reservoir } from "./model/reservoir";
import type { Sector } from "./model/sector";
import type { User } from "./model/user";

// Aggregate schema from all model types
export interface AppSchema {
  user: User[];
  account: Account[];
  account_user: AccountUser[];
  device: Device[];
  irrigation_plan: IrrigationPlan[];
  mesh_device_mapping: MeshDeviceMapping[];
  project: Project[];
  property: Property[];
  property_device: PropertyDevice[];
  irrigation_plan_step: IrrigationPlanStep[];
  reservoir: Reservoir[];
  sector: Sector[];
  water_pump: WaterPump[];
}

// Custom localStorage adapter for Directus SDK authentication
class LocalStorage {
  get() {
    const data = localStorage.getItem("directus-auth-data");
    return data ? JSON.parse(data) : null;
  }
  set(value: unknown) {
    if (value === null) {
      localStorage.removeItem("directus-auth-data");
    } else {
      localStorage.setItem("directus-auth-data", JSON.stringify(value));
    }
  }
}

// Create the Directus client instance
// const apiURL = `${window.location.origin}/api`;
const apiURL =
  window.location.port === "1234"
    ? `${window.location.origin.replace(window.location.port, "8055")}`
    : `${window.location.origin}/api`;

console.log("Directus API URL:", apiURL); // For debugging purposes

export type AppDirectusClient = ReturnType<typeof createDirectusInstance>;

export function createDirectusInstance(url = apiURL) {
  console.log("Creating Directus instance for", url);
  return createDirectus<AppSchema>(url)
    .with(rest())
    .with(authentication("json", { storage: new LocalStorage() }));
}
