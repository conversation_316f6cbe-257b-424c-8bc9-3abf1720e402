import {
  createItem,
  deleteItem,
  NestedPartial,
  readAssetRaw,
  readItem,
  readMe,
  updateItem,
  updateItemsBatch,
} from "@directus/sdk";

import type { OmitAndMerge } from "@/utils/types";
import type { AppDirectusClient, AppSchema } from "./client";
import { createDirectusInstance } from "./client";
import { getRequestUrl } from "./get-request-url";
import { MeshDeviceMapping } from "./model/mesh-device-mapping";
import { Project } from "./model/project";
import { Property } from "./model/property";
import { PropertyDevice } from "./model/property-device";
import { Reservoir } from "./model/reservoir";
import {
  AccountUserTree,
  AccountUserWithAccount,
  getAccountUserTree,
  listAccountsWithUsers,
} from "./queries/account";

type CollectionKey = keyof AppSchema;
type CollectionItem<K extends CollectionKey = CollectionKey> =
  AppSchema[K][number];

type CRUD<Item extends CollectionItem> = {
  create: (data: NestedPartial<Item>) => Promise<Item>;
  update: (id: string, data: Partial<Item>) => Promise<Item>;
  updateBatch: (items: Partial<Item>[]) => Promise<Item[]>;
  getOne: (id: string) => Promise<Item>;
  del: (id: string) => Promise<void>;
};

class DirectusApiService {
  private static instance: DirectusApiService;

  private constructor(protected readonly client: AppDirectusClient) {}

  public static getInstance(
    client: AppDirectusClient = createDirectusInstance()
  ): DirectusApiService {
    if (!DirectusApiService.instance) {
      DirectusApiService.instance = new DirectusApiService(client);
    }
    return DirectusApiService.instance;
  }

  protected crud<
    C extends CollectionKey,
    Item extends CollectionItem<C> = CollectionItem<C>
  >(collection: C): CRUD<Item> {
    const crud: CRUD<Item> = {
      create: async (data: NestedPartial<Item>) => {
        return (await this.client.request(
          createItem(collection, data as any, { fields: ["*"] })
        )) as Item;
      },
      update: async (id: string, data: Partial<Item>) => {
        return (await this.client.request(
          updateItem(collection, id, data, { fields: ["*"] })
        )) as Item;
      },
      updateBatch: async (items: Partial<Item>[]) => {
        return (await this.client.request(
          updateItemsBatch(collection, items as any, { fields: ["*"] })
        )) as Item[];
      },
      getOne: async (id: string) => {
        return (await this.client.request(
          readItem(collection, id, {
            fields: ["*"],
          })
        )) as Item;
      },
      del: async (id: string) => {
        return await this.client.request(deleteItem(collection, id));
      },
    };
    return crud;
  }

  public readonly asset = {
    resolveUrl: (assetId: string) => {
      if (!this.client) throw new Error("Directus client is not initialized");
      const asset = readAssetRaw(assetId);
      const requestOptions = asset();
      return getRequestUrl(
        this.client.url,
        requestOptions.path,
        requestOptions.params
      );
    },
  };

  public readonly account = {
    listAccountsWithUsers: async (): Promise<AccountUserWithAccount[]> => {
      return await listAccountsWithUsers(this.client);
    },
    getAccountUserTree: async (
      accountUserId: string
    ): Promise<AccountUserTree> => {
      return await getAccountUserTree(this.client, accountUserId);
    },
  };

  public readonly auth = {
    login: async (email: string, password: string) => {
      return await this.client.login({ email, password });
    },
    logout: async () => {
      return await this.client.logout();
    },
    fetchUser: async () => {
      const token = await this.client.getToken();
      if (!token) throw new Error("No token found");
      // Try to fetch the user using the SDK (assuming "users" collection)
      const rawUser = await this.client.request(
        readMe({
          fields: ["email", "first_name", "last_name", "avatar", "id", "role"], // Adjust fields as needed
        })
      );
      return (
        (rawUser as OmitAndMerge<
          typeof rawUser,
          "avatar" | "role",
          { avatar: string | null; role: string | null }
        >) ?? null
      );
    },
    getToken: async () => {
      return await this.client.getToken();
    },
  };

  public readonly project: CRUD<Project> = this.crud("project");

  public readonly property: CRUD<Property> = this.crud("property");

  public readonly propertyDevice: CRUD<PropertyDevice> =
    this.crud("property_device");

  public readonly device = this.crud("device");

  public readonly reservoir: CRUD<Reservoir> = this.crud("reservoir");

  public readonly sector = this.crud("sector");

  public readonly waterPump = this.crud("water_pump");

  public readonly irrigationPlan = this.crud("irrigation_plan");

  public readonly meshDeviceMapping: CRUD<MeshDeviceMapping> = this.crud(
    "mesh_device_mapping"
  );

  public readonly irrigationPlanStep = {
    ...this.crud("irrigation_plan_step"),
    swapOrders: async (
      step1: Pick<CollectionItem<"irrigation_plan_step">, "id" | "order">,
      step2: Pick<CollectionItem<"irrigation_plan_step">, "id" | "order">
    ) => {
      return await this.client.request(
        updateItemsBatch("irrigation_plan_step", [
          { id: step1.id, order: step2.order },
          { id: step2.id, order: step1.order },
        ])
      );
    },
  };
}

export const apiService = DirectusApiService.getInstance();
