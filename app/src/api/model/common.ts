export type AnyJson = ReturnType<typeof JSON.parse>;

export const DAY_OF_WEEK_VALUES = ['MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT', 'SUN'] as const;
export type DayOfWeek = (typeof DAY_OF_WEEK_VALUES)[number];

export interface Model {
  id: string;
  date_created: string;
  user_created: string | null;
  date_updated: string;
  user_updated: string | null;
  // eslint-disable-next-line @typescript-eslint/no-redundant-type-constituents
  metadata: AnyJson | null;
  notes: string | null;
}
