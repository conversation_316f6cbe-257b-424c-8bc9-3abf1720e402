import type {
  DirectusRelationArrayType,
  DirectusRelationFieldType,
} from "@/utils/types";
import type { Polygon } from "geojson";
import type { Model } from "./common";
import type { Device } from "./device";
import type { IrrigationPlanStep } from "./irrigation-plan-step";
import type { Project } from "./project";

export const VALVE_CONTROLLER_OUTPUT_VALUES = [1, 2, 3, 4] as const;
export type ValveControllerOutput =
  (typeof VALVE_CONTROLLER_OUTPUT_VALUES)[number];

export type SectorRelationsTypes = {
  project: DirectusRelationFieldType<Project>;
  steps: DirectusRelationFieldType<IrrigationPlanStep>;
  valve_controller: DirectusRelationFieldType<Device>;
};

export type SectorDefaultRelationsTypes = SectorRelationsTypes;

export interface Sector<
  Types extends Partial<SectorRelationsTypes> = SectorDefaultRelationsTypes
> extends Model {
  project: Types["project"];
  name: string;
  description: string | null;
  valve_controller: Types["valve_controller"];
  valve_controller_output: ValveControllerOutput;
  area: number | null;
  polygon: Polygon | null;
  steps: DirectusRelationArrayType<Types["steps"]>;
}
