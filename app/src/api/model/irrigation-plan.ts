import type {
  DirectusRelationArrayType,
  DirectusRelationFieldType,
} from "@/utils/types";
import type { DayOfWeek, Model } from "./common";
import type { IrrigationPlanStep } from "./irrigation-plan-step";
import type { Project } from "./project";

export type PlanRelationsTypes = {
  project: DirectusRelationFieldType<Project>;
  steps: DirectusRelationFieldType<IrrigationPlanStep>;
};

export type PlanDefaultRelationsTypes = {
  project: DirectusRelationFieldType<Project>;
  steps: DirectusRelationFieldType<IrrigationPlanStep>;
};

export interface IrrigationPlan<
  Types extends Partial<PlanRelationsTypes> = PlanDefaultRelationsTypes,
> extends Model {
  project: Types["project"];
  name: string;
  description: string | null;
  start_time: string;
  days_of_week: DayOfWeek[];
  is_enabled: boolean;
  fertigation_enabled: boolean;
  backwash_enabled: boolean;
  total_irrigation_duration: number;
  start_date: string | null;
  end_date: string | null;
  steps: DirectusRelationArrayType<Types["steps"]>;
}
