// Auto-generated from Directus schema

import type { DirectusRelationFieldType } from "@/utils/types";
import type { Account } from "./account";
import type { User } from "./user";
import type { Model } from "./common";

export type AccountUserRelationsTypes = {
  account: DirectusRelationFieldType<Account>;
  user: DirectusRelationFieldType<User>;
};

export type AccountUserDefaultRelationsTypes = {
  account: DirectusRelationFieldType<Account>;
  user: DirectusRelationFieldType<User>;
};

export type AccountAccessLevel = "admin" | "user" | "guest";

export interface AccountUser<
  Types extends Partial<AccountUserRelationsTypes> = AccountUserDefaultRelationsTypes
> extends Model {
  account: Types["account"];
  user: Types["user"];
  role: AccountAccessLevel;
  start_date: string;
  end_date: string | null;
}
