import type { Account } from "./account";
import type {
  DirectusRelationArrayType,
  DirectusRelationFieldType,
} from "@/utils/types";
import type { AccountUser } from "./account-user";

export type UserRelationsTypes = {
  account: DirectusRelationFieldType<Account>;
  accounts: DirectusRelationFieldType<AccountUser>;
};

export type UserDefaultRelationsTypes = {
  account: DirectusRelationFieldType<Account>;
  accounts: DirectusRelationFieldType<AccountUser>;
};

export interface User<
  Types extends UserRelationsTypes = UserDefaultRelationsTypes
> {
  id: string;
  first_name: string | null;
  last_name: string | null;
  email: string | null;
  password: string | null;
  location: string | null;
  title: string | null;
  description: string | null;
  tags: string[] | null;
  avatar: string | null;
  language: string | null;
  theme: string | null;
  tfa_secret: string | null;
  status: string;
  role: string | null;
  account: DirectusRelationArrayType<Types["account"]>;
  accounts: DirectusRelationArrayType<Types["accounts"]>;
}
