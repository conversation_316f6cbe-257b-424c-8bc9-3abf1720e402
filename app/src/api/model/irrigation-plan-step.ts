import type { DirectusRelationFieldType } from "@/utils/types";
import type { Model } from "./common";
import type { IrrigationPlan } from "./irrigation-plan";
import type { Sector } from "./sector";

export type SectorIrrigationStepRelationsTypes = {
  irrigation_plan: DirectusRelationFieldType<IrrigationPlan>;
  sector: DirectusRelationFieldType<Sector>;
};

export type SectorIrrigationStepDefaultRelationsTypes =
  SectorIrrigationStepRelationsTypes;

export interface IrrigationPlanStep<
  Types extends Partial<SectorIrrigationStepRelationsTypes> = SectorIrrigationStepDefaultRelationsTypes
> extends Model {
  irrigation_plan: Types["irrigation_plan"];
  sector: Types["sector"];
  order: number;
  duration_seconds: number;
  fertigation_start_delay_seconds: number | null;
  fertigation_duration_seconds: number | null;
  description: string | null;
}
