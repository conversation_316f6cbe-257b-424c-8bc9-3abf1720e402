import type {
  DirectusRelationArrayType,
  DirectusRelationFieldType,
} from "@/utils/types";
import type { Model } from "./common";
import type { Device } from "./device";
import type { IrrigationPlan } from "./irrigation-plan";
import type { Property } from "./property";
import type { Sector } from "./sector";
import type { WaterPump } from "./water-pump";

export type ProjectRelationsTypes = {
  property: DirectusRelationFieldType<Property>;
  irrigation_water_pump: DirectusRelationFieldType<WaterPump>;
  fertigation_water_pump: DirectusRelationFieldType<WaterPump>;
  localized_irrigation_controller: DirectusRelationFieldType<Device>;
  irrigation_plans: DirectusRelationFieldType<IrrigationPlan>;
  sectors: DirectusRelationFieldType<Sector>;
};

export type ProjectDefaultRelationsTypes = ProjectRelationsTypes;

export interface Project<
  Types extends Partial<ProjectRelationsTypes> = ProjectDefaultRelationsTypes
> extends Model {
  name: string;
  description: string | null;
  pipe_wash_time_seconds: number | null;
  backwash_duration_seconds: number | null;
  backwash_period_seconds: number | null;
  start_date: string | null;
  end_date: string | null;
  property: Types["property"];
  irrigation_water_pump: Types["irrigation_water_pump"];
  fertigation_water_pump: Types["fertigation_water_pump"] | null;
  localized_irrigation_controller: Types["localized_irrigation_controller"];
  irrigation_plans: DirectusRelationArrayType<Types["irrigation_plans"]>;
  sectors: DirectusRelationArrayType<Types["sectors"]>;
}
