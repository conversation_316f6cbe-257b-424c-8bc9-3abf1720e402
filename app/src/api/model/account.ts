import type { Model } from "./common";
import type { Property } from "./property";
import type { User } from "./user";
import type {
  DirectusRelationArrayType,
  DirectusRelationFieldType,
} from "@/utils/types";

export type AccountRelationsTypes = {
  owner: DirectusRelationFieldType<User>;
  properties: DirectusRelationFieldType<Property>; // o2m relation - allows any Property type
};

export type AccountDefaultRelationsTypes = {
  owner: DirectusRelationFieldType<User>;
  properties: DirectusRelationFieldType<Property>;
};

export interface Account<
  Types extends Partial<AccountRelationsTypes> = AccountDefaultRelationsTypes
> extends Model {
  owner: Types["owner"]; // m2o relation
  properties: DirectusRelationArrayType<Types["properties"]>; // o2m relation
}
