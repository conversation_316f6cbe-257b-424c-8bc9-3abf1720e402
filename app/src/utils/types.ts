export type PickAndMerge<T, <PERSON><PERSON><PERSON><PERSON> extends keyof T, Merge> = Omit<
  Pick<T, PickKeys>,
  keyof Merge
> &
  Merge;
export type OmitAndMerge<T, Omit<PERSON><PERSON><PERSON> extends keyof T, Merge> = Omit<
  Omit<T, OmitKeys>,
  keyof Merge
> &
  Merge;
export type SomeRequired<T, K extends keyof T> = Omit<T, K> &
  Required<Pick<T, K>>;
export type SomeOptional<T, K extends keyof T> = Omit<T, K> &
  Partial<Pick<T, K>>;
export type DirectusRelationFieldType<T> = string | T;
export type DirectusRelationArrayType<R> = R extends string ? string[] : R[];
