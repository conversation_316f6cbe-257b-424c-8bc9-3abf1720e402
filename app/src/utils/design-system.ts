/**
 * Design System Utility Classes
 *
 * This file provides utility classes and constants that map to the design system
 * defined in docs/design.json. Use these instead of hardcoded Tailwind classes
 * to ensure consistency across the application.
 */

// Color classes based on design system
export const designColors = {
  // Primary colors (green palette)
  primary: {
    50: "text-green-50 bg-green-50 border-green-50",
    100: "text-green-100 bg-green-100 border-green-100",
    200: "text-green-200 bg-green-200 border-green-200",
    300: "text-green-300 bg-green-300 border-green-300",
    400: "text-green-400 bg-green-400 border-green-400",
    500: "text-green-500 bg-green-500 border-green-500", // primary
    600: "text-green-600 bg-green-600 border-green-600",
    700: "text-green-700 bg-green-700 border-green-700",
    800: "text-green-800 bg-green-800 border-green-800",
    900: "text-green-900 bg-green-900 border-green-900",
  },

  // Neutral colors (gray palette)
  neutral: {
    50: "text-neutral-50 bg-neutral-50 border-neutral-50",
    100: "text-neutral-100 bg-neutral-100 border-neutral-100",
    200: "text-neutral-200 bg-neutral-200 border-neutral-200",
    300: "text-neutral-300 bg-neutral-300 border-neutral-300",
    400: "text-neutral-400 bg-neutral-400 border-neutral-400",
    500: "text-neutral-500 bg-neutral-500 border-neutral-500",
    600: "text-neutral-600 bg-neutral-600 border-neutral-600",
    700: "text-neutral-700 bg-neutral-700 border-neutral-700",
    800: "text-neutral-800 bg-neutral-800 border-neutral-800",
    900: "text-neutral-900 bg-neutral-900 border-neutral-900",
  },

  // Semantic colors
  semantic: {
    success: "text-green-500 bg-green-500 border-green-500",
    warning: "text-amber-500 bg-amber-500 border-amber-500",
    error: "text-red-500 bg-red-500 border-red-500",
    info: "text-blue-500 bg-blue-500 border-blue-500",
  },
} as const;

// Spacing classes based on design system (4px, 8px, 16px, 24px, 32px, 48px, 64px)
export const designSpacing = {
  xs: "p-1 px-1 py-1 m-1 mx-1 my-1 gap-1 space-x-1 space-y-1", // 4px
  sm: "p-2 px-2 py-2 m-2 mx-2 my-2 gap-2 space-x-2 space-y-2", // 8px
  md: "p-4 px-4 py-4 m-4 mx-4 my-4 gap-4 space-x-4 space-y-4", // 16px
  lg: "p-6 px-6 py-6 m-6 mx-6 my-6 gap-6 space-x-6 space-y-6", // 24px
  xl: "p-8 px-8 py-8 m-8 mx-8 my-8 gap-8 space-x-8 space-y-8", // 32px
  "2xl": "p-12 px-12 py-12 m-12 mx-12 my-12 gap-12 space-x-12 space-y-12", // 48px
  "3xl": "p-16 px-16 py-16 m-16 mx-16 my-16 gap-16 space-x-16 space-y-16", // 64px
} as const;

// Border radius classes based on design system
export const designBorderRadius = {
  sm: "rounded", // 4px
  md: "rounded-lg", // 8px
  lg: "rounded-xl", // 12px
  xl: "rounded-2xl", // 16px
  "2xl": "rounded-3xl", // 24px
  full: "rounded-full", // 9999px
} as const;

// Typography classes based on design system
export const designTypography = {
  fontSize: {
    xs: "text-xs", // 12px
    sm: "text-sm", // 14px
    base: "text-base", // 16px
    lg: "text-lg", // 18px
    xl: "text-xl", // 20px
    "2xl": "text-2xl", // 24px
    "3xl": "text-3xl", // 30px
    "4xl": "text-4xl", // 36px
  },
  fontWeight: {
    light: "font-light", // 300
    normal: "font-normal", // 400
    medium: "font-medium", // 500
    semibold: "font-semibold", // 600
    bold: "font-bold", // 700
  },
  lineHeight: {
    tight: "leading-tight", // 1.2
    normal: "leading-normal", // 1.5
    relaxed: "leading-relaxed", // 1.75
  },
} as const;

// Component-specific design system classes
export const designComponents = {
  // Button variants following design system
  button: {
    primary:
      "bg-green-600 hover:bg-green-700 active:bg-green-800 disabled:bg-neutral-300 disabled:text-neutral-500 text-white font-medium rounded-xl min-h-12 transition-all duration-150 ease-in-out",
    secondary:
      "bg-transparent border border-green-500 text-green-500 hover:bg-green-50 active:bg-green-100 disabled:bg-neutral-300 disabled:text-neutral-500 disabled:border-neutral-300 font-medium rounded-xl min-h-12 transition-all duration-150 ease-in-out",
    ghost:
      "bg-transparent text-green-500 hover:bg-green-50 active:bg-green-100 disabled:text-neutral-400 font-medium rounded-xl min-h-12 transition-all duration-150 ease-in-out",
  },

  // Input variants following design system
  input: {
    default:
      "w-full px-4 py-3 border border-neutral-200 rounded-xl text-base focus:ring-2 focus:ring-green-500/20 focus:border-green-500 transition-all duration-150 ease-in-out min-h-12 bg-white",
    error:
      "w-full px-4 py-3 border border-red-500 rounded-xl text-base focus:ring-2 focus:ring-red-500/20 focus:border-red-500 transition-all duration-150 ease-in-out min-h-12 bg-white",
  },

  // Card variants following design system
  card: {
    default: "bg-white rounded-xl border border-neutral-100 p-6 shadow-md",
    elevated: "bg-white rounded-xl p-6 shadow-lg",
  },

  // Navigation components
  navigation: {
    header: "bg-white border-b border-neutral-100 px-6 py-4 min-h-14",
    tabBar: "bg-white border-t border-neutral-100 px-6 py-2 min-h-15",
  },
} as const;

// Animation classes following design system
export const designAnimations = {
  transition: {
    fast: "transition-all duration-150 ease-in-out",
    normal: "transition-all duration-300 ease-in-out",
    slow: "transition-all duration-500 ease-in-out",
  },
} as const;

// Helper function to get design system compliant classes
export function getDesignClass(
  category: keyof typeof designComponents,
  variant: string
): string {
  const component = designComponents[category] as Record<string, string>;
  return component[variant] || "";
}

// Loading state classes
export const designStates = {
  loading: "opacity-60 cursor-wait",
  disabled: "opacity-40 cursor-not-allowed",
  interactive: "cursor-pointer transition-all duration-150 ease-in-out",
} as const;
