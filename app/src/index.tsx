import { file, serve } from "bun";
import index from "./index.html";

const sendSW = true;

/**
 * Replace the request url from start until end of mountPath with directus URL.
 * @param originalURL The original URL to modify. Example: "http://localhost:3000/api/endpoint"
 * @param targetURL The Directus API URL to replace with. Example: "http://localhost:8055"
 * @param mountPath The mount path to replace. Default is "/api"
 * @return The modified URL with the Directus API URL. Example: "http://localhost:8055/endpoint"
 */

function replaceProxyURL(
  originalURL: string,
  targetURL: string,
  mountPath: string = "/api"
): string {
  const index = originalURL.indexOf(mountPath);
  if (index === -1) {
    return originalURL; // Mount path not found, return original URL
  }
  return `${targetURL}${originalURL.substring(index + mountPath.length)}`;
}

const server = serve({
  routes: {
    // Serve index.html for all unmatched routes.

    "/*": index,
    "/sw.js": async (req) => {
      if (!sendSW) {
        return new Response("Service Worker not available", { status: 404 });
      }
      return new Response(file("public/sw.js"), {
        headers: {
          "Content-Type": "application/javascript",
        },
      });
    },
    "/manifest.json": async (req) => {
      return new Response(file("public/manifest.json"), {
        headers: {
          "Content-Type": "application/json",
        },
      });
    },
    "/public/:file": async (req) => {
      const fileName = req.params.file;
      return new Response(file(`public/${fileName}`), {
        headers: {
          "Content-Type": "application/json",
        },
      });
    },
    "/screenshots/:file": async (req) => {
      const fileName = req.params.file;
      return new Response(file(`public/screenshots/${fileName}`), {
        headers: {
          "Content-Type": "image/png",
        },
      });
    },
    "/icons/:file": async (req) => {
      const fileName = req.params.file;
      return new Response(file(`public/icons/${fileName}`), {
        headers: {
          "Content-Type": "image/png",
        },
      });
    },
    "/api/*": async (req, ctx) => {
      const apiEndpoint = replaceProxyURL(
        req.url,
        "http://localhost:8055",
        "/api"
      );
      console.log("API Endpoint:", apiEndpoint);

      const ip = ctx.requestIP(req);
      const headers = new Headers(req.headers);
      // headers.set("X-Forwarded-For", ip?.address || "127.0.0.1");
      headers.set("X-Forwarded-Proto", "http");
      headers.set(
        "X-Forwarded-Host",
        req.headers.get("Host") || "localhost:8055"
      );
      console.log(
        `Forwarding request to Directus API: ${apiEndpoint} from IP: ${
          ip?.address || "127.0.0.1"
        }`,
        { headers, url: req.url }
      );
      const apiResponse = fetch(apiEndpoint, {
        headers,
        method: req.method,
        body: req.method === "GET" ? undefined : req.body,
      }).catch((error) => {
        console.error("Error fetching API:", error);
        return new Response("Internal Server Error", { status: 500 });
      });
      const apiResponseWrapper = apiResponse.then((response) => {
        return new Response(response.body, {
          status: response.status,
          statusText: response.statusText,
          headers: response.headers,
        });
      });
      return apiResponseWrapper;
    },
  },

  development: process.env.NODE_ENV !== "production" && {
    // Enable browser hot reloading in development
    hmr: true,

    // Echo console logs from the browser to the server
    console: true,
  },
});

console.log(`Server running at ${server.url}`);
