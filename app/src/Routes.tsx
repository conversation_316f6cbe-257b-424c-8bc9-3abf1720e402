import { useAtomValue } from "jotai";
import { useEffect } from "react";
import { Route, Router, Switch } from "wouter";
import { useHashLocation } from "wouter/use-hash-location";
import { ToastContainer } from "./components";
import AccountSelectionPage from "./pages/AccountSelectionPage";
import CreatePropertyPage from "./pages/CreatePropertyPage";
import LoginPage from "./pages/LoginPage";
import NoAccountPage from "./pages/NoAccountPage";
import NoPropertyPage from "./pages/NoPropertyPage";
import PropertyDetailsPage from "./pages/PropertyDetailsPage";
import PropertySelectionPage from "./pages/PropertySelectionPage";
import AppShell from "./pages/main/AppShell";
import { isAuthenticatedAtom } from "./store";

// Route Guard Components
const ProtectedRoute: React.FC<{
  component: React.ComponentType<any>;
  path: string;
  [key: string]: any;
}> = ({ component: Component, ...props }) => {
  const isAuthenticated = useAtomValue(isAuthenticatedAtom);
  const [, setLocation] = useHashLocation();

  if (!isAuthenticated) {
    setLocation("/login");
    return null;
  }

  return <Route {...props} component={Component} />;
};

const PublicRoute: React.FC<{
  component: React.ComponentType<any>;
  path: string;
  redirectAuthenticated?: string;
  [key: string]: any;
}> = ({
  component: Component,
  redirectAuthenticated = "/app/dashboard",
  ...props
}) => {
  const isAuthenticated = useAtomValue(isAuthenticatedAtom);
  const [, setLocation] = useHashLocation();

  if (isAuthenticated && redirectAuthenticated) {
    setLocation(redirectAuthenticated);
    return null;
  }

  return <Route {...props} component={Component} />;
};

function AppRouter() {
  const [location, setLocation] = useHashLocation();

  useEffect(() => {
    console.log(`AppRouter mounted - current location: ${location}`);
  }, [location]);

  return (
    <>
      {/* 
      Routes below are matched exclusively -
      the first matched route gets rendered
    */}
      <Router hook={useHashLocation}>
        <Switch>
          {/* Public Routes - Redirect authenticated users */}
          <PublicRoute path="/login" component={LoginPage} />

          {/* Protected Routes - Require authentication */}
          <ProtectedRoute
            path="/select-account"
            component={AccountSelectionPage}
          />
          <ProtectedRoute path="/no-account" component={NoAccountPage} />
          <ProtectedRoute
            path="/select-property"
            component={PropertySelectionPage}
          />
          <ProtectedRoute path="/no-property" component={NoPropertyPage} />
          <ProtectedRoute
            path="/property/create"
            component={CreatePropertyPage}
          />
          <ProtectedRoute
            path="/property/edit/:id"
            component={CreatePropertyPage}
          />
          <ProtectedRoute
            path="/property/:id"
            component={PropertyDetailsPage}
          />

          {/* Main Application Shell - Protected */}
          <ProtectedRoute path="/app/*" component={AppShell} />

          {/* Root path - Smart redirect based on auth status */}
          <Route
            path="/"
            component={() => {
              const isAuthenticated = useAtomValue(isAuthenticatedAtom);
              setLocation(isAuthenticated ? "/app/dashboard" : "/login");
              return null;
            }}
          />

          {/* Default route in a switch */}
          <Route>404: No such page!</Route>
        </Switch>
      </Router>
      <ToastContainer position="top-right" />
    </>
  );
}
export default AppRouter;
