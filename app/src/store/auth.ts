// src/store/auth.ts
import { atom } from "jotai";
import { apiService } from "@/api";
import { User } from "@/api/model/user";
import { withOperationHandlingAtom, isNetworkError } from "./operations";

export type AuthUser = Pick<
  User,
  "id" | "first_name" | "last_name" | "email" | "role" | "avatar"
>;

// Auth state atoms
export const authUserAtom = atom<AuthUser | null>(null);
export const isAuthenticatedAtom = atom((get) => {
  const user = get(authUserAtom);
  return !!user;
});

// Legacy atoms for backwards compatibility (deprecated)
export const authLoadingAtom = atom(false);
export const authErrorAtom = atom<string | null>(null);
export const authNetworkErrorAtom = atom<boolean>(false);

// Auth actions
export const loginAtom = atom(
  null,
  async (
    get,
    set,
    { email, password }: { email: string; password: string }
  ) => {
    const result = await set(
      withOperationHandling<PERSON>tom,
      {
        operation: "login",
        message: "Fazendo login...",
        displayLoading: true,
        displayError: true,
      },
      async () => {
        await apiService.auth.login(email, password);
        const user = await apiService.auth.fetchUser();
        set(authUserAtom, user);
        return user;
      }
    );

    return result;
  }
);

export const logoutAtom = atom(null, async (get, set) => {
  const result = await set(
    withOperationHandlingAtom,
    {
      operation: "logout",
      message: "Fazendo logout...",
      displayLoading: false,
      displayError: false,
    },
    async () => {
      await apiService.auth.logout();
    }
  );

  // Always clear user state on logout, regardless of API call result
  set(authUserAtom, null);

  return result;
});

export const initializeAuthAtom = atom(null, async (get, set) => {
  const result = await set(
    withOperationHandlingAtom,
    {
      operation: "initializeAuth",
      message: "Verificando autenticação...",
      displayLoading: false,
      displayError: false,
    },
    async () => {
      const user = await apiService.auth.fetchUser();
      set(authUserAtom, user);
      return user;
    }
  );

  // Handle network errors separately for auth initialization
  if (!result.success && result.error) {
    // Create a mock error object to check if it's a network error
    const error = new Error(result.error);
    if (isNetworkError(error)) {
      // For network errors during initialization, we might want to set a flag
      // but don't clear the user state (they might be offline but still authenticated)
      console.error("Network error during auth initialization:", result.error);
    } else {
      // For other errors, clear the user state (invalid token, etc.)
      set(authUserAtom, null);
    }
  }

  return result;
});

// Retry auth initialization (useful for network error recovery)
export const retryAuthInitializationAtom = atom(null, async (get, set) => {
  return await set(initializeAuthAtom);
});
