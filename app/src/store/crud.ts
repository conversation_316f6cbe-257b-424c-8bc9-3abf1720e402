// src/store/crud.ts
import { apiService } from "@/api";
import { Device } from "@/api/model/device";
import type { IrrigationPlan } from "@/api/model/irrigation-plan";
import type { IrrigationPlanStep } from "@/api/model/irrigation-plan-step";
import type { MeshDeviceMapping } from "@/api/model/mesh-device-mapping";
import type { Project } from "@/api/model/project";
import type { Property } from "@/api/model/property";
import type { PropertyDevice } from "@/api/model/property-device";
import type { Reservoir } from "@/api/model/reservoir";
import type { Sector } from "@/api/model/sector";
import type { WaterPump } from "@/api/model/water-pump";
import { NestedPartial } from "@directus/sdk";
import { atom } from "jotai";
import { refetchDataAtom } from "./data";
import { OperationResult, withOperationHandlingAtom } from "./operations";

/**
 * Generic operation factory that can handle any arguments
 * @param operation - The async operation function to execute
 * @param operationType - The type of operation for tracking
 * @param operationMessage - Optional message to display during operation
 * @param options - Optional configuration
 * @returns A write-only atom that executes the operation
 */
const createGenericOperation = <TArgs extends any[], TResult>(
  operation: (...args: TArgs) => Promise<TResult>,
  operationType: string,
  operationMessage?: string,
  options?: {
    displayLoading?: boolean;
    displayError?: boolean;
    refetchData?: boolean;
  }
) => {
  const {
    displayLoading = true,
    displayError = true,
    refetchData = true,
  } = options || {};

  return atom(null, async (get, set, ...args: TArgs) => {
    const result = await set(
      withOperationHandlingAtom,
      {
        operation: operationType,
        message: operationMessage || `${operationType}...`,
        displayLoading,
        displayError,
      },
      async () => {
        const apiResult = await operation(...args);
        // Refetch the account user tree after successful operation if enabled
        if (refetchData) {
          set(refetchDataAtom);
        }
        return apiResult;
      }
    );

    return result;
  });
};

const createCreateOperation = <T>(
  operation: (data: Partial<T>) => Promise<T>,
  operationType: string,
  operationMessage?: string
) => {
  return atom(null, async (get, set, data: Partial<T>) => {
    const result = await set(
      withOperationHandlingAtom,
      {
        operation: operationType,
        message: operationMessage || `${operationType}...`,
        displayLoading: true,
        displayError: true,
      },
      async () => {
        const apiResult = await operation(data);
        // Refetch the account user tree after successful operation
        set(refetchDataAtom);
        return apiResult;
      }
    );

    return result as OperationResult<T>;
  });
};

const createUpdateOperation = <T>(
  operation: (id: string, data: Partial<T>) => Promise<T>,
  operationType: string,
  operationMessage?: string
) => {
  return atom(
    null,
    async (get, set, { id, data }: { id: string; data: Partial<T> }) => {
      const result = await set(
        withOperationHandlingAtom,
        {
          operation: operationType,
          message: operationMessage || `${operationType}...`,
          displayLoading: true,
          displayError: true,
        },
        async () => {
          const apiResult = await operation(id, data);
          // Refetch the account user tree after successful operation
          set(refetchDataAtom);
          return apiResult;
        }
      );

      return result;
    }
  );
};

const createUpdateBatchOperation = <T>(
  operation: (items: Partial<T>[]) => Promise<T[]>,
  operationType: string,
  operationMessage?: string
) => {
  return atom(null, async (get, set, items: Partial<T>[]) => {
    const result = await set(
      withOperationHandlingAtom,
      {
        operation: operationType,
        message: operationMessage || `${operationType}...`,
        displayLoading: true,
        displayError: true,
      },
      async () => {
        const apiResult = await operation(items);
        // Refetch the account user tree after successful operation
        set(refetchDataAtom);
        return apiResult;
      }
    );

    return result;
  });
};

const createDeleteOperation = <T>(
  operation: (id: string) => Promise<void>,
  operationType: string,
  operationMessage?: string
) => {
  return atom(null, async (get, set, id: string) => {
    const result = await set(
      withOperationHandlingAtom,
      {
        operation: operationType,
        message: operationMessage || `${operationType}...`,
        displayLoading: true,
        displayError: true,
      },
      async () => {
        await operation(id);
        // Refetch the account user tree after successful operation
        set(refetchDataAtom);
        return { success: true };
      }
    );
    return result;
  });
};

// Property operations
export const createPropertyAtom = createCreateOperation(
  (data: Partial<Property>) => apiService.property.create(data),
  "createProperty",
  "Criando propriedade..."
);

export const updatePropertyAtom = createUpdateOperation(
  (id: string, data: Partial<Property>) => apiService.property.update(id, data),
  "updateProperty",
  "Atualizando propriedade..."
);

// Project operations
export const createProjectAtom = createCreateOperation(
  (data: Partial<Project>) => apiService.project.create(data),
  "createProject",
  "Criando projeto..."
);

export const updateProjectAtom = createUpdateOperation(
  (id: string, data: Partial<Project>) => apiService.project.update(id, data),
  "updateProject",
  "Atualizando projeto..."
);

// Sector operations
export const createSectorAtom = createCreateOperation(
  (data: Partial<Sector>) => apiService.sector.create(data),
  "createSector",
  "Criando setor..."
);

export const updateSectorAtom = createUpdateOperation(
  (id: string, data: Partial<Sector>) => apiService.sector.update(id, data),
  "updateSector",
  "Atualizando setor..."
);

export const deleteSectorAtom = createDeleteOperation(
  (id: string) => apiService.sector.del(id),
  "deleteSector",
  "Excluindo setor..."
);

// Water Pump operations
export const createWaterPumpAtom = createCreateOperation(
  (data: Partial<WaterPump>) => apiService.waterPump.create(data),
  "createWaterPump",
  "Criando bomba de água..."
);

export const updateWaterPumpAtom = createUpdateOperation(
  (id: string, data: Partial<WaterPump>) =>
    apiService.waterPump.update(id, data),
  "updateWaterPump",
  "Atualizando bomba de água..."
);

export const deleteWaterPumpAtom = createDeleteOperation(
  (id: string) => apiService.waterPump.del(id),
  "deleteWaterPump",
  "Excluindo bomba de água..."
);

// Reservoir operations
export const createReservoirAtom = createCreateOperation(
  (data: Partial<Reservoir>) => apiService.reservoir.create(data),
  "createReservoir",
  "Criando reservatório..."
);

export const updateReservoirAtom = createUpdateOperation(
  (id: string, data: Partial<Reservoir>) =>
    apiService.reservoir.update(id, data),
  "updateReservoir",
  "Atualizando reservatório..."
);

export const deleteReservoirAtom = createDeleteOperation(
  (id: string) => apiService.reservoir.del(id),
  "deleteReservoir",
  "Excluindo reservatório..."
);

// Irrigation Plan operations
export const createIrrigationPlanAtom = createCreateOperation(
  (data: NestedPartial<IrrigationPlan>) =>
    apiService.irrigationPlan.create(data),
  "createIrrigationPlan",
  "Criando plano de irrigação..."
);

export const updateIrrigationPlanAtom = createUpdateOperation(
  (id: string, data: Partial<IrrigationPlan>) =>
    apiService.irrigationPlan.update(id, data),
  "updateIrrigationPlan",
  "Atualizando plano de irrigação..."
);

// Irrigation Plan Step operations
export const createIrrigationPlanStepAtom = createCreateOperation(
  (data: Partial<IrrigationPlanStep>) =>
    apiService.irrigationPlanStep.create(data),
  "createIrrigationPlanStep",
  "Criando etapa do plano..."
);

export const updateIrrigationPlanStepAtom = createUpdateOperation(
  (id: string, data: Partial<IrrigationPlanStep>) =>
    apiService.irrigationPlanStep.update(id, data),
  "updateIrrigationPlanStep",
  "Atualizando etapa do plano..."
);

export const swapIrrigationPlanStepsOrdersAtom = createGenericOperation(
  (
    step1: Pick<IrrigationPlanStep, "id" | "order">,
    step2: Pick<IrrigationPlanStep, "id" | "order">
  ) => apiService.irrigationPlanStep.swapOrders(step1, step2),
  "swapIrrigationPlanStep",
  "Trocando ordem das etapas..."
);

export const updateIrrigationPlanStepBatchAtom = createUpdateBatchOperation(
  (items: Partial<IrrigationPlanStep>[]) =>
    apiService.irrigationPlanStep.updateBatch(items),
  "updateIrrigationPlanStepBatch",
  "Atualizando etapas do plano..."
);

export const deleteIrrigationPlanStepAtom = createDeleteOperation(
  (id: string) => apiService.irrigationPlanStep.del(id),
  "deleteIrrigationPlanStep",
  "Excluindo etapa do plano..."
);

// Property Device operations
export const createPropertyDeviceAtom = createCreateOperation(
  (data: Partial<PropertyDevice>) => apiService.propertyDevice.create(data),
  "createPropertyDevice",
  "Criando dispositivo..."
);

export const updatePropertyDeviceAtom = createUpdateOperation(
  (id: string, data: Partial<PropertyDevice>) =>
    apiService.propertyDevice.update(id, data),
  "updatePropertyDevice",
  "Atualizando dispositivo..."
);

export const updateDeviceAtom = createUpdateOperation(
  (id: string, data: Partial<Device>) => apiService.device.update(id, data),
  "updateDevice",
  "Atualizando dispositivo..."
);

// Mesh Device Mapping operations
export const createMeshDeviceMappingAtom = createCreateOperation(
  (data: Partial<MeshDeviceMapping>) =>
    apiService.meshDeviceMapping.create(data),
  "createMeshDeviceMapping",
  "Criando mapeamento de dispositivo..."
);

export const updateMeshDeviceMappingAtom = createUpdateOperation(
  (id: string, data: Partial<MeshDeviceMapping>) =>
    apiService.meshDeviceMapping.update(id, data),
  "updateMeshDeviceMapping",
  "Atualizando mapeamento de dispositivo..."
);

export const deleteMeshDeviceMappingAtom = createDeleteOperation(
  (id: string) => apiService.meshDeviceMapping.del(id),
  "deleteMeshDeviceMapping",
  "Removendo mapeamento de dispositivo..."
);

// Export the generic operation factory for use in other files
export { createGenericOperation };
