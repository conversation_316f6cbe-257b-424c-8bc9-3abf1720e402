@import "tailwindcss";
@theme {
  /* Color Palette */
  --color-primary-50: #f0f9f0;
  --color-primary-100: #dcf2dc;
  --color-primary-200: #bbe5bb;
  --color-primary-300: #8fd68f;
  --color-primary-400: #5cbd5c;
  --color-primary-500: #3ba73b;
  --color-primary-600: #2d8a2d;
  --color-primary-700: #256d25;
  --color-primary-800: #1e5a1e;
  --color-primary-900: #194719;

  --color-neutral-50: #f9f9f9;
  --color-neutral-100: #f0f0f0;
  --color-neutral-200: #e4e4e4;
  --color-neutral-300: #d1d1d1;
  --color-neutral-400: #b4b4b4;
  --color-neutral-500: #9a9a9a;
  --color-neutral-600: #818181;
  --color-neutral-700: #6a6a6a;
  --color-neutral-800: #5a5a5a;
  --color-neutral-900: #4a4a4a;

  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: #3b82f6;

  --color-background-primary: #ffffff;
  --color-background-secondary: #f8f9fa;
  --color-background-accent: #f0f9f0;

  /* Typography */
  --font-family-primary:
    Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;

  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-base: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-2xl: 24px;
  --font-size-3xl: 30px;
  --font-size-4xl: 36px;

  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* Border Radius */
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;
  --border-radius-xl: 16px;
  --border-radius-2xl: 24px;
  --border-radius-full: 9999px;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md:
    0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg:
    0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl:
    0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Animations */
  --duration-fast: 150ms;
  --duration-normal: 300ms;
  --duration-slow: 500ms;

  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in: cubic-bezier(0.4, 0, 1, 1);

  /* Breakpoints */
  --breakpoint-mobile: 375px;
  --breakpoint-tablet: 768px;
  --breakpoint-desktop: 1024px;

  /* Component Specific */
  --min-height-interactive: 48px;
  --min-height-header: 56px;
  --min-height-tab-bar: 60px;

  /* Icon Sizes */
  --icon-size-sm: 16px;
  --icon-size-md: 20px;
  --icon-size-lg: 24px;
  --icon-size-xl: 32px;

  /* Grid */
  --grid-columns: 12;
  --grid-gap: var(--spacing-md);

  /* Focus Ring */
  --focus-ring: 0 0 0 3px rgba(59, 167, 59, 0.1);

  /* States */
  --opacity-loading: 0.6;
  --opacity-disabled: 0.4;
}
/* Custom animations */
@keyframes shrink {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}

html,
body,
#root {
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
}
