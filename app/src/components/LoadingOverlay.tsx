// src/components/LoadingOverlay.tsx
import React from "react";
import { useAtomValue } from "jotai";
import { hasActiveLoadingAtom, currentLoadingMessageAtom } from "@/store";

export const LoadingOverlay: React.FC = () => {
  const hasActiveLoading = useAtomValue(hasActiveLoadingAtom);
  const currentMessage = useAtomValue(currentLoadingMessageAtom);

  if (!hasActiveLoading) {
    return null;
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/30">
      <div className="bg-white rounded-lg shadow-lg p-6 max-w-sm w-full mx-4">
        <div className="flex items-center space-x-3">
          {/* Loading spinner */}
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          <div className="flex-1">
            <p className="text-gray-900 font-medium">Carregando...</p>
            {currentMessage && (
              <p className="text-gray-600 text-sm mt-1">{currentMessage}</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
