import React, { useEffect } from "react";
import { use<PERSON><PERSON>, useSet<PERSON><PERSON> } from "jotai";
import { X, CheckCircle, AlertCircle, Info, AlertTriangle } from "lucide-react";
import { Button } from "./ui/Button";
import {
  toasts<PERSON>tom,
  removeToast<PERSON>tom,
  addToast<PERSON>tom,
  removeAllToasts<PERSON>tom,
  showSuccessToast<PERSON>tom,
  showErrorToast<PERSON>tom,
  showWarningToast<PERSON>tom,
  showInfoToast<PERSON>tom,
  type ToastData,
} from "@/store";

interface ToastProps {
  toast: ToastData;
}

function Toast({ toast }: ToastProps) {
  const removeToast = useSetAtom(removeToast<PERSON>tom);
  const {
    id,
    title,
    message,
    type = "info",
    duration = 5000,
    actionLabel,
    onAction,
  } = toast;

  useEffect(() => {
    if (duration > 0) {
      const timer = setTimeout(() => {
        removeToast(id);
      }, duration);
      return () => clearTimeout(timer);
    }
  }, [id, duration, removeToast]);

  const getTypeStyles = () => {
    switch (type) {
      case "success":
        return {
          container: "bg-green-50 border-green-200 text-green-900",
          icon: <CheckCircle className="h-5 w-5 text-green-500" />,
        };
      case "error":
        return {
          container: "bg-red-50 border-red-200 text-red-900",
          icon: <AlertCircle className="h-5 w-5 text-red-500" />,
        };
      case "warning":
        return {
          container: "bg-yellow-50 border-yellow-200 text-yellow-900",
          icon: <AlertTriangle className="h-5 w-5 text-yellow-500" />,
        };
      case "info":
      default:
        return {
          container: "bg-blue-50 border-blue-200 text-blue-900",
          icon: <Info className="h-5 w-5 text-blue-500" />,
        };
    }
  };

  const styles = getTypeStyles();

  const getProgressBarStyles = () => {
    switch (type) {
      case "success":
        return {
          background: "bg-green-200",
          progress: "bg-green-600",
        };
      case "error":
        return {
          background: "bg-red-200",
          progress: "bg-red-600",
        };
      case "warning":
        return {
          background: "bg-yellow-200",
          progress: "bg-yellow-600",
        };
      case "info":
      default:
        return {
          background: "bg-blue-200",
          progress: "bg-blue-600",
        };
    }
  };

  const progressStyles = getProgressBarStyles();

  return (
    <div
      className={`relative p-4 mb-3 rounded-lg border shadow-sm ${styles.container}`}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          {styles.icon}
          <div>
            {title && <p className="text-sm font-semibold mb-1">{title}</p>}
            <p className={`text-sm ${title ? "font-normal" : "font-medium"}`}>
              {message}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          {actionLabel && onAction && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onAction}
              className="text-sm font-medium underline hover:no-underline"
            >
              {actionLabel}
            </Button>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => removeToast(id)}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Progress bar for auto-dismiss countdown */}
      {duration > 0 && (
        <div className="mt-3">
          <div className={`${progressStyles.background} rounded-full h-1`}>
            <div
              className={`${progressStyles.progress} h-1 rounded-full`}
              style={{
                animation: `shrink ${duration}ms linear forwards`,
              }}
            />
          </div>
        </div>
      )}
    </div>
  );
}

interface ToastContainerProps {
  position?:
    | "top-right"
    | "top-left"
    | "bottom-right"
    | "bottom-left"
    | "top-center"
    | "bottom-center";
}

function ToastContainer({ position = "top-right" }: ToastContainerProps) {
  const [toasts] = useAtom(toastsAtom);

  if (toasts.length === 0) return null;

  const getPositionClasses = () => {
    switch (position) {
      case "top-left":
        return "top-4 left-4";
      case "top-center":
        return "top-4 left-1/2 transform -translate-x-1/2";
      case "top-right":
        return "top-4 right-4";
      case "bottom-left":
        return "bottom-4 left-4";
      case "bottom-center":
        return "bottom-4 left-1/2 transform -translate-x-1/2";
      case "bottom-right":
        return "bottom-4 right-4";
      default:
        return "top-4 right-4";
    }
  };

  return (
    <div className={`fixed z-50 max-w-sm w-full ${getPositionClasses()}`}>
      {toasts.map((toast) => (
        <Toast key={toast.id} toast={toast} />
      ))}
    </div>
  );
}

// Hook for managing toasts (using store)
export function useToast() {
  const [toasts] = useAtom(toastsAtom);
  const addToast = useSetAtom(addToastAtom);
  const removeToast = useSetAtom(removeToastAtom);
  const removeAllToasts = useSetAtom(removeAllToastsAtom);
  const showSuccess = useSetAtom(showSuccessToastAtom);
  const showError = useSetAtom(showErrorToastAtom);
  const showWarning = useSetAtom(showWarningToastAtom);
  const showInfo = useSetAtom(showInfoToastAtom);

  return {
    toasts,
    addToast,
    removeToast,
    removeAllToasts,
    showSuccess,
    showError,
    showWarning,
    showInfo,
  };
}

export { Toast, ToastContainer };
export default ToastContainer;
