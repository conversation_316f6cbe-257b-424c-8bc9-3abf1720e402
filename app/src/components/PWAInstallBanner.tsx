import React from "react";
import { Download, X, Smartphone } from "lucide-react";
import { usePWA } from "../hooks/usePWA";
import { Button } from "./ui/Button";

export function PWAInstallBanner() {
  const { showInstallPrompt, install, dismissInstallPrompt, isInstalled } =
    usePWA();

  if (isInstalled || !showInstallPrompt) {
    return null;
  }

  const handleInstall = async () => {
    const success = await install();
    if (!success) {
      dismissInstallPrompt();
    }
  };

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-gradient-to-r from-green-600 to-green-700 text-white p-4 shadow-lg z-50 border-t-4 border-green-400">
      <div className="max-w-4xl mx-auto flex items-center justify-between gap-4">
        <div className="flex items-center gap-3 flex-1">
          <div className="bg-white/20 p-2 rounded-lg">
            <Smartphone className="h-6 w-6" />
          </div>
          <div className="flex-1">
            <h3 className="font-semibold text-lg mb-1">Instalar Irriga+</h3>
            <p className="text-green-100 text-sm">
              Instale o app para acesso rápido, notificações e uso offline
            </p>
          </div>
        </div>

        <div className="flex items-center gap-3">
          <Button
            onClick={handleInstall}
            variant="primary"
            size="sm"
            className="bg-white text-green-600 hover:bg-green-50 flex items-center gap-2 shadow-sm"
          >
            <Download className="h-4 w-4" />
            Instalar
          </Button>

          <Button
            onClick={dismissInstallPrompt}
            variant="ghost"
            size="sm"
            className="text-green-100 hover:text-white p-2"
            aria-label="Fechar"
          >
            <X className="h-5 w-5" />
          </Button>
        </div>
      </div>
    </div>
  );
}

export default PWAInstallBanner;
