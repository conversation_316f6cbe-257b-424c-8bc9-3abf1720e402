// src/components/ErrorToast.tsx
import React, { useEffect, useState, useCallback } from "react";
import { Button } from "./ui/Button";
import { useAtomValue, useSetAtom } from "jotai";
import {
  displayErrorsAtom,
  removeErrorOperation<PERSON>tom,
  type ErrorOperation,
} from "@/store";

export const ErrorToast: React.FC = () => {
  const errors = useAtomValue(displayErrorsAtom);
  const removeError = useSetAtom(removeErrorOperationAtom);
  const [currentError, setCurrentError] = useState<ErrorOperation | null>(null);

  // Show the first error if there's one and no current error is being displayed
  useEffect(() => {
    if (errors.length > 0 && !currentError) {
      setCurrentError(errors[0]);
    } else if (errors.length === 0 && currentError) {
      setCurrentError(null);
    }
  }, [errors, currentError]);

  const handleDismiss = useCallback(() => {
    if (currentError) {
      removeError(currentError.operation);
      setCurrentError(null);
    }
  }, [currentError, removeError]);

  // Auto-dismiss after 5 seconds
  useEffect(() => {
    if (currentError) {
      const timer = setTimeout(handleDismiss, 5000);
      return () => clearTimeout(timer);
    }
  }, [currentError, handleDismiss]);

  if (!currentError) {
    return null;
  }

  return (
    <div className="fixed top-4 right-4 z-50 max-w-sm w-full">
      <div className="bg-red-50 border border-red-200 rounded-lg shadow-lg p-4">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            {/* Error icon */}
            <svg
              className="h-5 w-5 text-red-400"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                clipRule="evenodd"
              />
            </svg>
          </div>
          <div className="ml-3 flex-1">
            <p className="text-sm font-medium text-red-800">Erro</p>
            <p className="text-sm text-red-700 mt-1">{currentError.message}</p>
          </div>
          <div className="ml-auto pl-3">
            <div className="-mx-1.5 -my-1.5">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleDismiss}
                className="text-red-500 hover:bg-red-100"
              >
                <span className="sr-only">Dismiss</span>
                <svg
                  className="h-5 w-5"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                    clipRule="evenodd"
                  />
                </svg>
              </Button>
            </div>
          </div>
        </div>

        {/* Progress bar for auto-dismiss */}
        <div className="mt-3">
          <div className="bg-red-200 rounded-full h-1">
            <div
              className="bg-red-600 h-1 rounded-full animate-[shrink_5s_linear]"
              style={{
                animation: "shrink 5s linear forwards",
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
};
