import React from "react";
import { WifiOff, Wifi } from "lucide-react";
import { usePWA } from "../hooks/usePWA";

export function NetworkStatus() {
  const { isOnline } = usePWA();

  if (isOnline) {
    return null;
  }

  return (
    <div className="fixed top-0 left-0 right-0 bg-amber-500 text-white px-4 py-2 text-center text-sm font-medium z-50 shadow-sm">
      <div className="flex items-center justify-center gap-2">
        <WifiOff className="h-4 w-4" />
        <span>
          Você está offline - Algumas funcionalidades podem estar limitadas
        </span>
      </div>
    </div>
  );
}

export default NetworkStatus;
