import React from "react";
import { propertiesAtom } from "@/store/data";
import { useAtomValue } from "jotai";

interface PropertyOverviewProps extends React.HTMLAttributes<HTMLDivElement> {
  propertyId: string;
  showTitle?: boolean;
  showAddress?: boolean;
  showSettings?: boolean;
  showStats?: boolean;
  title?: string;
}

export const PropertyOverview: React.FC<PropertyOverviewProps> = ({
  propertyId,
  showTitle = true,
  showAddress = true,
  showSettings = true,
  showStats = true,
  title,
  ...divProps
}) => {
  const properties = useAtomValue(propertiesAtom);
  const property = properties.find((p) => p.id === propertyId);

  if (!property) {
    return (
      <div
        {...divProps}
        className={`bg-white rounded-xl shadow-sm border border-gray-200 p-6 ${
          divProps.className || ""
        }`}
      >
        <h2 className="text-lg font-semibold text-gray-900 mb-4">
          Informa<PERSON><PERSON><PERSON> <PERSON>priedade
        </h2>
        <p className="text-gray-600">Propriedade não encontrada.</p>
      </div>
    );
  }

  const addressParts = [
    property.address_street_name,
    property.address_street_number,
    property.address_complement,
  ].filter(Boolean);
  const address = addressParts.join(", ");
  const cityState = [property.address_city, property.address_state]
    .filter(Boolean)
    .join(" - ");
  let area: string | number | undefined = undefined;
  if (
    property.metadata &&
    typeof property.metadata === "object" &&
    "area" in property.metadata
  ) {
    area = (property.metadata as any).area;
  }
  if (!area && property.notes && typeof property.notes === "string") {
    const match = property.notes.match(/area\s*[:=]\s*(\d+(?:\.\d+)?)/i);
    if (match) area = match[1];
  }
  const description =
    property.notes ||
    (property.metadata && (property.metadata as any).description);

  const numDevices = property.devices?.length || 0;
  const numProjects = property.projects?.length || 0;
  const numWaterPumps = property.water_pumps?.length || 0;

  return (
    <div
      {...divProps}
      className={` ${divProps.className || ""} flex flex-col gap-3`}
    >
      {showTitle && (
        <div className="">
          <h1 className="text-2xl font-bold text-gray-900">
            {title || property.name}
          </h1>
        </div>
      )}

      {showAddress && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div>
            <p className="text-sm font-medium text-gray-500">Endereço</p>
            <p className="text-gray-900 mt-1">{address || "-"}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Cidade/Estado</p>
            <p className="text-gray-900 mt-1">{cityState || "-"}</p>
          </div>
          {area && (
            <div>
              <p className="text-sm font-medium text-gray-500">Área</p>
              <p className="text-gray-900 mt-1">{area} hectares</p>
            </div>
          )}
          {property.timezone && (
            <div>
              <p className="text-sm font-medium text-gray-500">Fuso Horário</p>
              <p className="text-gray-900 mt-1">{property.timezone}</p>
            </div>
          )}
        </div>
      )}

      {showSettings &&
        (property.rain_gauge_enabled ||
          property.backwash_duration_minutes ||
          property.backwash_period_minutes) && (
          <div className="p-4 bg-gray-50 rounded-lg border border-gray-200">
            <h3 className="text-sm font-medium text-gray-700 mb-3">
              Configurações
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
              {property.rain_gauge_enabled && (
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span className="text-gray-600">Pluviômetro habilitado</span>
                  {property.precipitation_volume_limit_mm && (
                    <span className="text-gray-500">
                      (limite: {property.precipitation_volume_limit_mm}mm)
                    </span>
                  )}
                </div>
              )}
              {property.backwash_duration_minutes && (
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-gray-600">
                    Retrolavagem: {property.backwash_duration_minutes}min
                  </span>
                  {property.backwash_period_minutes && (
                    <span className="text-gray-500">
                      (a cada {property.backwash_period_minutes}min)
                    </span>
                  )}
                </div>
              )}
            </div>
          </div>
        )}

      {showStats && (
        <div className="grid grid-cols-3 gap-3">
          <div className="bg-blue-50 rounded-lg p-3 flex flex-col items-center justify-between border border-blue-100">
            <span className="text-blue-600 text-xs font-medium text-center">
              Dispositivos
            </span>
            <span className="text-xl font-bold text-blue-700">
              {numDevices}
            </span>
          </div>
          <div className="bg-green-50 rounded-lg p-3 flex flex-col items-center justify-between border border-green-100">
            <span className="text-green-600 text-xs font-medium text-center">
              Projetos
            </span>
            <span className="text-xl font-bold text-green-700">
              {numProjects}
            </span>
          </div>
          <div className="bg-indigo-50 rounded-lg p-3 flex flex-col items-center justify-between border border-indigo-100">
            <span className="text-indigo-600 text-xs font-medium text-center">
              Bombas d'Água
            </span>
            <span className="text-xl font-bold text-indigo-700">
              {numWaterPumps}
            </span>
          </div>
        </div>
      )}
    </div>
  );
};
