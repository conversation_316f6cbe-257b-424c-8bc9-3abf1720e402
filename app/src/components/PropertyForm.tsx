import { useGeolocation } from "@uidotdev/usehooks";
import { MapIcon } from "lucide-react";
import React, { useCallback, useState } from "react";
import { Button } from "@/components/ui/Button";
import { X, MapPin, Check } from "lucide-react";

export interface Point {
  type: "Point";
  coordinates: [number, number]; // [longitude, latitude]
}

export interface PropertyFormData {
  name: string;
  timezone: string;
  point: Point | null;
  address_postal_code: string | null;
  address_street_name: string | null;
  address_street_number: string | null;
  address_complement: string | null;
  address_neighborhood: string | null;
  address_city: string | null;
  address_state: string | null;
  address_country: string | null;
  backwash_duration_minutes: number | null;
  backwash_period_minutes: number | null;
  backwash_delay_seconds: number | null;
  rain_gauge_enabled: boolean;
  rain_gauge_resolution_mm: number | null;
  precipitation_volume_limit_mm: number | null;
  precipitation_suspended_duration_hours: number | null;
}

interface PropertyFormProps {
  initialData?: Partial<PropertyFormData>;
  isEditMode?: boolean;
  isLoading?: boolean;
  onSubmit: (data: PropertyFormData) => Promise<void>;
  onCancel: () => void;
  submitButtonText?: string;
}

const defaultFormData: PropertyFormData = {
  name: "",
  timezone: "America/Sao_Paulo",
  point: null,
  address_postal_code: null,
  address_street_name: null,
  address_street_number: null,
  address_complement: null,
  address_neighborhood: null,
  address_city: null,
  address_state: null,
  address_country: "Brasil",
  backwash_duration_minutes: null,
  backwash_period_minutes: null,
  backwash_delay_seconds: null,
  rain_gauge_enabled: false,
  rain_gauge_resolution_mm: 0.2,
  precipitation_volume_limit_mm: 2,
  precipitation_suspended_duration_hours: 24,
};

function GeoLocation({
  onLocation,
}: {
  onLocation: (location: { latitude: number; longitude: number }) => void;
}): React.JSX.Element {
  const state = useGeolocation();

  if (state.loading) {
    return (
      <p className="mt-1 text-xs text-gray-500">
        Acessando localização... (você pode precisar habilitar permissões)
      </p>
    );
  }

  if (state.error) {
    return (
      <p className="mt-1 text-xs text-red-600">
        Habilite permissões para acessar seus dados de localização
      </p>
    );
  }

  const { latitude, longitude } = state;

  if (latitude != null && longitude != null) {
    return (
      <Button
        type="button"
        variant="ghost"
        size="sm"
        onClick={() => onLocation({ latitude, longitude })}
        icon={<MapIcon className="h-4 w-4" />}
        iconPosition="left"
      >
        Usar localização atual
      </Button>
    );
  } else {
    return <p>Localização não disponível</p>;
  }
}

export function PropertyForm({
  initialData = {},
  isEditMode = false,
  isLoading = false,
  onSubmit,
  onCancel,
  submitButtonText,
}: PropertyFormProps): React.JSX.Element {
  const [formData, setFormData] = useState<PropertyFormData>({
    ...defaultFormData,
    ...initialData,
  });

  const [errors, setErrors] = useState<Partial<PropertyFormData>>({});

  const handleInputChange = useCallback(
    (
      e: React.ChangeEvent<
        HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
      >
    ) => {
      const { name, value } = e.target;

      // Convert empty strings to null for nullable fields, except for required fields
      const requiredFields = [
        "name",
        "timezone",
        "address_city",
        "address_state",
        "address_country",
        "address_postal_code",
      ];
      const processedValue =
        !requiredFields.includes(name) && value.trim() === "" ? null : value;

      setFormData((prev) => ({ ...prev, [name]: processedValue }));

      // Clear error when user starts typing
      if (errors[name as keyof PropertyFormData]) {
        setErrors((prev) => ({ ...prev, [name]: undefined }));
      }
    },
    [errors]
  );

  const validateForm = useCallback((): boolean => {
    const newErrors: Partial<PropertyFormData> = {};

    if (!formData.name.trim()) {
      newErrors.name = "Nome da propriedade é obrigatório";
    }

    if (!formData.address_street_name?.trim()) {
      newErrors.address_street_name = "Nome da rua é obrigatório";
    }

    if (!formData.address_city?.trim()) {
      newErrors.address_city = "Cidade é obrigatória";
    }

    if (!formData.address_state?.trim()) {
      newErrors.address_state = "Estado é obrigatório";
    }

    if (!formData.address_country?.trim()) {
      newErrors.address_country = "País é obrigatório";
    }

    if (!formData.address_postal_code?.trim()) {
      newErrors.address_postal_code = "CEP é obrigatório";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData]);

  const handleSubmit = useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault();

      if (!validateForm()) {
        return;
      }

      try {
        await onSubmit(formData);
      } catch (error) {
        console.error("Property form submission error:", error);
      }
    },
    [formData, onSubmit, validateForm]
  );

  const defaultSubmitText = isEditMode
    ? "Salvar Alterações"
    : "Criar Propriedade";

  return (
    <div className="">
      <form onSubmit={handleSubmit} className=" space-y-6">
        {/* Property Name */}
        <div>
          <label
            htmlFor="name"
            className="block text-sm font-medium text-gray-700 mb-2"
          >
            Nome da Propriedade *
          </label>
          <input
            type="text"
            id="name"
            name="name"
            value={formData.name}
            onChange={handleInputChange}
            placeholder="Ex: Fazenda São José"
            className={`w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 ${
              errors.name ? "border-red-300" : "border-gray-300"
            }`}
          />
          {errors.name && (
            <p className="mt-1 text-sm text-red-600">{errors.name}</p>
          )}
        </div>

        {/* Backwashing Configuration */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">
            Configuração de Retrolavagem
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label
                htmlFor="backwash_duration_minutes"
                className="block text-sm font-medium text-gray-700 mb-2"
              >
                Duração da Retrolavagem (minutos)
              </label>
              <input
                type="number"
                id="backwash_duration_minutes"
                name="backwash_duration_minutes"
                value={formData.backwash_duration_minutes || ""}
                onChange={handleInputChange}
                placeholder="Ex: 5"
                min="0"
                step="1"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
              />
              <p className="mt-1 text-xs text-gray-500">
                Tempo para limpeza do sistema por reversão do fluxo
              </p>
            </div>

            <div>
              <label
                htmlFor="backwash_period_minutes"
                className="block text-sm font-medium text-gray-700 mb-2"
              >
                Período entre Retrolavagens (minutos)
              </label>
              <input
                type="number"
                id="backwash_period_minutes"
                name="backwash_period_minutes"
                value={formData.backwash_period_minutes || ""}
                onChange={handleInputChange}
                placeholder="Ex: 1440"
                min="0"
                step="1"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
              />
              <p className="mt-1 text-xs text-gray-500">
                Intervalo para manutenção automática
              </p>
            </div>
          </div>
        </div>

        {/* Rain Gauge Configuration */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">
            Configuração do Pluviômetro
          </h3>

          <div className="space-y-4">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="rain_gauge_enabled"
                name="rain_gauge_enabled"
                checked={formData.rain_gauge_enabled}
                onChange={(e) => {
                  setFormData((prev) => ({
                    ...prev,
                    rain_gauge_enabled: e.target.checked,
                  }));
                }}
                className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
              />
              <label
                htmlFor="rain_gauge_enabled"
                className="ml-2 block text-sm font-medium text-gray-700"
              >
                Habilitar monitoramento de chuva
              </label>
            </div>

            {formData.rain_gauge_enabled && (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pl-6 border-l-2 border-green-200">
                <div>
                  <label
                    htmlFor="rain_gauge_resolution_mm"
                    className="block text-sm font-medium text-gray-700 mb-2"
                  >
                    Resolução do Pluviômetro (mm)
                  </label>
                  <input
                    type="number"
                    id="rain_gauge_resolution_mm"
                    name="rain_gauge_resolution_mm"
                    value={formData.rain_gauge_resolution_mm || ""}
                    onChange={handleInputChange}
                    placeholder="0.2"
                    min="0"
                    step="0.1"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                  />
                  <p className="mt-1 text-xs text-gray-500">
                    Menor incremento mensurável
                  </p>
                </div>

                <div>
                  <label
                    htmlFor="precipitation_volume_limit_mm"
                    className="block text-sm font-medium text-gray-700 mb-2"
                  >
                    Limite de Precipitação (mm)
                  </label>
                  <input
                    type="number"
                    id="precipitation_volume_limit_mm"
                    name="precipitation_volume_limit_mm"
                    value={formData.precipitation_volume_limit_mm || ""}
                    onChange={handleInputChange}
                    placeholder="2"
                    min="0"
                    step="0.1"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                  />
                  <p className="mt-1 text-xs text-gray-500">
                    Volume para suspender irrigação
                  </p>
                </div>

                <div>
                  <label
                    htmlFor="precipitation_suspended_duration_hours"
                    className="block text-sm font-medium text-gray-700 mb-2"
                  >
                    Duração da Suspensão (horas)
                  </label>
                  <input
                    type="number"
                    id="precipitation_suspended_duration_hours"
                    name="precipitation_suspended_duration_hours"
                    value={
                      formData.precipitation_suspended_duration_hours || ""
                    }
                    onChange={handleInputChange}
                    placeholder="24"
                    min="0"
                    step="1"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                  />
                  <p className="mt-1 text-xs text-gray-500">
                    Tempo de suspensão após limite
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Address and Location Section */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">
            Endereço e Localização
          </h3>

          {/* Timezone */}
          <div>
            <label
              htmlFor="timezone"
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              Fuso Horário
            </label>
            <select
              id="timezone"
              name="timezone"
              value={formData.timezone}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
            >
              <option value="America/Sao_Paulo">São Paulo (UTC-3)</option>
              <option value="America/Manaus">Manaus (UTC-4)</option>
              <option value="America/Rio_Branco">Rio Branco (UTC-5)</option>
            </select>
          </div>

          {/* Street Name and Number */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label
                htmlFor="address_street_name"
                className="block text-sm font-medium text-gray-700 mb-2"
              >
                Nome da Rua *
              </label>
              <input
                type="text"
                id="address_street_name"
                name="address_street_name"
                value={formData.address_street_name || ""}
                onChange={handleInputChange}
                placeholder="Ex: Rua das Flores"
                className={`w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 ${
                  errors.address_street_name
                    ? "border-red-300"
                    : "border-gray-300"
                }`}
              />
              {errors.address_street_name && (
                <p className="mt-1 text-sm text-red-600">
                  {errors.address_street_name}
                </p>
              )}
            </div>

            <div>
              <label
                htmlFor="address_street_number"
                className="block text-sm font-medium text-gray-700 mb-2"
              >
                Número
              </label>
              <input
                type="text"
                id="address_street_number"
                name="address_street_number"
                value={formData.address_street_number || ""}
                onChange={handleInputChange}
                placeholder="123"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
              />
            </div>
          </div>

          {/* Complement and Neighborhood */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label
                htmlFor="address_complement"
                className="block text-sm font-medium text-gray-700 mb-2"
              >
                Complemento
              </label>
              <input
                type="text"
                id="address_complement"
                name="address_complement"
                value={formData.address_complement || ""}
                onChange={handleInputChange}
                placeholder="Ex: Bloco A, Apto 101"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
              />
            </div>

            <div>
              <label
                htmlFor="address_neighborhood"
                className="block text-sm font-medium text-gray-700 mb-2"
              >
                Bairro
              </label>
              <input
                type="text"
                id="address_neighborhood"
                name="address_neighborhood"
                value={formData.address_neighborhood || ""}
                onChange={handleInputChange}
                placeholder="Ex: Centro"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
              />
            </div>
          </div>

          {/* City and State */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label
                htmlFor="address_city"
                className="block text-sm font-medium text-gray-700 mb-2"
              >
                Cidade *
              </label>
              <input
                type="text"
                id="address_city"
                name="address_city"
                value={formData.address_city || ""}
                onChange={handleInputChange}
                placeholder="Ex: São Paulo"
                className={`w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 ${
                  errors.address_city ? "border-red-300" : "border-gray-300"
                }`}
              />
              {errors.address_city && (
                <p className="mt-1 text-sm text-red-600">
                  {errors.address_city}
                </p>
              )}
            </div>

            <div>
              <label
                htmlFor="address_state"
                className="block text-sm font-medium text-gray-700 mb-2"
              >
                Estado *
              </label>
              <input
                type="text"
                id="address_state"
                name="address_state"
                value={formData.address_state || ""}
                onChange={handleInputChange}
                placeholder="Ex: SP"
                className={`w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 ${
                  errors.address_state ? "border-red-300" : "border-gray-300"
                }`}
              />
              {errors.address_state && (
                <p className="mt-1 text-sm text-red-600">
                  {errors.address_state}
                </p>
              )}
            </div>
          </div>

          {/* Country and ZIP Code */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label
                htmlFor="address_country"
                className="block text-sm font-medium text-gray-700 mb-2"
              >
                País *
              </label>
              <input
                type="text"
                id="address_country"
                name="address_country"
                value={formData.address_country || ""}
                onChange={handleInputChange}
                placeholder="Ex: Brasil"
                className={`w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 ${
                  errors.address_country ? "border-red-300" : "border-gray-300"
                }`}
              />
              {errors.address_country && (
                <p className="mt-1 text-sm text-red-600">
                  {errors.address_country}
                </p>
              )}
            </div>
            <div>
              <label
                htmlFor="address_postal_code"
                className="block text-sm font-medium text-gray-700 mb-2"
              >
                CEP *
              </label>
              <input
                type="text"
                id="address_postal_code"
                name="address_postal_code"
                value={formData.address_postal_code || ""}
                onChange={handleInputChange}
                placeholder="00000-000"
                className={`w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 ${
                  errors.address_postal_code
                    ? "border-red-300"
                    : "border-gray-300"
                }`}
              />
              {errors.address_postal_code && (
                <p className="mt-1 text-sm text-red-600">
                  {errors.address_postal_code}
                </p>
              )}
            </div>
          </div>

          {/* Coordinates */}
          <div>
            <label
              htmlFor="coordinates"
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              Coordenadas (Latitude, Longitude)
            </label>
            <div className="grid grid-cols-2 gap-2">
              <input
                type="number"
                placeholder="Latitude"
                step="any"
                value={formData.point?.coordinates[1] || ""}
                onChange={(e) => {
                  const lat = parseFloat(e.target.value);
                  if (!isNaN(lat)) {
                    const lng = formData.point?.coordinates[0] || 0;
                    setFormData((prev) => ({
                      ...prev,
                      point: { type: "Point", coordinates: [lng, lat] },
                    }));
                  } else {
                    setFormData((prev) => ({ ...prev, point: null }));
                  }
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
              />
              <input
                type="number"
                placeholder="Longitude"
                step="any"
                value={formData.point?.coordinates[0] || ""}
                onChange={(e) => {
                  const lng = parseFloat(e.target.value);
                  if (!isNaN(lng)) {
                    const lat = formData.point?.coordinates[1] || 0;
                    setFormData((prev) => ({
                      ...prev,
                      point: { type: "Point", coordinates: [lng, lat] },
                    }));
                  } else {
                    setFormData((prev) => ({ ...prev, point: null }));
                  }
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
              />
            </div>
            <GeoLocation
              onLocation={({ latitude, longitude }) => {
                setFormData((prev) => ({
                  ...prev,
                  point: {
                    type: "Point",
                    coordinates: [longitude, latitude],
                  },
                }));
              }}
            />
            {/* <p className="mt-1 text-xs text-gray-500">
              Opcional: Use coordenadas GPS para localização precisa
            </p> */}
          </div>
        </div>

        {/* Form Actions */}
        <div className="flex flex-col sm:flex-row gap-3 pt-6 border-t border-gray-200">
          <Button
            type="submit"
            variant="primary"
            size="md"
            loading={isLoading}
            icon={isLoading ? undefined : <Check className="h-5 w-5" />}
            iconPosition="left"
            disabled={isLoading}
            fullWidth
          >
            {isEditMode ? "Salvar" : "Criar"}
          </Button>

          <Button
            type="button"
            variant="secondary"
            size="md"
            onClick={onCancel}
            icon={<X className="h-5 w-5" />}
            iconPosition="left"
            disabled={isLoading}
            fullWidth
          >
            Cancelar
          </Button>
        </div>
      </form>
    </div>
  );
}
