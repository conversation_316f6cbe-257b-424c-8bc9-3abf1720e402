import React from "react";
import Button from "@/components/ui/Button";

export interface ModalFooterProps {
  primaryAction?: {
    label?: string;
    onClick: () => void;
    disabled?: boolean;
    loading?: boolean;
  };
  secondaryAction?: {
    label?: string;
    onClick: () => void;
    disabled?: boolean;
  };
  children?: React.ReactNode;
  className?: string;
}

export function ModalFooter({
  primaryAction,
  secondaryAction,
  children,
  className = "",
}: ModalFooterProps) {
  if (children) {
    return (
      <div className={`flex gap-3 pt-4 border-t border-gray-200 ${className}`}>
        {children}
      </div>
    );
  }

  return (
    <div className={`flex gap-3 pt-4 border-t border-gray-200 ${className}`}>
      {secondaryAction && (
        <Button
          variant="secondary"
          className="flex-1"
          onClick={secondaryAction.onClick}
          disabled={secondaryAction.disabled}
        >
          {secondaryAction.label || "Cancelar"}
        </Button>
      )}
      {primaryAction && (
        <Button
          variant="primary"
          className="flex-1"
          onClick={primaryAction.onClick}
          disabled={primaryAction.disabled}
          loading={primaryAction.loading}
        >
          {primaryAction.label || "Salvar"}
        </Button>
      )}
    </div>
  );
}

export default ModalFooter;
