import React from "react";
import { Button } from "@/components/ui/Button";
import { Plus } from "lucide-react";

export interface FABProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  icon?: React.ReactNode;
}

export function FAB({
  icon = <Plus className="h-6 w-6" />,
  className = "",
  ...props
}: FABProps) {
  return (
    <Button
      variant="primary"
      className={`fixed w-14 h-14 p-0 !rounded-full shadow-lg flex items-center justify-center ${className}`}
      {...props}
    >
      {icon}
    </Button>
  );
}

export default FAB;
