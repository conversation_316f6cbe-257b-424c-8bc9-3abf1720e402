import React from "react";
import { twMerge } from "tailwind-merge";
import clsx from "clsx";

export interface ToggleProps {
  checked: boolean;
  onChange: (checked: boolean) => void;
  disabled?: boolean;
  className?: string;
  size?: "sm" | "md";
  color?: "green" | "gray";
  ariaLabel?: string;
}

/**
 * Accessible toggle switch built with input[type=checkbox] and Tailwind peer styles.
 * - Uses the same visual style previously inlined in IrrigationPlanConfigPanel.
 * - color="gray" keeps a gray track even when enabled (for unavailable states).
 */
export function Toggle({
  checked,
  onChange,
  disabled = false,
  className,
  size = "md",
  color = "green",
  ariaLabel,
}: ToggleProps) {
  const sizes = {
    sm: {
      track: "w-9 h-5",
      knob: "after:h-4 after:w-4 after:top-[2px] after:left-[2px]",
      translate: "peer-checked:after:translate-x-4",
    },
    md: {
      track: "w-11 h-6",
      knob: "after:h-5 after:w-5 after:top-[2px] after:left-[2px]",
      translate: "peer-checked:after:translate-x-full",
    },
  }[size];

  // Base track classes (rounded, focus ring, position, knob via ::after)
  const baseTrack =
    "relative rounded-full peer focus:outline-none focus:ring-4 focus:ring-blue-300 after:content-[''] after:absolute after:bg-white after:border after:border-gray-300 after:rounded-full after:transition-all";

  // Color logic:
  // - green: normal enabled appearance: gray track when off, green when checked
  // - gray: keep gray track for unavailable state (still shows knob motion if not disabled)
  const colorClasses =
    color === "green" ? "bg-gray-200 peer-checked:bg-green-600" : "bg-gray-100";

  const trackClasses = twMerge(
    clsx(baseTrack, sizes.track, sizes.knob, sizes.translate, colorClasses),
    className
  );

  return (
    <label className="relative inline-flex items-center cursor-pointer">
      <input
        type="checkbox"
        className="sr-only peer"
        checked={checked}
        onChange={(e) => !disabled && onChange(e.target.checked)}
        disabled={disabled}
        aria-label={ariaLabel}
      />
      <div className={clsx(trackClasses, disabled && "cursor-not-allowed")} />
    </label>
  );
}

export default Toggle;
