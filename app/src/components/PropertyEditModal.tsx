import Modal from "@/components/Modal";
import {
  PropertyWizard,
  type PropertyWizardData,
} from "@/components/PropertyWizard";
import {
  currentAccountAtom,
  selectedPropertyAtom,
  selectedPropertyIdAtom,
  updatePropertyAtom,
} from "@/store";
import { useAtomValue, useSet<PERSON>tom } from "jotai";
import { useState } from "react";
import { useToast } from "./Toast";

interface PropertyEditModalProps {
  isOpen: boolean;
  onClose: () => void;
}

function PropertyEditModal({ isOpen, onClose }: PropertyEditModalProps) {
  const [isPropertyWizardLoading, setIsPropertyWizardLoading] = useState(false);
  const selectedPropertyId = useAtomValue(selectedPropertyIdAtom);
  const selectedProperty = useAtomValue(selectedPropertyAtom);
  const currentAccount = useAtomValue(currentAccountAtom);
  const updateProperty = useSetAtom(updatePropertyAtom);
  const { showSuccess } = useToast();

  const handlePropertyWizardSubmit = async (data: PropertyWizardData) => {
    if (!selectedPropertyId) return;

    setIsPropertyWizardLoading(true);
    try {
      // Convert PropertyWizard Point to GeoJSON Point format
      let geoJsonPoint: GeoJSON.Point | null = null;
      if (data.point) {
        geoJsonPoint = {
          type: "Point",
          coordinates: data.point.coordinates,
        };
      }

      await updateProperty({
        id: selectedPropertyId,
        data: {
          name: data.name,
          timezone: data.timezone,
          point: geoJsonPoint,
          address_street_name: data.address_street_name,
          address_street_number: data.address_street_number,
          address_complement: data.address_complement,
          address_neighborhood: data.address_neighborhood,
          address_city: data.address_city,
          address_state: data.address_state,
          address_postal_code: data.address_postal_code,
          address_country: data.address_country,
          backwash_duration_minutes: data.backwash_duration_minutes,
          backwash_period_minutes: data.backwash_period_minutes,
          backwash_delay_seconds: data.backwash_delay_seconds,
          rain_gauge_enabled: data.rain_gauge_enabled,
          rain_gauge_resolution_mm: data.rain_gauge_resolution_mm,
          precipitation_volume_limit_mm: data.precipitation_volume_limit_mm,
          precipitation_suspended_duration_hours:
            data.precipitation_suspended_duration_hours,
        },
      });
      showSuccess({
        message: "Propriedade atualizada com sucesso!",
      });
      onClose();
    } catch (error) {
      console.error("Failed to update property:", error);
      // Error handling is already done by the withOperationHandlingAtom
    } finally {
      setIsPropertyWizardLoading(false);
    }
  };

  const handlePropertyWizardCancel = () => {
    onClose();
  };

  // Transform property data to wizard data
  const getPropertyWizardData = (): Partial<PropertyWizardData> => {
    if (!selectedProperty) return {};

    // Convert GeoJSON Point to PropertyWizard Point format
    let formPoint: { type: "Point"; coordinates: [number, number] } | null =
      null;
    if (selectedProperty.point && selectedProperty.point.coordinates) {
      const [lng, lat] = selectedProperty.point.coordinates;
      if (typeof lng === "number" && typeof lat === "number") {
        formPoint = {
          type: "Point",
          coordinates: [lng, lat],
        };
      }
    }

    return {
      account: currentAccount?.id || "",
      name: selectedProperty.name || "",
      timezone: selectedProperty.timezone || "America/Sao_Paulo",
      point: formPoint,
      address_postal_code: selectedProperty.address_postal_code,
      address_street_name: selectedProperty.address_street_name,
      address_street_number: selectedProperty.address_street_number,
      address_complement: selectedProperty.address_complement,
      address_neighborhood: selectedProperty.address_neighborhood,
      address_city: selectedProperty.address_city,
      address_state: selectedProperty.address_state,
      address_country: selectedProperty.address_country || "Brasil",
      backwash_duration_minutes: selectedProperty.backwash_duration_minutes,
      backwash_period_minutes: selectedProperty.backwash_period_minutes,
      backwash_delay_seconds: selectedProperty.backwash_delay_seconds,
      rain_gauge_enabled: selectedProperty.rain_gauge_enabled || false,
      rain_gauge_resolution_mm: selectedProperty.rain_gauge_resolution_mm,
      precipitation_volume_limit_mm:
        selectedProperty.precipitation_volume_limit_mm,
      precipitation_suspended_duration_hours:
        selectedProperty.precipitation_suspended_duration_hours,
    };
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Editar Propriedade"
      size="xl"
    >
      <PropertyWizard
        onSubmit={handlePropertyWizardSubmit}
        onCancel={handlePropertyWizardCancel}
        initialData={getPropertyWizardData()}
        isEditMode={true}
        isLoading={isPropertyWizardLoading}
      />
    </Modal>
  );
}

export default PropertyEditModal;
