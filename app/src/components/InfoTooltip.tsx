import { Info } from "lucide-react";
import { Tooltip } from "react-tooltip";

interface InfoTooltipProps {
  content: string;
  className?: string;
}

/**
 * InfoTooltip - A clickable info icon that displays helpful text in a tooltip
 *
 * @param content - The informational text to display in the tooltip
 * @param className - Optional CSS classes for styling
 */
function InfoTooltip({ content, className = "" }: InfoTooltipProps) {
  const randomId = `tooltip-${Math.random().toString(36).substring(2, 15)}`;
  return (
    <>
      <a data-tooltip-id={randomId} data-tooltip-content={content}>
        <Info className="h-4 w-4 text-info" />
      </a>
      <Tooltip
        id={randomId}
        className="max-w-xs bg-neutral-50! border border-gray-200 rounded-lg shadow-lg text-sm text-gray-700! leading-relaxed"
      />
    </>
  );
}

export default InfoTooltip;
