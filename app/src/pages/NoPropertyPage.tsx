import React from "react";
import { useLocation } from "wouter";

function NoPropertyPage() {
  const [, setLocation] = useLocation();

  const handleCreateProperty = () => {
    // TODO: Implement property creation flow
    setLocation("/property/create");
  };

  const handleBackToAccounts = () => {
    setLocation("/select-account");
  };

  return (
    <div className="min-h-screen bg-green-50 flex items-center justify-center py-8 px-4">
      <div className="max-w-md mx-auto text-center">
        {/* Icon */}
        <div className="mx-auto w-20 h-20 bg-orange-100 rounded-2xl flex items-center justify-center mb-6">
          <svg
            className="w-10 h-10 text-orange-600"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
            />
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
            />
          </svg>
        </div>

        {/* Title */}
        <h1 className="text-2xl font-bold text-gray-900 mb-3">
          Nenhuma Propriedade Encontrada
        </h1>

        {/* Description */}
        <p className="text-gray-600 mb-8 leading-relaxed">
          Esta conta não possui propriedades cadastradas. Para começar a usar o
          sistema de irrigação, você precisa criar pelo menos uma propriedade.
        </p>

        {/* Actions */}
        <div className="space-y-4">
          <button
            onClick={handleCreateProperty}
            className="w-full inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-green-600 hover:bg-green-700 transition-colors"
          >
            <svg
              className="w-5 h-5 mr-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 6v6m0 0v6m0-6h6m-6 0H6"
              />
            </svg>
            Criar Primeira Propriedade
          </button>

          <button
            onClick={handleBackToAccounts}
            className="w-full inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-base font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-colors"
          >
            <svg
              className="w-5 h-5 mr-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M10 19l-7-7m0 0l7-7m-7 7h18"
              />
            </svg>
            Voltar às Contas
          </button>
        </div>

        {/* Help Section */}
        <div className="mt-8 p-4 bg-blue-50 rounded-lg border border-blue-200">
          <div className="flex items-start">
            <svg
              className="w-5 h-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <div className="text-left">
              <h3 className="text-sm font-medium text-blue-900 mb-1">
                Precisa de ajuda?
              </h3>
              <p className="text-sm text-blue-700">
                Uma propriedade representa um local físico onde você instalará
                dispositivos de irrigação. Você pode adicionar múltiplas
                propriedades por conta.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default NoPropertyPage;
