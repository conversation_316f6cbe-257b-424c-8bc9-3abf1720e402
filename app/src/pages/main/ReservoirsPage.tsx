import { appShell, reservoirs<PERSON>tom } from "@/store";
import { useAtomValue, useSet<PERSON>tom } from "jotai";
import { Droplets } from "lucide-react";
import { useEffect } from "react";
import { useLocation } from "wouter";
import { ReservoirCard } from "./components/ReservoirCard";
import { FAB } from "@/components/ui/FAB";

export function ReservoirsPage() {
  const setBackButton = useSetAtom(appShell.backButtonAtom);
  useEffect(() => {
    setBackButton(false);
  });

  const [, setLocation] = useLocation();
  const reservoirs = useAtomValue(reservoirsAtom);

  const handleCreateReservoir = () => {
    setLocation("/app/reservoirs/new");
  };

  const handleReservoirClick = (reservoirId: string) => {
    setLocation(`/app/reservoirs/${reservoirId}`);
  };

  return (
    <div className="p-4 pb-20 bg-gray-50 min-h-screen">
      <h1 className="text-2xl font-bold text-gray-900 mb-6">Reservatórios</h1>

      {reservoirs.length === 0 ? (
        <div className="bg-white rounded-xl border border-gray-200 p-8 text-center">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Droplets className="w-8 h-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Nenhum reservatório encontrado
          </h3>
          <p className="text-gray-600">
            Crie seu primeiro reservatório para começar a gerenciar o
            armazenamento de água da propriedade.
          </p>
        </div>
      ) : (
        <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
          {reservoirs.map((reservoir) => (
            <ReservoirCard
              key={reservoir.id}
              reservoirId={reservoir.id}
              onClick={() => handleReservoirClick(reservoir.id)}
            />
          ))}
        </div>
      )}

      {/* Floating Action Button */}
      <FAB onClick={handleCreateReservoir} className="right-4 bottom-20" />
    </div>
  );
}
