import { DeviceModel } from "@/api/model/device";
import { ChevronRight, Plus } from "lucide-react";
import Button from "@/components/ui/Button";
import { FAB } from "@/components/ui/FAB";

interface Device {
  id: string;
  name: string;
  model: DeviceModel;
  identifier: string;
}

interface Sector {
  id: string;
  projectId: string;
  name: string;
  description: string;
  controllerId: string;
  controllerOutput: number; // 1, 2, 3, or 4
  area: number;
}

interface SectorsPanelProps {
  sectors: Sector[];
  devices: Device[];
  onEditSector: (sectorId: string) => void;
  onAddSector: () => void;
}

function SectorsPanel({
  sectors,
  devices,
  onEditSector,
  onAddSector,
}: SectorsPanelProps) {
  return (
    <div className="p-6 space-y-4">
      {sectors.length > 0 ? (
        sectors.map((sector) => (
          <div
            key={sector.id}
            className="bg-white rounded-xl border border-neutral-100 p-6 hover:shadow-md transition-all duration-150 ease-in-out cursor-pointer"
            onClick={() => onEditSector(sector.id)}
          >
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <h3 className="font-medium text-neutral-900 mb-2 text-base">
                  {sector.name}
                </h3>
                <div className="text-sm text-neutral-600 space-y-1">
                  <div>
                    Controlador:{" "}
                    {devices.find((d) => d.id === sector.controllerId)
                      ?.identifier || "N/A"}
                  </div>
                  <div>Saída: {sector.controllerOutput}</div>
                  {sector.area > 0 && (
                    <div>Área: {sector.area.toFixed(2)} hectares</div>
                  )}
                </div>
              </div>
              <span className="p-2 text-neutral-400 group-hover:text-neutral-600 transition-colors">
                <ChevronRight className="h-5 w-5" />
              </span>
            </div>
          </div>
        ))
      ) : (
        <div className="text-center py-12">
          <div className="text-neutral-400 mb-4">
            <svg
              className="mx-auto h-12 w-12"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1}
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-neutral-900 mb-2">
            Nenhum setor encontrado
          </h3>
          <p className="text-neutral-600 mb-6 max-w-sm mx-auto">
            Adicione seu primeiro setor de irrigação para começar a gerenciar
            suas zonas de cultivo.
          </p>
          <Button
            onClick={onAddSector}
            icon={<Plus className="h-4 w-4" />}
            iconPosition="left"
          >
            Adicionar Setor
          </Button>
        </div>
      )}

      {/* Add New Sector Button - Only show when there are sectors */}
      {sectors.length > 0 && <FAB onClick={onAddSector} />}
    </div>
  );
}

export default SectorsPanel;
