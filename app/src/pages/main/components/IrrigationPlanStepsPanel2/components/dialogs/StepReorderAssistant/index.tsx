import Modal from "@/components/Modal";
import { useState, useEffect } from "react";
import { IrrigationPlanStepFormData } from "@/pages/main/IrrigationPlanWizardPage";
import Button from "@/components/ui/Button";

interface StepReorderAssistantProps {
  isOpen: boolean;
  onClose: () => void;
  steps: IrrigationPlanStepFormData[];
  onReorderSteps: (newOrder: IrrigationPlanStepFormData[]) => void;
}

interface SectorInfo {
  id: string;
  name: string;
  step: IrrigationPlanStepFormData;
}

/**
 * StepReorderAssistant - Interactive component for reordering irrigation plan steps
 *
 * Provides an intuitive interface for users to reorder sectors by clicking them
 * in the desired sequence. Features mobile-friendly design with large touch targets.
 */
function StepReorderAssistant({
  isOpen,
  onClose,
  steps,
  onReorderSteps,
}: StepReorderAssistantProps) {
  const [selectedSectors, setSelectedSectors] = useState<SectorInfo[]>([]);
  const [availableSectors, setAvailableSectors] = useState<SectorInfo[]>([]);
  const [showCancelConfirm, setShowCancelConfirm] = useState(false);
  const [showResetConfirm, setShowResetConfirm] = useState(false);

  // Ordinal numbers for questions
  const ordinals = [
    "1º",
    "2º",
    "3º",
    "4º",
    "5º",
    "6º",
    "7º",
    "8º",
    "9º",
    "10º",
  ];

  // Initialize sectors when modal opens
  useEffect(() => {
    if (isOpen) {
      const sectors: SectorInfo[] = steps.map((step) => ({
        id: step.sectorId,
        name: step.sectorName,
        step,
      }));
      setAvailableSectors(sectors);
      setSelectedSectors([]);
    }
  }, [isOpen, steps]);

  // Get current question text
  const getQuestionText = () => {
    if (
      selectedSectors.length ===
      availableSectors.length + selectedSectors.length
    ) {
      return "Ordem completa! Confirme se está correta:";
    }
    const ordinal =
      ordinals[selectedSectors.length] || `${selectedSectors.length + 1}º`;
    return `Clique no ${ordinal} setor a irrigar.`;
  };

  // Handle sector selection
  const handleSectorClick = (sector: SectorInfo) => {
    setSelectedSectors((prev) => [...prev, sector]);
    setAvailableSectors((prev) => prev.filter((s) => s.id !== sector.id));
  };

  // Handle reset
  const handleReset = () => {
    if (selectedSectors.length > 0) {
      setShowResetConfirm(true);
    } else {
      performReset();
    }
  };

  const performReset = () => {
    const allSectors: SectorInfo[] = steps.map((step) => ({
      id: step.sectorId,
      name: step.sectorName,
      step,
    }));
    setAvailableSectors(allSectors);
    setSelectedSectors([]);
    setShowResetConfirm(false);
  };

  // Handle cancel
  const handleCancel = () => {
    if (selectedSectors.length > 0) {
      setShowCancelConfirm(true);
    } else {
      onClose();
    }
  };

  const performCancel = () => {
    setShowCancelConfirm(false);
    onClose();
  };

  // Handle confirm order
  const handleConfirmOrder = () => {
    if (selectedSectors.length === 0) {
      return;
    }

    // Create new ordered steps
    const reorderedSteps = selectedSectors.map((sector, index) => ({
      ...sector.step,
      order: index + 1,
    }));

    onReorderSteps(reorderedSteps);
    onClose();
  };

  // Check if confirm button should be enabled - require all sectors to be selected
  const totalSectors = steps.length;
  const isConfirmEnabled = selectedSectors.length === totalSectors;

  return (
    <>
      <Modal
        title="Reordenar Setores"
        isOpen={isOpen}
        onClose={handleCancel}
        showCloseButton={true}
        size="lg"
      >
        <div className="space-y-6">
          {/* Question Section */}
          <div className="text-center">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              {getQuestionText()}
            </h3>
          </div>

          {/* Order Display */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="text-sm font-medium text-gray-700 mb-2">
              Nova ordem:
            </div>
            <div className="text-gray-900">
              {selectedSectors.length === 0 ? (
                <span className="text-gray-500 italic">
                  Nenhum setor selecionado ainda
                </span>
              ) : (
                selectedSectors.map((sector) => sector.name).join(", ")
              )}
            </div>
          </div>

          {/* Sector Grid */}
          {availableSectors.length > 0 && (
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              {availableSectors.map((sector) => (
                <button
                  key={sector.id}
                  onClick={() => handleSectorClick(sector)}
                  className="min-h-[60px] px-4 py-3 bg-white border-2 border-gray-300 rounded-lg hover:border-green-500 hover:bg-green-50 transition-colors text-center font-medium text-gray-900 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                >
                  {sector.name}
                </button>
              ))}
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 pt-4 ">
            <Button
              variant="neutral"
              outline
              onClick={handleCancel}
              className=""
            >
              Cancelar
            </Button>
            <Button variant="warning" outline onClick={handleReset}>
              Recomeçar
            </Button>
            <Button
              variant="primary"
              onClick={handleConfirmOrder}
              disabled={!isConfirmEnabled}
            >
              Confirmar Ordem
            </Button>
          </div>
        </div>
      </Modal>

      {/* Cancel Confirmation Modal - Higher z-index */}
      {showCancelConfirm && (
        <div
          className="fixed inset-0 flex items-center justify-center p-4 bg-black/50"
          style={{ zIndex: 70 }}
          onClick={() => setShowCancelConfirm(false)}
        >
          <div
            className="bg-white rounded-lg shadow-xl max-w-md w-full"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Cancelar Reordenação
              </h3>
              <p className="text-gray-600 mb-6">
                Tem certeza que deseja cancelar? A seleção atual será perdida.
              </p>
              <div className="flex justify-end gap-3">
                <Button
                  onClick={() => setShowCancelConfirm(false)}
                  variant="secondary"
                >
                  Continuar
                </Button>
                <Button onClick={performCancel} variant="warning">
                  Sim, cancelar
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Reset Confirmation Modal - Higher z-index */}
      {showResetConfirm && (
        <div
          className="fixed inset-0 flex items-center justify-center p-4 bg-black/50"
          style={{ zIndex: 70 }}
          onClick={() => setShowResetConfirm(false)}
        >
          <div
            className="bg-white rounded-lg shadow-xl max-w-md w-full"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Recomeçar Seleção
              </h3>
              <p className="text-gray-600 mb-6">
                Tem certeza que deseja recomeçar? A seleção atual será perdida.
              </p>
              <div className="flex justify-end gap-3">
                <Button
                  onClick={() => setShowResetConfirm(false)}
                  variant="secondary"
                >
                  Continuar
                </Button>
                <Button onClick={performReset} variant="warning">
                  Sim, recomeçar
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}

export default StepReorderAssistant;
