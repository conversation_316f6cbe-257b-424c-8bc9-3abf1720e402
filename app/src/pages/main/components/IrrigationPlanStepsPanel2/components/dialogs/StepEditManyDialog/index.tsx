import Modal from "@/components/Modal";
import { IrrigationPlanStepFormData } from "@/pages/main/IrrigationPlanWizardPage";
import { useEffect, useState } from "react";
import { StepEditManyDialogProps } from "../../../types";
import { validateMultipleStepsWithUpdates } from "../../../utils/validation";
import Button from "@/components/ui/Button";

/**
 * Dialog component for bulk editing multiple irrigation plan steps.
 * Allows modification of duration and fertigation settings for selected steps.
 */
function StepEditManyDialog({
  isOpen,
  onClose,
  selectedSteps,
  fertigationEnabled,
  projectData,
  onSave,
}: StepEditManyDialogProps) {
  const [formData, setFormData] = useState<{
    durationMinutes: string;
    fertigationStartDelayMinutes: string;
    fertigationDurationMinutes: string;
  }>({
    durationMinutes: "",
    fertigationStartDelayMinutes: "",
    fertigationDurationMinutes: "",
  });

  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  // Get pipe wash time from project data, default to 0 if not available
  const pipeWashTimeSeconds = projectData?.pipe_wash_time_seconds || 0;

  // Reset form when dialog opens/closes
  useEffect(() => {
    if (isOpen) {
      // Reset to empty form when dialog opens
      setFormData({
        durationMinutes: "",
        fertigationStartDelayMinutes: "",
        fertigationDurationMinutes: "",
      });
      setValidationErrors([]);
    }
  }, [isOpen]);

  // Validate fertigation timing whenever form data changes
  useEffect(() => {
    if (!fertigationEnabled || selectedSteps.length === 0) {
      setValidationErrors([]);
      return;
    }

    // Create updates object from current form values
    const updates: Partial<IrrigationPlanStepFormData> = {};

    if (formData.durationMinutes.trim() !== "") {
      const duration = parseInt(formData.durationMinutes);
      if (!isNaN(duration) && duration > 0) {
        updates.durationMinutes = duration;
      }
    }

    if (formData.fertigationStartDelayMinutes.trim() !== "") {
      const delay = parseInt(formData.fertigationStartDelayMinutes);
      if (!isNaN(delay) && delay >= 0) {
        updates.fertigationStartDelayMinutes = delay;
      }
    }

    if (formData.fertigationDurationMinutes.trim() !== "") {
      const duration = parseInt(formData.fertigationDurationMinutes);
      if (!isNaN(duration) && duration >= 0) {
        updates.fertigationDurationMinutes = duration;
      }
    }

    // Only validate if there are actual updates to apply
    if (Object.keys(updates).length > 0) {
      const validationResults = validateMultipleStepsWithUpdates(
        selectedSteps,
        updates,
        pipeWashTimeSeconds
      );

      const errors = validationResults.map(
        (result) =>
          `${result.step.sectorName}: ${result.validation.errorMessage}`
      );
      setValidationErrors(errors);
    } else {
      setValidationErrors([]);
    }
  }, [formData, selectedSteps, fertigationEnabled, pipeWashTimeSeconds]);

  const handleSave = () => {
    // Don't save if there are validation errors
    if (validationErrors.length > 0) {
      return;
    }

    const updates: Partial<IrrigationPlanStepFormData> = {};

    // Only include fields with valid values
    if (formData.durationMinutes.trim() !== "") {
      const duration = parseInt(formData.durationMinutes);
      if (!isNaN(duration) && duration > 0) {
        updates.durationMinutes = duration;
      }
    }

    if (fertigationEnabled) {
      if (formData.fertigationStartDelayMinutes.trim() !== "") {
        const delay = parseInt(formData.fertigationStartDelayMinutes);
        if (!isNaN(delay) && delay >= 0) {
          updates.fertigationStartDelayMinutes = delay;
        }
      }

      if (formData.fertigationDurationMinutes.trim() !== "") {
        const duration = parseInt(formData.fertigationDurationMinutes);
        if (!isNaN(duration) && duration >= 0) {
          updates.fertigationDurationMinutes = duration;
        }
      }
    }

    onSave(updates);
    onClose();
  };

  const handleCancel = () => {
    setFormData({
      durationMinutes: "",
      fertigationStartDelayMinutes: "",
      fertigationDurationMinutes: "",
    });
    setValidationErrors([]);
    onClose();
  };

  const selectedSectorNames = selectedSteps
    .map((step) => step.sectorName)
    .join(", ");

  return (
    <Modal
      title="Editar Setores Selecionados"
      isOpen={isOpen}
      onClose={handleCancel}
      showCloseButton={true}
    >
      <div className="space-y-4">
        {/* Selected Sectors */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Setores Selecionados
          </label>
          <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-lg text-gray-900 text-sm">
            {selectedSectorNames}
          </div>
        </div>

        {/* Duration */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Duração (minutos) - deixe vazio para não alterar
          </label>
          <input
            type="number"
            min="1"
            max="1440"
            value={formData.durationMinutes}
            onChange={(e) =>
              setFormData((prev) => ({
                ...prev,
                durationMinutes: e.target.value,
              }))
            }
            placeholder="Ex: 15"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
          />
        </div>

        {/* Fertigation fields */}
        {fertigationEnabled && (
          <>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Atraso da Fertirrigação (minutos) - deixe vazio para não alterar
              </label>
              <input
                type="number"
                min="0"
                max="1440"
                value={formData.fertigationStartDelayMinutes}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    fertigationStartDelayMinutes: e.target.value,
                  }))
                }
                placeholder="Ex: 0"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Duração da Fertirrigação (minutos) - deixe vazio para não
                alterar
              </label>
              <input
                type="number"
                min="0"
                max="1440"
                value={formData.fertigationDurationMinutes}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    fertigationDurationMinutes: e.target.value,
                  }))
                }
                placeholder="Ex: 5"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
          </>
        )}

        {/* Validation Errors */}
        {validationErrors.length > 0 && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-3">
            <p className="text-sm font-medium text-red-700 mb-2">
              Erros de validação:
            </p>
            <ul className="text-sm text-red-700 space-y-1">
              {validationErrors.map((error, index) => (
                <li key={index} className="flex items-start">
                  <span className="mr-2">•</span>
                  <span>{error}</span>
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex justify-end gap-3 pt-4 border-t">
          <Button onClick={handleCancel} variant="secondary">
            Cancelar
          </Button>
          <Button
            onClick={handleSave}
            disabled={validationErrors.length > 0}
            variant="primary"
          >
            Salvar Alterações
          </Button>
        </div>
      </div>
    </Modal>
  );
}

export default StepEditManyDialog;
