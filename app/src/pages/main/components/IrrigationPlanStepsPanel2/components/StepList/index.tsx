import StepCard from "../StepCard";
import { StepListProps } from "../../types";

/**
 * Component that manages the list of step cards.
 * Renders list of StepCard components, handles empty state, and manages list-level interactions.
 */
function StepList({
  steps,
  selectedSteps,
  fertigationEnabled,
  readOnly,
  onToggleStepSelect,
  onSelectAll,
  onClearSelection,
  onMoveStep,
  onEditStep,
  onDeleteStep,
}: StepListProps) {
  if (steps.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        Nenhum setor selecionado. Clique em "Escolher Setores" para começar.
      </div>
    );
  }

  // Calculate select all checkbox state
  const allStepIds = steps.map((step) => step.id);
  const allSelected =
    allStepIds.length > 0 && allStepIds.every((id) => selectedSteps.has(id));
  const someSelected = allStepIds.some((id) => selectedSteps.has(id));
  const isIndeterminate = someSelected && !allSelected;

  // <PERSON><PERSON> select all toggle
  const handleSelectAllToggle = () => {
    if (allSelected) {
      onClearSelection();
    } else {
      onSelectAll(allStepIds);
    }
  };

  return (
    <div className="space-y-3">
      {/* Select All Header */}
      {!readOnly && (
        <div className="flex items-center gap-3 px-4 py-2 bg-gray-50 rounded-lg border border-gray-200">
          <div className="relative">
            <input
              type="checkbox"
              checked={allSelected}
              ref={(input) => {
                if (input) input.indeterminate = isIndeterminate;
              }}
              onChange={handleSelectAllToggle}
              className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
            />
          </div>
          <span className="text-sm font-medium text-gray-700">
            {allSelected
              ? `Todos selecionados (${selectedSteps.size})`
              : someSelected
                ? `${selectedSteps.size} de ${steps.length} selecionados`
                : "Selecionar todos"}
          </span>
        </div>
      )}

      {/* Step Cards */}
      {steps.map((step, index) => (
        <StepCard
          key={step.id}
          step={step}
          index={index}
          totalSteps={steps.length}
          isSelected={selectedSteps.has(step.id)}
          fertigationEnabled={fertigationEnabled}
          readOnly={readOnly}
          onToggleSelect={onToggleStepSelect}
          onMoveUp={() => onMoveStep(step.id, "up")}
          onMoveDown={() => onMoveStep(step.id, "down")}
          onEdit={onEditStep}
          onDelete={onDeleteStep}
        />
      ))}
    </div>
  );
}

export default StepList;
