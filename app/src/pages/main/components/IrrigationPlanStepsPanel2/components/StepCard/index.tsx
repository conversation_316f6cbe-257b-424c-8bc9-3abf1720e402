import { ChevronDown, ChevronUp, Edit, Trash2 } from "lucide-react";
import { StepCardProps } from "../../types";
import { formatMinutesToReadable } from "../../utils/formatters";
import Button from "@/components/ui/Button";

/**
 * Individual step card component that displays step details and basic interactions.
 * Shows step details, handles move up/down, edit, delete actions, and checkbox selection.
 */
function StepCard({
  step,
  index,
  totalSteps,
  isSelected,
  fertigationEnabled,
  readOnly,
  onToggleSelect,
  onMoveUp,
  onMoveDown,
  onEdit,
  onDelete,
}: StepCardProps) {
  return (
    <div className="bg-white rounded-xl border border-gray-200 p-4">
      <div className="flex items-start gap-3">
        {/* Move Controls */}
        <div className="flex flex-col gap-1 pt-1">
          <Button
            onClick={() => onMoveUp(step.id)}
            disabled={readOnly || index === 0}
            icon={<ChevronUp className="h-4 w-4" />}
            variant="ghost"
            size="sm"
            aria-label="Mover para cima"
          />
          <div className="text-xs text-gray-500 text-center font-medium">
            {step.order}
          </div>
          <Button
            onClick={() => onMoveDown(step.id)}
            disabled={readOnly || index === totalSteps - 1}
            icon={<ChevronDown className="h-4 w-4" />}
            variant="ghost"
            size="sm"
            aria-label="Mover para baixo"
          />
        </div>

        {/* Checkbox and Content */}
        <div className="flex-1 flex items-start gap-3">
          <div className="flex-1">
            {/* Header */}
            <div className="flex flex-1 items-center justify-between mb-3">
              {/* Checkbox and Sector Name */}
              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={isSelected}
                  onChange={() => onToggleSelect(step.id)}
                  disabled={readOnly}
                  className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded disabled:opacity-50"
                />
                <h4 className="font-semibold text-gray-900">
                  {step.sectorName}
                </h4>
              </div>
              {/* Edit and Delete Buttons */}
              <div className="flex gap-2">
                <Button
                  onClick={() => onEdit(step.id)}
                  disabled={readOnly}
                  icon={<Edit className="h-4 w-4" />}
                  variant="ghost"
                  size="sm"
                  aria-label="Editar"
                />
                <Button
                  onClick={() => onDelete(step.id)}
                  disabled={readOnly}
                  icon={<Trash2 className="h-4 w-4" />}
                  variant="destructive"
                  ghost
                  size="sm"
                  aria-label="Excluir"
                />
              </div>
            </div>

            {/* Step Details */}
            <div
              className={`grid ${
                fertigationEnabled ? "grid-cols-3" : "grid-cols-1"
              } gap-x-1 text-sm items-center`}
            >
              {/* Titles row */}
              <div className="flex flex-col justify-center items-start text-gray-600 mb-0">
                Duração
              </div>
              {fertigationEnabled && (
                <>
                  <div className="flex flex-col justify-center items-start text-gray-600 mb-0">
                    Atraso Ferti
                  </div>
                  <div className="flex flex-col justify-center items-start text-gray-600 mb-0">
                    Duração Ferti
                  </div>
                </>
              )}
              {/* Values row */}
              <div className="flex flex-col justify-center items-start font-medium">
                {formatMinutesToReadable(step.durationMinutes)}
              </div>
              {fertigationEnabled && (
                <>
                  <div className="flex flex-col justify-center items-start font-medium">
                    {step.fertigationStartDelayMinutes !== undefined
                      ? formatMinutesToReadable(
                          step.fertigationStartDelayMinutes
                        )
                      : "-"}
                  </div>
                  <div className="flex flex-col justify-center items-start font-medium">
                    {step.fertigationDurationMinutes !== undefined
                      ? formatMinutesToReadable(step.fertigationDurationMinutes)
                      : "-"}
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default StepCard;
