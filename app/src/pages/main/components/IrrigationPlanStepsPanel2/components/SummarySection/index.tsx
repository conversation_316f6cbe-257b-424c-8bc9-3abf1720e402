import { SummarySectionProps } from "../../types";
import { formatDuration } from "../../utils/formatters";
import { calculateTotalDuration } from "../../utils/stepHelpers";

/**
 * Summary section component that displays irrigation plan overview.
 * Shows total duration, step count, available sectors, and estimated completion time.
 */
function SummarySection({ steps, sectors, planData }: SummarySectionProps) {
  /**
   * Calculates total duration of all steps.
   */
  const getTotalDuration = () => {
    return calculateTotalDuration(steps);
  };

  /**
   * Calculates the estimated completion time based on start time and total duration.
   */
  const getEstimatedCompletionTime = () => {
    if (!planData.startTime) return "Não definido";

    const [hours, minutes] = planData.startTime.split(":").map(Number);
    const startTimeMinutes = hours * 60 + minutes;
    const totalDurationMinutes = Math.floor(getTotalDuration() / 60);
    const finishTimeMinutes = startTimeMinutes + totalDurationMinutes;

    const finishHours = Math.floor(finishTimeMinutes / 60) % 24;
    const finishMinutes = finishTimeMinutes % 60;

    return `${finishHours.toString().padStart(2, "0")}:${finishMinutes
      .toString()
      .padStart(2, "0")}`;
  };

  return (
    <div className="bg-white rounded-xl border border-gray-200 p-4">
      <div className="grid grid-cols-2 gap-4 text-center">
        <div className="bg-gray-50 rounded-lg p-3">
          <div className="text-lg font-bold text-gray-900">
            {formatDuration(getTotalDuration())}
          </div>
          <div className="text-xs text-gray-600">Duração total</div>
        </div>
        <div className="bg-gray-50 rounded-lg p-3">
          <div className="text-lg font-bold text-gray-900">
            {getEstimatedCompletionTime()}
          </div>
          <div className="text-xs text-gray-600">Término estimado</div>
        </div>
        <div className="bg-gray-50 rounded-lg p-3">
          <div className="text-lg font-bold text-gray-900">{steps.length}</div>
          <div className="text-xs text-gray-600">Setores</div>
        </div>
        <div className="bg-gray-50 rounded-lg p-3">
          <div className="text-lg font-bold text-gray-900">
            {sectors.length - steps.length}
          </div>
          <div className="text-xs text-gray-600">Disponíveis</div>
        </div>
      </div>
    </div>
  );
}

export default SummarySection;
