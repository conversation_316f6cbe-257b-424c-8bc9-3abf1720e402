/**
 * Formats duration in seconds to human-readable string.
 * @param seconds - Duration in seconds
 * @returns Formatted duration string (e.g., "15 min", "1m 30s")
 */
export const formatDuration = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  if (remainingSeconds === 0) {
    return `${minutes}min`;
  }
  return `${minutes}m ${remainingSeconds}s`;
};

/**
 * Formats duration in minutes to human-readable string.
 * @param minutes - Duration in minutes
 * @returns Formatted duration string (e.g., "15min", "1h 30min", "2h")
 */
export const formatMinutesToReadable = (minutes: number): string => {
  if (minutes >= 60) {
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return remainingMinutes > 0
      ? `${hours}h ${remainingMinutes}min`
      : `${hours}h`;
  }
  return `${minutes}min`;
};
