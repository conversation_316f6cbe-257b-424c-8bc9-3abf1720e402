import { IrrigationPlanStepFormData } from "@/pages/main/IrrigationPlanWizardPage";
import { formatDuration } from "./formatters";

/**
 * Validation result for fertigation timing constraints
 */
export interface FertigationValidationResult {
  isValid: boolean;
  errorMessage?: string;
  maxAvailableMinutes?: number;
  totalFertigationTime?: number;
}

/**
 * Validates fertigation timing constraints according to irrigation documentation.
 *
 * Constraint: Start Delay + Duration + Pipe Wash Time ≤ Irrigation Duration
 *
 * This ensures that:
 * - The fertigation solution is properly washed away by irrigation water
 * - The next sector doesn't get contaminated by the previous sector's fertigation solution
 */
export const validateFertigationTiming = (
  step: IrrigationPlanStepFormData,
  pipeWashTimeSeconds: number
): FertigationValidationResult => {
  // If no fertigation configured, validation passes
  if (!step.fertigationStartDelayMinutes && !step.fertigationDurationMinutes) {
    return { isValid: true };
  }

  const startDelaySeconds = (step.fertigationStartDelayMinutes || 0) * 60;
  const durationSeconds = (step.fertigationDurationMinutes || 0) * 60;
  const totalFertigationTime =
    startDelaySeconds + durationSeconds + pipeWashTimeSeconds;

  const stepDurationSeconds = (step.durationMinutes || 0) * 60;
  if (totalFertigationTime > stepDurationSeconds) {
    const availableTimeSeconds = Math.max(
      0,
      stepDurationSeconds - pipeWashTimeSeconds
    );
    const maxAvailableMinutes = Math.floor(availableTimeSeconds / 60);

    return {
      isValid: false,
      errorMessage: pipeWashTimeSeconds
        ? `Fertirrigação não pode exceder o tempo disponível de ${formatDuration(pipeWashTimeSeconds)} para a lavagem do tubo`
        : `Fertirrigação não pode exceder a duração de ${formatDuration(stepDurationSeconds)} da irrigação`,
      maxAvailableMinutes,
      totalFertigationTime,
    };
  }

  return { isValid: true };
};

/**
 * Validates multiple steps with the same fertigation timing updates.
 * Returns validation results for each step that would have validation errors.
 */
export const validateMultipleStepsWithUpdates = (
  steps: IrrigationPlanStepFormData[],
  updates: Partial<IrrigationPlanStepFormData>,
  pipeWashTimeSeconds: number
): { step: IrrigationPlanStepFormData; validation: FertigationValidationResult }[] => {
  const validationErrors: { step: IrrigationPlanStepFormData; validation: FertigationValidationResult }[] = [];

  for (const step of steps) {
    // Create a temporary step with the proposed updates
    const updatedStep: IrrigationPlanStepFormData = {
      ...step,
      ...updates,
    };

    const validation = validateFertigationTiming(updatedStep, pipeWashTimeSeconds);
    if (!validation.isValid) {
      validationErrors.push({ step, validation });
    }
  }

  return validationErrors;
};
