import {
  IrrigationPlanFormData,
  IrrigationPlanStepFormData,
} from "@/pages/main/IrrigationPlanWizardPage";
import { useCallback, useMemo } from "react";
import { Sector } from "../types";
import { createNewStep } from "../utils/stepHelpers";

/**
 * Props for the useSectorManagement hook.
 */
interface UseSectorManagementProps {
  steps: IrrigationPlanStepFormData[];
  sectors: Sector[];
  planData: IrrigationPlanFormData;
  onRemoveStep: (stepId: string) => Promise<void>;
  onUpdateStep: (
    stepId: string,
    updates: Partial<IrrigationPlanStepFormData>
  ) => void;
  onSaveStep: (step: IrrigationPlanStepFormData) => Promise<void>;
  onBulkRemoveSteps?: (stepIds: string[]) => Promise<void>;
}

/**
 * Custom hook for managing sector selection and step creation/removal logic.
 * @param props - Hook configuration
 * @returns Object containing sector management functions and state
 */
export const useSectorManagement = ({
  steps,
  sectors,
  planData,
  onRemoveStep,
  onUpdateStep,
  onSaveStep,
  onBulkRemoveSteps,
}: UseSectorManagementProps) => {
  /**
   * Get currently selected sector IDs from existing steps.
   */
  const selectedSectorIds = useMemo(
    () => steps.map((step) => step.sectorId),
    [steps]
  );

  /**
   * Handles changes to sector selection, adding/removing steps as needed.
   * Ensures proper step ordering after deletions and additions.
   * Operations are executed serially to prevent database deadlocks.
   * @param sectorIds - Array of selected sector IDs
   */
  const handleSectorsChange = useCallback(
    async (sectorIds: string[]) => {
      const currentSectorIds = new Set(selectedSectorIds);
      const newSectorIds = new Set(sectorIds);

      // First, collect all steps that need to be removed
      const stepsToRemove = steps.filter(
        (step) => !newSectorIds.has(step.sectorId)
      );

      // STEP 1: Remove steps for sectors that are no longer selected
      // Execute deletions serially to prevent database deadlocks
      if (stepsToRemove.length > 0) {
        if (onBulkRemoveSteps && stepsToRemove.length > 1) {
          await onBulkRemoveSteps(stepsToRemove.map((step) => step.id));
        } else {
          // Remove steps one by one serially
          for (const step of stepsToRemove) {
            await onRemoveStep(step.id);
          }
        }
      }

      // Calculate remaining steps after removal (for order calculation)
      const remainingSteps = steps.filter((step) =>
        newSectorIds.has(step.sectorId)
      );

      // After removals, the remaining steps will be reordered by the parent component
      // So we need to calculate the next order based on the remaining count
      let nextOrder = remainingSteps.length + 1;

      // STEP 2: Add steps for newly selected sectors
      // Execute additions serially to prevent database deadlocks
      for (const sectorId of sectorIds) {
        if (!currentSectorIds.has(sectorId)) {
          const sector = sectors.find((s) => s.id === sectorId);
          if (sector) {
            // Create a new step for this sector
            const newStep = createNewStep(sector, planData);
            newStep.order = nextOrder++;
            await onSaveStep(newStep);
          }
        }
      }

      // Note: The parent component (IrrigationPlanPage or IrrigationPlanWizardPage)
      // will handle reordering the remaining steps after deletions through their
      // handleRemoveStep implementation, which recalculates order numbers.
      // New steps are added with order numbers that continue from the end.
    },
    [
      steps,
      sectors,
      onRemoveStep,
      onUpdateStep,
      onSaveStep,
      planData,
      selectedSectorIds,
    ]
  );

  return {
    selectedSectorIds,
    handleSectorsChange,
  };
};
