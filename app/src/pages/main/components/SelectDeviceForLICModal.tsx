import { Modal, useToast } from "@/components";
import Button from "@/components/ui/Button";
import { ChevronDown, Plus } from "lucide-react";
import { useState } from "react";
import type { DeviceWithMapping } from "@/utils/mesh-device-utils";
import { getDeviceIcon } from "@/utils/mesh-device-utils";
import { getDeviceModelLabel } from "@/utils/device-model";
import clsx from "clsx";

interface SelectDeviceForLICModalProps {
  isOpen: boolean;
  onClose: () => void;
  licDevice: DeviceWithMapping | null;
  availableDevices: DeviceWithMapping[]; // All mesh devices (unmapped and mapped to other LICs)
  onSelectDevice?: (
    meshDeviceId: string,
    licDeviceId: string,
    startDate: string
  ) => void;
}

function SelectDeviceForLICModal({
  isOpen,
  onClose,
  licDevice,
  availableDevices,
  onSelectDevice,
}: SelectDeviceForLICModalProps) {
  const { showWarning } = useToast();
  const [selectedDeviceId, setSelectedDeviceId] = useState<string>("");
  const [startDate, setStartDate] = useState<string>(
    new Date().toISOString().split("T")[0]
  );
  const [isDeviceDropdownOpen, setIsDeviceDropdownOpen] = useState(false);

  if (!licDevice || !licDevice.isLIC) {
    return null;
  }

  const selectedDevice = availableDevices.find(
    (device) => device.id === selectedDeviceId
  );

  const handleSelectDevice = () => {
    if (!selectedDeviceId) {
      showWarning({
        message: "Por favor, selecione um dispositivo para adicionar à rede.",
      });
      return;
    }

    if (!startDate) {
      showWarning({
        message: "Por favor, selecione uma data de início.",
      });
      return;
    }

    if (onSelectDevice) {
      onSelectDevice(selectedDeviceId, licDevice.id, startDate);
      onClose();
    } else {
      showWarning({
        message: "Funcionalidade de adição ainda não implementada.",
      });
    }
  };

  const handleCancel = () => {
    setSelectedDeviceId("");
    setStartDate(new Date().toISOString().split("T")[0]);
    onClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Adicionar Dispositivo à Rede"
      size="md"
    >
      <div className="space-y-4">
        {/* LIC Device Info */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            <span className="text-xl">
              {getDeviceIcon(licDevice.device.model)}
            </span>
            <span className="font-medium text-gray-900">
              {licDevice.device.identifier}
            </span>
          </div>
          <p className="text-sm text-gray-600 mb-1">
            {getDeviceModelLabel(licDevice.device.model)}
          </p>
          <p className="text-sm text-green-600">
            Rede com {licDevice.meshDevices?.length || 0} dispositivo
            {(licDevice.meshDevices?.length || 0) !== 1 ? "s" : ""}
          </p>
        </div>

        {/* Device Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Selecionar Dispositivo
          </label>

          {availableDevices.length === 0 ? (
            <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg text-center text-gray-500">
              <p>Nenhum dispositivo disponível</p>
              <p className="text-sm">Não há dispositivos mesh para mapear</p>
            </div>
          ) : (
            <div className="relative">
              <Button
                type="button"
                variant="ghost"
                className="w-full flex items-center justify-start px-4 py-3 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                onClick={() => setIsDeviceDropdownOpen(!isDeviceDropdownOpen)}
                icon={
                  <ChevronDown
                    className={`text-gray-400 transition-transform ${
                      isDeviceDropdownOpen ? "rotate-180" : ""
                    }`}
                    size={20}
                  />
                }
              >
                <div className="flex items-center gap-2">
                  {selectedDevice ? (
                    <>
                      <span className="text-lg">
                        {getDeviceIcon(selectedDevice.device.model)}
                      </span>
                      <div className="flex flex-col">
                        <span className="font-medium text-left">
                          {selectedDevice.device.identifier}
                        </span>
                        <span className="text-xs text-gray-500">
                          {selectedDevice.mappingStatus === "unmapped"
                            ? "Não mapeado"
                            : selectedDevice.licDevice
                              ? `Mapeado para ${selectedDevice.licDevice.device.identifier}`
                              : "Status desconhecido"}
                        </span>
                      </div>
                    </>
                  ) : (
                    <span className="text-gray-500">
                      Selecione um dispositivo...
                    </span>
                  )}
                </div>
              </Button>

              {isDeviceDropdownOpen && (
                <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10 max-h-60 overflow-y-auto">
                  {availableDevices.map((device) => (
                    <button
                      type="button"
                      key={device.id}
                      className={`border-b border-gray-200 w-full flex items-center justify-between px-4 py-3 text-left hover:bg-gray-50 transition-colors first:rounded-t-lg last:rounded-b-lg ${
                        selectedDeviceId === device.id
                          ? "bg-green-50 text-green-700"
                          : "text-gray-900"
                      }`}
                      onClick={() => {
                        setSelectedDeviceId(device.id);
                        setIsDeviceDropdownOpen(false);
                      }}
                    >
                      <div className="flex items-center gap-2">
                        <span className="text-lg">
                          {getDeviceIcon(device.device.model)}
                        </span>
                        <div className="flex flex-col">
                          <span className="font-medium">
                            {device.device.identifier}
                          </span>
                          <span
                            className={clsx("text-xs", {
                              "text-red-500":
                                device.mappingStatus === "unmapped",
                              "text-green-500":
                                device.mappingStatus === "mapped",
                            })}
                          >
                            {device.mappingStatus === "unmapped"
                              ? "Não mapeado"
                              : device.licDevice
                                ? `Mapeado para ${device.licDevice.device.identifier}`
                                : "Status desconhecido"}
                          </span>
                        </div>
                      </div>
                      <span className="text-sm text-gray-500">
                        {device.device.model}
                      </span>
                    </button>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>

        {/* Start Date */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Data de Início
          </label>
          <input
            type="date"
            value={startDate}
            onChange={(e) => setStartDate(e.target.value)}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent outline-none"
          />
        </div>

        {/* Information */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-start gap-2">
            <Plus className="text-blue-600 mt-0.5" size={16} />
            <div className="text-sm text-blue-800">
              <p className="font-medium mb-1">Adicionando à rede mesh:</p>
              <p>
                O dispositivo selecionado será associado a este LIC e poderá
                receber comandos através dele. Se o dispositivo já estiver
                mapeado para outro LIC, ele será transferido para esta rede. A
                associação será ativa a partir da data especificada.
              </p>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3 pt-4 border-t border-gray-200">
          <Button variant="secondary" className="flex-1" onClick={handleCancel}>
            Cancelar
          </Button>
          <Button
            variant="primary"
            className="flex-1"
            onClick={handleSelectDevice}
            disabled={!selectedDeviceId || availableDevices.length === 0}
          >
            Adicionar
          </Button>
        </div>
      </div>
    </Modal>
  );
}

export default SelectDeviceForLICModal;
