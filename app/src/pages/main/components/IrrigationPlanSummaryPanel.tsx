import { Calendar, Clock, Play, RotateCcw } from "lucide-react";
import {
  IrrigationPlanFormData,
  IrrigationPlanStepFormData,
} from "../IrrigationPlanWizardPage";

interface IrrigationPlanSummaryPanelProps {
  planData: IrrigationPlanFormData;
  planSteps: IrrigationPlanStepFormData[];
}

const DAYS_OF_WEEK_LABELS = {
  MON: "Segunda",
  TUE: "Terça",
  WED: "Quarta",
  THU: "Quinta",
  FRI: "Sexta",
  SAT: "Sábado",
  SUN: "Domingo",
} as const;

function IrrigationPlanSummaryPanel({
  planData,
  planSteps,
}: IrrigationPlanSummaryPanelProps) {
  const formatDuration = (minutes: number) => {
    return `${minutes} min`;
  };

  const formatTime = (timeString: string) => {
    return timeString || "06:00";
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return "Não definido";
    return new Date(dateString).toLocaleDateString("pt-BR");
  };

  const getTotalDurationMinutes = () => {
    return planSteps.reduce((total, step) => total + step.durationMinutes, 0);
  };

  const getDaysOfWeekText = () => {
    if (planData.daysOfWeek.length === 0) return "Nenhum dia selecionado";
    if (planData.daysOfWeek.length === 7) return "Todos os dias";

    const sortedDays = ["MON", "TUE", "WED", "THU", "FRI", "SAT", "SUN"].filter(
      (day) => planData.daysOfWeek.includes(day as any)
    );

    return sortedDays
      .map(
        (day) => DAYS_OF_WEEK_LABELS[day as keyof typeof DAYS_OF_WEEK_LABELS]
      )
      .join(", ");
  };

  const getEstimatedFinishTime = () => {
    if (!planData.startTime) return "Não definido";

    const [hours, minutes] = planData.startTime.split(":").map(Number);
    const startTimeMinutes = hours * 60 + minutes;
    const totalDurationMinutes = Math.ceil(getTotalDurationMinutes() / 60);
    const finishTimeMinutes = startTimeMinutes + totalDurationMinutes;

    const finishHours = Math.floor(finishTimeMinutes / 60) % 24;
    const finishMinutes = finishTimeMinutes % 60;

    return `${finishHours.toString().padStart(2, "0")}:${finishMinutes
      .toString()
      .padStart(2, "0")}`;
  };

  return (
    <div className="p-4 space-y-6">
      {/* Plan Overview */}
      <div className="bg-white rounded-xl border border-gray-200 p-6">
        <div className="flex items-start justify-between mb-4">
          <div>
            <h2 className="text-xl font-bold text-gray-900 mb-1">
              {planData.name || "Nome não definido"}
            </h2>
            {planData.description && (
              <p className="text-gray-600">{planData.description}</p>
            )}
          </div>
          <div className="flex gap-2">
            <div
              className={`px-3 py-1 rounded-full text-sm font-medium ${
                planData.isEnabled
                  ? "bg-green-100 text-green-700"
                  : "bg-gray-100 text-gray-700"
              }`}
            >
              {planData.isEnabled ? "Habilitado" : "Desabilitado"}
            </div>
            {planData.fertigationEnabled && (
              <div className="px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-700">
                Fertirrigação
              </div>
            )}
            {planData.backwashEnabled && (
              <div className="px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-700">
                Retrolavagem
              </div>
            )}
          </div>
        </div>

        {/* Key Stats */}
        <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
          <div className="bg-blue-50 rounded-lg p-3 text-center">
            <Clock className="h-6 w-6 text-blue-600 mx-auto mb-1" />
            <div className="text-lg font-bold text-gray-900">
              {formatDuration(getTotalDurationMinutes())}
            </div>
            <div className="text-xs text-gray-600">Duração Total</div>
          </div>

          <div className="bg-green-50 rounded-lg p-3 text-center">
            <Play className="h-6 w-6 text-green-600 mx-auto mb-1" />
            <div className="text-lg font-bold text-gray-900">
              {planSteps.length}
            </div>
            <div className="text-xs text-gray-600">Setores</div>
          </div>

          <div className="bg-orange-50 rounded-lg p-3 text-center">
            <RotateCcw className="h-6 w-6 text-orange-600 mx-auto mb-1" />
            <div className="text-lg font-bold text-gray-900">
              {planData.daysOfWeek.length}
            </div>
            <div className="text-xs text-gray-600">Dias por Semana</div>
          </div>

          <div className="bg-purple-50 rounded-lg p-3 text-center">
            <Calendar className="h-6 w-6 text-purple-600 mx-auto mb-1" />
            <div className="text-lg font-bold text-gray-900">
              {formatTime(planData.startTime)}
            </div>
            <div className="text-xs text-gray-600">Horário de Início</div>
          </div>
        </div>
      </div>

      {/* Schedule Details */}
      <div className="bg-white rounded-xl border border-gray-200 p-6 space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">
          Detalhes do Agendamento
        </h3>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Horário de Início
            </label>
            <div className="text-gray-900 font-medium">
              {formatTime(planData.startTime)}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Horário Estimado de Término
            </label>
            <div className="text-gray-900 font-medium">
              {getEstimatedFinishTime()}
            </div>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Dias da Semana
          </label>
          <div className="text-gray-900 font-medium">{getDaysOfWeekText()}</div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Data de Início
            </label>
            <div className="text-gray-900 font-medium">
              {formatDate(planData.startDate)}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Data de Fim
            </label>
            <div className="text-gray-900 font-medium">
              {formatDate(planData.endDate)}
            </div>
          </div>
        </div>
      </div>

      {/* Steps Details */}
      <div className="bg-white rounded-xl border border-gray-200 p-1">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 px-5 pt-3">
          Sequência de Irrigação
        </h3>

        {planSteps.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            Nenhum setor configurado
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full border-collapse border border-gray-300 text-sm">
              <thead className="">
                <tr className="bg-gray-100">
                  <th className="border border-gray-300 px-4 py-3 text-left font-semibold text-gray-900">
                    Setor
                  </th>
                  <th className="border border-gray-300 px-4 py-3 text-left font-semibold text-gray-900">
                    Irrigação (min)
                  </th>
                  <th
                    className="border border-gray-300 px-4 py-3 text-center font-semibold text-gray-900"
                    colSpan={2}
                  >
                    Fertirrigação (min)
                  </th>
                  <th className="border border-gray-300 px-4 py-3 text-left font-semibold text-gray-900">
                    Lavagem (min)
                  </th>
                </tr>
                <tr className="bg-gray-50">
                  <th className="border border-gray-300 px-4 py-2"></th>
                  <th className="border border-gray-300 px-4 py-2"></th>
                  <th className="border border-gray-300 px-4 py-2 text-center font-medium text-gray-700">
                    Atraso
                  </th>
                  <th className="border border-gray-300 px-4 py-2 text-center font-medium text-gray-700">
                    Duração
                  </th>
                  <th className="border border-gray-300 px-4 py-2"></th>
                </tr>
              </thead>
              <tbody>
                {planSteps.map((step) => {
                  const hasFertigation =
                    typeof step.fertigationStartDelayMinutes === "number" &&
                    typeof step.fertigationDurationMinutes === "number";
                  const washTime = hasFertigation
                    ? step.durationMinutes -
                      Number(step.fertigationStartDelayMinutes) -
                      Number(step.fertigationDurationMinutes)
                    : null;
                  return (
                    <tr key={step.id} className="hover:bg-gray-50">
                      <td className="border border-gray-300 px-4 py-3 font-medium text-gray-900">
                        {step.order}
                      </td>
                      <td className="border border-gray-300 px-4 py-3 text-gray-900">
                        {step.durationMinutes}
                      </td>
                      <td className="border border-gray-300 px-4 py-3 text-center text-gray-900">
                        {step.fertigationStartDelayMinutes ?? "-"}
                      </td>
                      <td className="border border-gray-300 px-4 py-3 text-center text-gray-900">
                        {step.fertigationDurationMinutes ?? "-"}
                      </td>
                      <td className="border border-gray-300 px-4 py-3 text-gray-900">
                        {hasFertigation ? washTime : "-"}
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>

            {/* Total Summary */}
            <div className="border-t border-gray-200 pt-3 mt-4 px-5">
              <div className="flex items-center justify-start gap-4">
                <div className="font-semibold text-gray-900">Total Geral</div>
                <div className="text-lg font-bold text-gray-900">
                  {formatDuration(getTotalDurationMinutes())}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Validation Warnings */}
      {(planData.name.trim() === "" ||
        planData.daysOfWeek.length === 0 ||
        planSteps.length === 0) && (
        <div className="bg-red-50 border border-red-200 rounded-xl p-4">
          <h4 className="text-red-800 font-medium mb-2">
            Atenção! Configuração incompleta:
          </h4>
          <ul className="text-red-700 text-sm space-y-1">
            {planData.name.trim() === "" && (
              <li>• Nome do cronograma é obrigatório</li>
            )}
            {planData.daysOfWeek.length === 0 && (
              <li>• Selecione pelo menos um dia da semana</li>
            )}
            {planSteps.length === 0 && (
              <li>• Adicione pelo menos um setor ao cronograma</li>
            )}
          </ul>
        </div>
      )}

      {/* Success Message */}
      {planData.name.trim() !== "" &&
        planData.daysOfWeek.length > 0 &&
        planSteps.length > 0 && (
          <div className="bg-green-50 border border-green-200 rounded-xl p-4">
            <div className="flex items-center gap-2 text-green-800">
              <div className="w-5 h-5 bg-green-600 rounded-full flex items-center justify-center">
                <svg
                  className="w-3 h-3 text-white"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <span className="font-medium">
                Cronograma pronto para ser salvo!
              </span>
            </div>
          </div>
        )}
    </div>
  );
}

export default IrrigationPlanSummaryPanel;
