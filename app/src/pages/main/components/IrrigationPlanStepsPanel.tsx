import { AUTSector } from "@/api/queries/account";
import Button from "@/components/ui/Button";
import { Check, ChevronDown, ChevronUp, Plus, Trash2 } from "lucide-react";
import { useCallback, useState } from "react";
import ConfirmModal from "../../../components/ConfirmModal";
import InfoTooltip from "../../../components/InfoTooltip";
import {
  IrrigationPlanFormData,
  IrrigationPlanStepFormData,
} from "../IrrigationPlanWizardPage";

/**
 * Represents a sector that can be selected for irrigation plan steps.
 */
export type Sector = Pick<AUTSector, "id" | "name" | "description">;

/**
 * Validation result for fertigation timing constraints
 */
interface FertigationValidationResult {
  isValid: boolean;
  errorMessage?: string;
  maxAvailableMinutes?: number;
  totalFertigationTime?: number; // Optional field for detailed validation info
}

/**
 * Validates fertigation timing constraints according to irrigation documentation.
 *
 * Constraint: Start Delay + Duration + Pipe Wash Time ≤ Irrigation Duration
 *
 * This ensures that:
 * - The fertigation solution is properly washed away by irrigation water
 * - The next sector doesn't get contaminated by the previous sector's fertigation solution
 */
const validateFertigationTiming = (
  step: IrrigationPlanStepFormData,
  pipeWashTimeSeconds: number
): FertigationValidationResult => {
  // If no fertigation configured, validation passes
  if (!step.fertigationStartDelayMinutes && !step.fertigationDurationMinutes) {
    return { isValid: true };
  }

  // If only one fertigation field is filled, it's still valid (allows partial entry)
  if (!step.fertigationStartDelayMinutes || !step.fertigationDurationMinutes) {
    return { isValid: true };
  }

  const startDelaySeconds = step.fertigationStartDelayMinutes * 60;
  const durationSeconds = step.fertigationDurationMinutes * 60;
  const totalFertigationTime =
    startDelaySeconds + durationSeconds + pipeWashTimeSeconds;

  const stepDurationSeconds = step.durationMinutes * 60;
  if (totalFertigationTime > stepDurationSeconds) {
    const availableTimeSeconds = Math.max(
      0,
      stepDurationSeconds - pipeWashTimeSeconds
    );
    const maxAvailableMinutes = Math.floor(availableTimeSeconds / 60);

    return {
      isValid: false,
      errorMessage: `Tempo total excede duração da irrigação. Máximo disponível: ${maxAvailableMinutes} min`,
      maxAvailableMinutes,
      totalFertigationTime,
    };
  }

  return { isValid: true };
};

/**
 * Props for the IrrigationPlanStepsPanel component.
 *
 * This component manages individual irrigation plan steps, allowing users to:
 * - Add new steps with sector selection
 * - Remove existing steps with confirmation
 * - Update step configurations (duration, fertigation timing, sector)
 * - Reorder steps using up/down controls
 * - Save changes to persist data
 *
 * The component follows a controlled pattern where all state changes are managed
 * by the parent component through callback functions.
 */
interface IrrigationPlanStepsPanelProps {
  /** ID of the project containing the sectors */
  projectId: string;

  /** Irrigation plan data containing fertigation settings */
  planData: IrrigationPlanFormData;

  /** Array of irrigation plan steps to display and manage */
  steps: IrrigationPlanStepFormData[];

  /** Available sectors that can be assigned to steps */
  sectors: Sector[];

  /** Project data containing pipe wash time and other settings */
  projectData?: {
    pipe_wash_time_seconds: number | null;
    // Add other relevant project fields as needed
  };

  /**
   * Callback triggered when user wants to add a new step.
   * Parent should create a new step with temporary ID and default values.
   */
  onAddStep: () => void;

  /**
   * Callback triggered when user confirms removal of a step.
   * Parent should differentiate between new steps (temp IDs) and existing steps (real IDs).
   * For existing steps, should make API call to delete from database.
   *
   * @param stepId - ID of the step to remove (can be temporary or real ID)
   */
  onRemoveStep: (stepId: string) => void;

  /**
   * Callback triggered when user clicks save/confirm button for a step.
   * Parent should determine if this is a CREATE or UPDATE operation based on step ID:
   * - If stepId.startsWith('step-'): CREATE new step in database
   * - Otherwise: UPDATE existing step in database
   *
   * @param step - Complete step data to save
   */
  onSaveStep: (step: IrrigationPlanStepFormData) => void;

  /**
   * Callback triggered for real-time local updates as user modifies step fields.
   * Should update local state immediately for responsive UI.
   * Changes are NOT persisted until onSaveStep is called.
   *
   * @param stepId - ID of the step to update
   * @param updates - Partial step data with fields to update
   */
  onUpdateStep: (
    stepId: string,
    updates: Partial<IrrigationPlanStepFormData>
  ) => void;

  /**
   * Callback triggered when user reorders steps using up/down arrows.
   * Parent should swap steps and update order numbers for all affected steps.
   *
   * @param stepId - ID of the step to move
   * @param direction - Direction to move the step ("up" or "down")
   */
  onMoveStep: (stepId: string, direction: "up" | "down") => void;

  /** Whether the component should be in read-only mode (disables all editing) */
  readOnly?: boolean;

  /** Whether to show loading state */
  loading?: boolean;
}

/**
 * IrrigationPlanStepsPanel - A controlled component for managing irrigation plan steps
 *
 * This component provides a user interface for configuring irrigation plan steps,
 * including sector selection, duration settings, delays, and step ordering.
 *
 * ## Key Features:
 * - **Add Steps**: Users can add new irrigation steps for available sectors
 * - **Remove Steps**: Steps can be removed with confirmation modal
 * - **Edit Steps**: Real-time editing of duration, fertigation timing, and sector assignment
 * - **Reorder Steps**: Drag-free reordering using up/down arrow controls
 * - **Save Changes**: Individual step saving with visual feedback for unsaved changes
 * - **Sector Management**: Prevents duplicate sector assignments within a plan
 * - **Conditional Fertigation**: Fertigation fields only appear when fertigation is enabled in the plan
 *
 * ## Data Flow:
 * 1. User modifies fields → `onUpdateStep` → Local state update (immediate UI)
 * 2. User clicks save → `onSaveStep` → API persistence (CREATE/UPDATE based on ID)
 * 3. User clicks remove → `onRemoveStep` → API deletion + local state update
 * 4. User reorders → `onMoveStep` → Local state reordering
 * 5. User adds step → `onAddStep` → New step with temporary ID
 *
 * ## CREATE vs UPDATE Logic:
 * The component differentiates between new and existing steps using ID patterns:
 * - New steps: IDs starting with 'step-' (temporary IDs)
 * - Existing steps: Real database IDs
 *
 * @param props - Component props defining data and callback functions
 * @returns JSX element representing the irrigation plan steps interface
 */
function IrrigationPlanStepsPanel({
  projectId,
  planData,
  steps,
  sectors,
  onAddStep,
  onRemoveStep,
  onSaveStep,
  onUpdateStep,
  onMoveStep,
  readOnly = false,
  loading = false,
  projectData,
}: IrrigationPlanStepsPanelProps) {
  const [confirmModal, setConfirmModal] = useState<{
    isOpen: boolean;
    stepId: string;
    sectorName: string;
  }>({
    isOpen: false,
    stepId: "",
    sectorName: "",
  });

  const [modifiedSteps, setModifiedSteps] = useState<Set<string>>(new Set());

  // Get pipe wash time from project data, default to 0 if not available
  const pipeWashTimeSeconds = projectData?.pipe_wash_time_seconds || 0;

  /**
   * Validates fertigation timing for a specific step
   */
  const getStepValidation = useCallback(
    (step: IrrigationPlanStepFormData): FertigationValidationResult => {
      return validateFertigationTiming(step, pipeWashTimeSeconds);
    },
    [pipeWashTimeSeconds]
  );

  /**
   * Handles step addition by delegating to parent callback.
   * Parent is responsible for creating step with temporary ID and default values.
   */
  const handleAddStep = useCallback(() => {
    onAddStep();
  }, [onAddStep]);

  /**
   * Initiates step removal by showing confirmation modal.
   * Actual removal is handled by handleConfirmRemove after user confirmation.
   */
  const handleRemoveStep = useCallback(
    (stepId: string) => {
      const step = steps.find((s) => s.id === stepId);
      const sectorName = step ? getSectorName(step.sectorId) : "este setor";

      setConfirmModal({
        isOpen: true,
        stepId,
        sectorName,
      });
    },
    [steps]
  );

  /**
   * Confirms step removal and delegates to parent callback.
   * Parent handles API deletion and local state updates.
   */
  const handleConfirmRemove = useCallback(() => {
    onRemoveStep(confirmModal.stepId);
    setConfirmModal({ isOpen: false, stepId: "", sectorName: "" });
  }, [confirmModal.stepId, onRemoveStep]);

  /**
   * Cancels step removal by hiding the confirmation modal.
   */
  const handleCancelRemove = useCallback(() => {
    setConfirmModal({ isOpen: false, stepId: "", sectorName: "" });
  }, []);

  /**
   * Handles real-time step updates for immediate UI feedback.
   * Marks step as modified and delegates to parent for local state update.
   * Changes are not persisted until handleSaveStep is called.
   */
  const handleUpdateStep = useCallback(
    (stepId: string, updates: Partial<IrrigationPlanStepFormData>) => {
      onUpdateStep(stepId, updates);
      setModifiedSteps((prev) => new Set(prev).add(stepId));
    },
    [onUpdateStep]
  );

  /**
   * Saves step changes and removes from modified set.
   * Parent determines if this is CREATE or UPDATE based on step ID pattern.
   */
  const handleSaveStep = useCallback(
    (step: IrrigationPlanStepFormData) => {
      onSaveStep(step);
      setModifiedSteps((prev) => {
        const newSet = new Set(prev);
        newSet.delete(step.id);
        return newSet;
      });
    },
    [onSaveStep]
  );

  /**
   * Handles step reordering by delegating to parent callback.
   * Parent is responsible for swapping steps and updating order numbers.
   */
  const handleMoveStep = useCallback(
    (stepId: string, direction: "up" | "down") => {
      onMoveStep(stepId, direction);
    },
    [onMoveStep]
  );

  /**
   * Formats duration in seconds to human-readable string.
   * @param seconds - Duration in seconds
   * @returns Formatted string (e.g., "20 min" or "5m 30s")
   */
  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    if (remainingSeconds === 0) {
      return `${minutes} min`;
    }
    return `${minutes}m ${remainingSeconds}s`;
  };

  /** Converts minutes to seconds */
  const getDurationFromMinutes = (minutes: number) => minutes * 60;

  /** Converts seconds to minutes (rounded down) */
  const getMinutesFromDuration = (seconds: number) => Math.floor(seconds / 60);

  /**
   * Gets sector name by ID, with fallback for unknown sectors.
   * @param sectorId - ID of the sector to look up
   * @returns Sector name or fallback message
   */
  const getSectorName = (sectorId: string) => {
    const sector = sectors.find((s) => s.id === sectorId);
    return sector ? sector.name : "Setor desconhecido";
  };

  /**
   * Gets available sectors for a step's dropdown.
   * Includes the current sector plus any unassigned sectors.
   * @param currentSectorId - ID of the currently selected sector
   * @returns Array of sectors available for selection
   */
  const getAvailableSectors = (currentSectorId: string) => {
    return sectors.filter(
      (sector) =>
        sector.id === currentSectorId ||
        !steps.some((step) => step.sectorId === sector.id)
    );
  };

  /**
   * Calculates total duration of all steps.
   * @returns Total duration in seconds
   */
  const getTotalDuration = () => {
    return steps.reduce((total, step) => total + step.durationMinutes * 60, 0);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
      </div>
    );
  }

  return (
    <div className="p-4 space-y-6">
      {/* Summary */}
      <div className="bg-white rounded-xl border border-gray-200 p-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Resumo</h3>
          <div className="text-right">
            <div className="text-2xl font-bold text-gray-900">
              {formatDuration(getTotalDuration())}
            </div>
            <div className="text-sm text-gray-600">Duração total</div>
          </div>
        </div>
        <div className="grid grid-cols-2 gap-4 text-center">
          <div className="bg-gray-50 rounded-lg p-3">
            <div className="text-lg font-bold text-gray-900">
              {steps.length}
            </div>
            <div className="text-xs text-gray-600">Setores</div>
          </div>
          <div className="bg-gray-50 rounded-lg p-3">
            <div className="text-lg font-bold text-gray-900">
              {sectors.length - steps.length}
            </div>
            <div className="text-xs text-gray-600">Disponíveis</div>
          </div>
        </div>
      </div>

      {/* Steps List */}
      <div className="space-y-3">
        {steps.map((step, index) => {
          const availableSectors = getAvailableSectors(step.sectorId);

          return (
            <div
              key={step.id}
              className="bg-white rounded-xl border border-gray-200 p-4"
            >
              <div className="flex items-start gap-3">
                {/* Order Controls */}
                <div className="flex flex-col gap-1 pt-1">
                  <Button
                    onClick={() => !readOnly && handleMoveStep(step.id, "up")}
                    disabled={readOnly || index === 0}
                    variant="ghost"
                    size="sm"
                    className={`p-1 rounded ${
                      readOnly || index === 0
                        ? "text-gray-300 cursor-not-allowed"
                        : "text-gray-600 hover:bg-gray-100"
                    }`}
                    icon={<ChevronUp className="h-4 w-4" />}
                  />
                  <div className="text-center text-sm font-medium text-gray-900 px-1">
                    {step.order}
                  </div>
                  <Button
                    onClick={() => !readOnly && handleMoveStep(step.id, "down")}
                    disabled={readOnly || index === steps.length - 1}
                    variant="ghost"
                    size="sm"
                    className={`p-1 rounded ${
                      readOnly || index === steps.length - 1
                        ? "text-gray-300 cursor-not-allowed"
                        : "text-gray-600 hover:bg-gray-100"
                    }`}
                    icon={<ChevronDown className="h-4 w-4" />}
                  />
                </div>

                {/* Step Content */}
                <div className="flex-1 space-y-3">
                  {/* Sector Selection */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Setor
                    </label>
                    <select
                      value={step.sectorId || ""}
                      onChange={(e) => {
                        if (readOnly) return;
                        const selectedSector = sectors.find(
                          (s) => s.id === e.target.value
                        );
                        handleUpdateStep(step.id, {
                          sectorId: e.target.value,
                          sectorName: selectedSector?.name || "",
                        });
                      }}
                      disabled={readOnly}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50 disabled:text-gray-500"
                    >
                      {!step.sectorId && (
                        <option value="" disabled>
                          Selecione um setor
                        </option>
                      )}
                      {availableSectors.map((sector) => (
                        <option key={sector.id} value={sector.id}>
                          {sector.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Duration */}
                  <div>
                    <div className="flex items-center justify-between mb-1">
                      <label className="block text-sm font-medium text-gray-700">
                        Duração (minutos)
                      </label>
                      <InfoTooltip content="Tempo que o setor ficará irrigando." />
                    </div>
                    <input
                      type="number"
                      min="1"
                      max="180"
                      value={
                        step.durationMinutes === 0 ? "" : step.durationMinutes
                      }
                      onChange={(e) => {
                        if (readOnly) return;
                        const value = e.target.value;
                        if (value === "") {
                          // Allow empty field during editing
                          handleUpdateStep(step.id, {
                            durationMinutes: 0, // Use 0 as temporary value for empty field
                          });
                        } else {
                          const minutes = parseInt(value);
                          if (!isNaN(minutes) && minutes >= 1) {
                            handleUpdateStep(step.id, {
                              durationMinutes: minutes,
                            });
                          }
                        }
                      }}
                      onBlur={(e) => {
                        if (readOnly) return;
                        // Ensure minimum value of 1 when user leaves the field
                        const value = parseInt(e.target.value);
                        if (isNaN(value) || value < 1) {
                          handleUpdateStep(step.id, {
                            durationMinutes: 1,
                          });
                        }
                      }}
                      disabled={readOnly}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50 disabled:text-gray-500"
                    />
                  </div>

                  {/* Fertigation Fields - Only show if fertigation is enabled */}
                  {planData.fertigationEnabled &&
                    (() => {
                      const validation = getStepValidation(step);
                      const hasValidationError = !validation.isValid;

                      return (
                        <>
                          {/* Fertigation Start Delay */}
                          <div>
                            <div className="flex items-center justify-between mb-1">
                              <label className="block text-sm font-medium text-gray-700">
                                Atraso da fertirrigação (minutos)
                              </label>
                              <InfoTooltip content="Tempo para aguardar após iniciar irrigação antes de começar fertirrigação" />
                            </div>
                            <input
                              type="number"
                              min="0"
                              value={step.fertigationStartDelayMinutes || ""}
                              onChange={(e) => {
                                if (readOnly) return;
                                const delay =
                                  parseInt(e.target.value) || undefined;
                                handleUpdateStep(step.id, {
                                  fertigationStartDelayMinutes: delay,
                                });
                              }}
                              disabled={readOnly}
                              placeholder="Opcional"
                              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:border-blue-500 disabled:bg-gray-50 disabled:text-gray-500 ${
                                hasValidationError
                                  ? "border-red-300 focus:ring-red-500 focus:border-red-500"
                                  : "border-gray-300 focus:ring-blue-500"
                              }`}
                            />
                          </div>

                          {/* Fertigation Duration */}
                          <div>
                            <div className="flex items-center justify-between mb-1">
                              <label className="block text-sm font-medium text-gray-700">
                                Duração da fertirrigação (minutos)
                              </label>
                              <InfoTooltip content="Duração da aplicação de fertilizante durante a irrigação" />
                            </div>
                            <input
                              type="number"
                              min="0"
                              value={step.fertigationDurationMinutes || ""}
                              onChange={(e) => {
                                if (readOnly) return;
                                const duration =
                                  parseInt(e.target.value) || undefined;
                                handleUpdateStep(step.id, {
                                  fertigationDurationMinutes: duration,
                                });
                              }}
                              disabled={readOnly}
                              placeholder="Opcional"
                              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:border-blue-500 disabled:bg-gray-50 disabled:text-gray-500 ${
                                hasValidationError
                                  ? "border-red-300 focus:ring-red-500 focus:border-red-500"
                                  : "border-gray-300 focus:ring-blue-500"
                              }`}
                            />
                          </div>

                          {/* Validation Error Message */}
                          {hasValidationError && validation.errorMessage && (
                            <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                              <div className="flex items-start gap-2">
                                <div className="flex-shrink-0 w-5 h-5 bg-red-600 rounded-full flex items-center justify-center mt-0.5">
                                  <svg
                                    className="w-3 h-3 text-white"
                                    fill="currentColor"
                                    viewBox="0 0 20 20"
                                  >
                                    <path
                                      fillRule="evenodd"
                                      d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                                      clipRule="evenodd"
                                    />
                                  </svg>
                                </div>
                                <div className="flex-1">
                                  <div className="flex items-center gap-2">
                                    <p className="text-sm font-medium text-red-800">
                                      Configuração de fertirrigação inválida
                                    </p>
                                    <InfoTooltip
                                      content={`Duração total da fertirrigação com lavagem excede duração da irrigação em ${getMinutesFromDuration(
                                        (validation.totalFertigationTime ?? 0) -
                                          step.durationMinutes * 60
                                      )} min. A fertirrigação deve ser concluída ${getMinutesFromDuration(
                                        pipeWashTimeSeconds
                                      )} min antes do fim da irrigação.`}
                                    />
                                  </div>
                                </div>
                              </div>
                            </div>
                          )}
                        </>
                      );
                    })()}

                  {/* Fertigation disabled note */}
                  {/* {!planData.fertigationEnabled && (
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                      <p className="text-sm text-blue-700">
                        💡 Para configurar fertirrigação, ative a opção
                        "Fertirrigação" na configuração do plano.
                      </p>
                    </div>
                  )} */}
                </div>

                {/* Action Buttons */}
                {!readOnly && (
                  <div className="flex flex-col gap-2 ml-3">
                    <Button
                      onClick={() => handleSaveStep(step)}
                      disabled={!modifiedSteps.has(step.id)}
                      variant="ghost"
                      size="sm"
                      className={`p-2 rounded-lg transition-colors ${
                        modifiedSteps.has(step.id)
                          ? "text-green-600 hover:bg-green-50"
                          : "text-gray-400 cursor-not-allowed"
                      }`}
                      title="Salvar alterações"
                      icon={<Check className="h-4 w-4" />}
                    />
                    <Button
                      onClick={() => handleRemoveStep(step.id)}
                      variant="destructive"
                      size="sm"
                      className="p-2 hover:bg-red-50 rounded-lg transition-colors"
                      title="Remover setor"
                      icon={<Trash2 className="h-4 w-4" />}
                    />
                  </div>
                )}
              </div>
            </div>
          );
        })}

        {/* Add Step Button */}
        {!readOnly && steps.length < sectors.length && (
          <Button
            onClick={handleAddStep}
            className="w-full py-4 border-2 border-dashed border-gray-300 rounded-xl text-gray-600 hover:border-gray-400 hover:text-gray-700 transition-colors flex items-center justify-center gap-2"
            icon={<Plus className="h-5 w-5" />}
            size="md"
            variant="secondary"
            fullWidth
          >
            Adicionar Setor
          </Button>
        )}

        {!readOnly && steps.length === 0 && (
          <div className="text-center py-8">
            <div className="text-gray-500 mb-4">
              Nenhum setor adicionado ainda
            </div>
            <Button
              onClick={handleAddStep}
              className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2 mx-auto"
              icon={<Plus className="h-5 w-5" />}
              size="md"
              variant="primary"
            >
              Adicionar Primeiro Setor
            </Button>
          </div>
        )}
      </div>

      {/* Confirm Modal */}
      <ConfirmModal
        isOpen={confirmModal.isOpen}
        onClose={handleCancelRemove}
        onConfirm={handleConfirmRemove}
        title="Remover Setor"
        message={`Tem certeza que deseja remover ${confirmModal.sectorName} do plano de irrigação?`}
        confirmText="Remover"
        cancelText="Cancelar"
        variant="danger"
      />
    </div>
  );
}

export default IrrigationPlanStepsPanel;
