import { Modal, useToast } from "@/components";
import Button from "@/components/ui/Button";
import { AlertTriangle, ChevronDown } from "lucide-react";
import { useState } from "react";
import { useSetAtom } from "jotai";
import {
  createMeshDeviceMappingAtom,
  updateMeshDeviceMappingAtom,
} from "@/store";
import type { DeviceWithMapping } from "@/utils/mesh-device-utils";
import { getDeviceIcon } from "@/utils/mesh-device-utils";
import { getDeviceModelLabel } from "@/utils/device-model";

interface AssignToLICModalProps {
  isOpen: boolean;
  onClose: () => void;
  meshDevice: DeviceWithMapping | null;
  availableLICs: DeviceWithMapping[];
  onAssign?: (
    meshDeviceId: string,
    licDeviceId: string,
    startDate: string
  ) => void;
}

function AssignToLICModal({
  isOpen,
  onClose,
  meshDevice,
  availableLICs,
  onAssign,
}: AssignToLICModalProps) {
  const { showWarning, showSuccess } = useToast();
  const createMeshDeviceMapping = useSetAtom(createMeshDeviceMappingAtom);
  const updateMeshDeviceMapping = useSetAtom(updateMeshDeviceMappingAtom);
  const [selectedLICId, setSelectedLICId] = useState<string>("");
  const [startDate, setStartDate] = useState<string>(
    new Date().toISOString().split("T")[0]
  );
  const [isLICDropdownOpen, setIsLICDropdownOpen] = useState(false);

  if (!meshDevice || !meshDevice.isMesh) {
    return null;
  }

  const deviceIcon = getDeviceIcon(meshDevice.device.model);
  const modelLabel = getDeviceModelLabel(meshDevice.device.model);
  const selectedLIC = availableLICs.find((lic) => lic.id === selectedLICId);

  const handleAssign = async () => {
    if (!selectedLICId) {
      showWarning({
        message: "Por favor, selecione um LIC para mapear o dispositivo.",
      });
      return;
    }

    if (!startDate) {
      showWarning({
        message: "Por favor, selecione uma data de início.",
      });
      return;
    }

    try {
      // If device is already mapped, end the current mapping first
      if (meshDevice.current_mesh_device_mapping) {
        await updateMeshDeviceMapping({
          id: meshDevice.current_mesh_device_mapping.id,
          data: {
            end_date: startDate, // End current mapping on the start date of new mapping
          },
        });
      }

      // Create the new mesh device mapping
      await createMeshDeviceMapping({
        mesh_property_device: meshDevice.id,
        lic_property_device: selectedLICId,
        start_date: startDate,
      });

      showSuccess({
        message: meshDevice.current_mesh_device_mapping
          ? "Mapeamento alterado com sucesso!"
          : "Dispositivo mapeado com sucesso!",
      });

      // Call the callback if provided
      if (onAssign) {
        onAssign(meshDevice.id, selectedLICId, startDate);
      }

      onClose();
    } catch (error) {
      console.error("Error creating/updating mesh device mapping:", error);
      showWarning({
        message: "Erro ao mapear dispositivo. Tente novamente.",
      });
    }
  };

  const handleCancel = () => {
    setSelectedLICId("");
    setStartDate(new Date().toISOString().split("T")[0]);
    onClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={
        meshDevice?.current_mesh_device_mapping
          ? "Alterar Mapeamento"
          : "Mapear Dispositivo"
      }
      size="md"
    >
      <div className="space-y-6">
        {/* Device Info */}
        <div
          className={`border rounded-lg p-4 ${
            meshDevice.current_mesh_device_mapping
              ? "bg-blue-50 border-blue-200"
              : "bg-yellow-50 border-yellow-200"
          }`}
        >
          <div className="flex items-center gap-2 mb-2">
            <span className="text-xl">{deviceIcon}</span>
            <span className="font-medium text-gray-900">
              {meshDevice.device.identifier}
            </span>
          </div>
          <p className="text-sm text-gray-600 mb-1">{modelLabel}</p>
          <div
            className={`flex items-center gap-1 text-sm ${
              meshDevice.current_mesh_device_mapping
                ? "text-blue-600"
                : "text-red-600"
            }`}
          >
            <AlertTriangle size={14} />
            <span>
              {meshDevice.current_mesh_device_mapping && meshDevice.licDevice
                ? `Atualmente mapeado para ${meshDevice.licDevice.device.identifier}`
                : "Não mapeado para nenhum LIC"}
            </span>
          </div>
        </div>

        {/* LIC Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Selecionar LIC
          </label>

          {availableLICs.length === 0 ? (
            <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg text-center text-gray-500">
              <p>Nenhum LIC disponível</p>
              <p className="text-sm">
                Adicione um LIC primeiro para mapear dispositivos
              </p>
            </div>
          ) : (
            <div className="relative">
              <Button
                type="button"
                variant="ghost"
                className="w-full flex items-center justify-between px-4 py-3 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                onClick={() => setIsLICDropdownOpen(!isLICDropdownOpen)}
              >
                <div className="flex items-center gap-2">
                  {selectedLIC ? (
                    <>
                      <span className="text-lg">📡</span>
                      <span className="font-medium">
                        {selectedLIC.device.identifier}
                      </span>
                      <span className="text-sm text-gray-500">
                        ({selectedLIC.meshDevices?.length || 0} dispositivos)
                      </span>
                    </>
                  ) : (
                    <span className="text-gray-500">Selecione um LIC...</span>
                  )}
                </div>
                <ChevronDown
                  className={`text-gray-400 transition-transform ${
                    isLICDropdownOpen ? "rotate-180" : ""
                  }`}
                  size={20}
                />
              </Button>

              {isLICDropdownOpen && (
                <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10 max-h-60 overflow-y-auto">
                  {availableLICs.map((lic) => (
                    <Button
                      type="button"
                      key={lic.id}
                      variant={selectedLICId === lic.id ? "secondary" : "ghost"}
                      className={`w-full flex items-center justify-between px-4 py-3 text-left hover:bg-gray-50 transition-colors first:rounded-t-lg last:rounded-b-lg ${
                        selectedLICId === lic.id
                          ? "bg-green-50 text-green-700"
                          : "text-gray-900"
                      }`}
                      onClick={() => {
                        setSelectedLICId(lic.id);
                        setIsLICDropdownOpen(false);
                      }}
                    >
                      <div className="flex items-center gap-2">
                        <span className="text-lg">📡</span>
                        <span className="font-medium">
                          {lic.device.identifier}
                        </span>
                      </div>
                      <span className="text-sm text-gray-500">
                        {lic.meshDevices?.length || 0} dispositivos
                      </span>
                    </Button>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>

        {/* Start Date */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Data de Início
          </label>
          <input
            type="date"
            value={startDate}
            onChange={(e) => setStartDate(e.target.value)}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent outline-none"
          />
        </div>

        {/* Warning */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-start gap-2">
            <AlertTriangle className="text-blue-600 mt-0.5" size={16} />
            <div className="text-sm text-blue-800">
              <p className="font-medium mb-1">Informação importante:</p>
              <p>
                {meshDevice.current_mesh_device_mapping
                  ? "O mapeamento atual será encerrado e este dispositivo será transferido para o LIC selecionado."
                  : "Este dispositivo será associado ao LIC selecionado e poderá receber comandos através dele."}{" "}
                A nova associação será ativa a partir da data especificada.
              </p>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3 pt-4 border-t border-gray-200">
          <Button variant="secondary" className="flex-1" onClick={handleCancel}>
            Cancelar
          </Button>
          <Button
            variant="primary"
            className="flex-1"
            onClick={handleAssign}
            disabled={!selectedLICId || availableLICs.length === 0}
          >
            {meshDevice?.current_mesh_device_mapping ? "Alterar" : "Mapear"}
          </Button>
        </div>
      </div>
    </Modal>
  );
}

export default AssignToLICModal;
