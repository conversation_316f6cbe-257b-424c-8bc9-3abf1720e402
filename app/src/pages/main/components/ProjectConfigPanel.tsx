import { DeviceModel } from "@/api/model/device";
import { AUTProperty } from "@/api/queries/account";
import { ChevronDown } from "lucide-react";
import Button from "@/components/ui/Button";

interface Device {
  id: string;
  name: string;
  model: DeviceModel;
  identifier: string;
}

interface WaterPump {
  id: string;
  label: string;
  identifier: string;
  pumpType: string;
  pumpModel: string;
}

interface ProjectConfig {
  id: string;
  name: string;
  description: string;
  startDate: string;
  endDate: string;
  irrigationControllerId: string;
  waterPumpId: string;
  fertigationPumpId: string;
  pipeWashTimeMinutes: number | null; // in minutes
  backwashDurationMinutes: number | null; // in minutes
  backwashPeriodMinutes: number | null; // in minutes
}

interface ProjectConfigPanelProps {
  formData: ProjectConfig;
  property: AUTProperty | null;
  devices: Device[];
  waterPumps: WaterPump[];
  fertigationPumps: WaterPump[];
  saving: boolean;
  onInputChange: (field: keyof ProjectConfig, value: string) => void;
  onSave: () => void;
  onCancel: () => void;
}

function ProjectConfigPanel({
  formData,
  property: _property, // Unused since backwash fields are hidden
  devices,
  waterPumps,
  fertigationPumps,
  saving,
  onInputChange,
  onSave,
  onCancel,
}: ProjectConfigPanelProps) {
  // Validation for required fields
  const isValid = () => {
    return (
      formData.name.trim() !== "" &&
      formData.irrigationControllerId !== "" &&
      formData.waterPumpId !== ""
    );
  };

  return (
    <div className="p-6 bg-white">
      {/* Project Name */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-neutral-700 mb-2">
          Nome <span className="text-red-500">*</span>
        </label>
        <input
          type="text"
          value={formData.name}
          onChange={(e) => onInputChange("name", e.target.value)}
          className="w-full px-4 py-3 border border-neutral-200 rounded-xl text-base focus:ring-2 focus:ring-green-500/20 focus:border-green-500 transition-all duration-150 ease-in-out min-h-12"
          placeholder="Nome do projeto"
          required
        />
      </div>

      {/* Project Description */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-neutral-700 mb-2">
          Descrição
        </label>
        <textarea
          value={formData.description}
          onChange={(e) => onInputChange("description", e.target.value)}
          className="w-full px-4 py-3 border border-neutral-200 rounded-xl text-base focus:ring-2 focus:ring-green-500/20 focus:border-green-500 transition-all duration-150 ease-in-out resize-vertical"
          placeholder="Descrição do projeto (opcional)"
          rows={3}
        />
      </div>

      {/* Irrigation Controller */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-neutral-700 mb-2">
          Controlador de Irrigação <span className="text-red-500">*</span>
        </label>
        <div className="relative">
          <select
            value={formData.irrigationControllerId}
            onChange={(e) =>
              onInputChange("irrigationControllerId", e.target.value)
            }
            className="w-full px-4 py-3 border border-neutral-200 rounded-xl text-base focus:ring-2 focus:ring-green-500/20 focus:border-green-500 transition-all duration-150 ease-in-out appearance-none bg-white min-h-12"
            required
          >
            <option value="">Selecione um controlador</option>
            {devices
              .filter((d) => d.model === "LIC")
              .map((device) => (
                <option key={device.id} value={device.id}>
                  {device.name} ({device.identifier})
                </option>
              ))}
          </select>
          <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-neutral-400 pointer-events-none" />
        </div>
      </div>

      {/* Water Pump */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-neutral-700 mb-2">
          Bomba de Irrigação <span className="text-red-500">*</span>
        </label>
        <div className="relative">
          <select
            value={formData.waterPumpId}
            onChange={(e) => onInputChange("waterPumpId", e.target.value)}
            className="w-full px-4 py-3 border border-neutral-200 rounded-xl text-base focus:ring-2 focus:ring-green-500/20 focus:border-green-500 transition-all duration-150 ease-in-out appearance-none bg-white min-h-12"
            required
          >
            <option value="">Selecione uma bomba</option>
            {waterPumps.map((pump) => (
              <option key={pump.id} value={pump.id}>
                {pump.label} ({pump.identifier})
              </option>
            ))}
          </select>
          <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-neutral-400 pointer-events-none" />
        </div>
      </div>

      {/* Fertigation Pump */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-neutral-700 mb-2">
          Bomba de Fertirrigação
        </label>
        <div className="relative">
          <select
            value={formData.fertigationPumpId}
            onChange={(e) => onInputChange("fertigationPumpId", e.target.value)}
            className="w-full px-4 py-3 border border-neutral-200 rounded-xl text-base focus:ring-2 focus:ring-green-500/20 focus:border-green-500 transition-all duration-150 ease-in-out appearance-none bg-white min-h-12"
          >
            <option value="">Nenhuma</option>
            {fertigationPumps.map((pump) => (
              <option key={pump.id} value={pump.id}>
                {pump.label} ({pump.identifier})
              </option>
            ))}
          </select>
          <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-neutral-400 pointer-events-none" />
        </div>
      </div>

      {/* Irrigation System Configuration */}
      <div className="mb-6">
        <h3 className="text-lg font-medium text-neutral-900 mb-4">
          Configuração do Sistema de Irrigação
        </h3>

        <div className="grid grid-cols-1 gap-4">
          {/* Pipe Wash Time */}
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-2">
              Tempo de Lavagem dos Tubos (minutos)
            </label>
            <input
              type="number"
              value={formData.pipeWashTimeMinutes ?? undefined}
              onChange={(e) =>
                onInputChange("pipeWashTimeMinutes", e.target.value)
              }
              className="w-full px-4 py-3 border border-neutral-200 rounded-xl text-base focus:ring-2 focus:ring-green-500/20 focus:border-green-500 transition-all duration-150 ease-in-out min-h-12"
              placeholder="Ex: 5"
              min="0"
              max="60"
            />
            <p className="text-xs text-neutral-500 mt-1">
              Tempo para lavar tubos após fertirrigação (necessário se bomba de
              fertirrigação estiver configurada)
            </p>
          </div>

          {/* Backwash fields are hidden as per Task 5 requirements */}
        </div>
      </div>

      {/* Date Range fields are hidden as per Task 5 requirements */}

      {/* Action Buttons */}
      <div className="flex gap-3">
        <Button
          onClick={onSave}
          loading={saving}
          disabled={!isValid()}
          variant="primary"
          size="md"
          className="flex-1 rounded-xl"
        >
          Salvar
        </Button>
        <Button
          onClick={onCancel}
          variant="secondary"
          size="md"
          className="flex-1 rounded-xl"
        >
          Cancelar
        </Button>
      </div>

      {/* Required fields note */}
      {!isValid() && (
        <p className="text-sm text-neutral-500 mt-4 text-center">
          <span className="text-red-500">*</span> Campos obrigatórios
        </p>
      )}
    </div>
  );
}

export default ProjectConfigPanel;
