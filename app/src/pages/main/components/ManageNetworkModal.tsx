import { Modal, useToast } from "@/components";
import Button from "@/components/ui/Button";
import { X, Plus, Link, Edit } from "lucide-react";
import { useState } from "react";
import type { DeviceWithMapping } from "@/utils/mesh-device-utils";
import { getDeviceIcon, formatMappingDate } from "@/utils/mesh-device-utils";
import { getDeviceModelLabel } from "@/utils/device-model";
import SelectDeviceForLICModal from "./SelectDeviceForLICModal";

interface ManageNetworkModalProps {
  isOpen: boolean;
  onClose: () => void;
  licDevice: DeviceWithMapping | null;
  availableDevices?: DeviceWithMapping[]; // Unmapped mesh devices
  onAddDevice?: (
    meshDeviceId: string,
    licDeviceId: string,
    startDate: string
  ) => void;
  onRemoveDevice?: (deviceId: string) => void;
  onEditLIC?: (licDevice: DeviceWithMapping) => void;
}

function ManageNetworkModal({
  isOpen,
  onClose,
  licDevice,
  availableDevices = [],
  onAddDevice,
  onRemoveDevice,
  onEditLIC,
}: ManageNetworkModalProps) {
  const { showWarning } = useToast();
  const [isSelectDeviceModalOpen, setIsSelectDeviceModalOpen] = useState(false);

  if (!licDevice || !licDevice.isLIC) {
    return null;
  }

  const meshDevices = licDevice.meshDevices || [];
  const deviceIcon = getDeviceIcon(licDevice.device.model);
  const modelLabel = getDeviceModelLabel(licDevice.device.model);

  // Filter out devices already connected to this LIC
  const devicesNotInCurrentNetwork = availableDevices.filter(
    (device) => !meshDevices.some((meshDevice) => meshDevice.id === device.id)
  );

  const handleRemoveDevice = (deviceId: string) => {
    if (onRemoveDevice) {
      onRemoveDevice(deviceId);
    } else {
      showWarning({
        message: "Funcionalidade de remoção ainda não implementada.",
      });
    }
  };

  const handleAddDevice = () => {
    if (devicesNotInCurrentNetwork.length === 0) {
      showWarning({
        message: "Não há dispositivos disponíveis para mapear.",
      });
      return;
    }
    setIsSelectDeviceModalOpen(true);
  };

  const handleSelectDevice = (
    meshDeviceId: string,
    licDeviceId: string,
    startDate: string
  ) => {
    if (onAddDevice) {
      onAddDevice(meshDeviceId, licDeviceId, startDate);
    }
    setIsSelectDeviceModalOpen(false);
  };

  const handleEditLIC = () => {
    if (onEditLIC && licDevice) {
      onEditLIC(licDevice);
    } else {
      showWarning({
        message: "Funcionalidade de edição ainda não implementada.",
      });
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={`Gerenciar Rede - ${licDevice.device.identifier}`}
      size="md"
    >
      <div className="ManageNetworkModal space-y-6">
        {/* LIC Device Info */}
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <span className="text-xl">{deviceIcon}</span>
              <span className="font-medium text-gray-900">
                {licDevice.device.identifier}
              </span>
            </div>
            <Button
              type="button"
              variant="primary"
              onClick={handleEditLIC}
              className="py-2! min-h-6!"
              size="sm"
              title="Editar LIC"
              icon={<Edit size={16} />}
            >
              Editar
            </Button>
          </div>
          <p className="text-sm text-gray-600 mb-1">{modelLabel}</p>
          <div className="flex items-center gap-1 text-sm text-green-600">
            <Link size={14} />
            <span>
              Coordenador para {meshDevices.length} dispositivo
              {meshDevices.length !== 1 ? "s" : ""}
            </span>
          </div>
        </div>

        {/* Connected Devices */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            Dispositivos Conectados ({meshDevices.length})
          </h3>

          {meshDevices.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <div className="text-4xl mb-2">🔗</div>
              <p>Nenhum dispositivo conectado</p>
              <p className="text-sm">
                Adicione dispositivos para formar uma rede mesh
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              {meshDevices.map((meshDevice) => (
                <div
                  key={meshDevice.id}
                  className="bg-white border border-gray-200 rounded-lg p-4"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="text-gray-400 font-mono text-sm">
                          ├─
                        </span>
                        <span className="text-lg">
                          {getDeviceIcon(meshDevice.device.model)}
                        </span>
                        <span className="font-medium text-gray-900">
                          {meshDevice.device.identifier}
                        </span>
                        <span className="text-sm text-gray-500">
                          ({meshDevice.device.model})
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 ml-6">
                        {getDeviceModelLabel(meshDevice.device.model)}
                      </p>
                      {meshDevice.current_mesh_device_mapping && (
                        <p className="text-xs text-gray-500 ml-6">
                          Mapeado desde:{" "}
                          {formatMappingDate(
                            meshDevice.current_mesh_device_mapping.start_date
                          )}
                        </p>
                      )}
                    </div>

                    {/* Remove Button */}
                    <Button
                      variant="destructive"
                      onClick={() => handleRemoveDevice(meshDevice.id)}
                      className="p-0! w-8 h-8 rounded-full!"
                      size="sm"
                      title="Remover da rede"
                      icon={<X size={16} />}
                    />
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Add Device Button */}
        <Button
          variant="primary"
          onClick={handleAddDevice}
          className="w-full "
          icon={<Plus className="h-5 w-5" />}
          size="md"
        >
          Adicionar Dispositivo
        </Button>

        {/* Action Buttons */}
        <div className="flex gap-3 pt-4 border-t border-gray-200">
          <Button variant="secondary" className="flex-1" onClick={onClose}>
            Fechar
          </Button>
        </div>
      </div>

      {/* Select Device Modal */}
      <SelectDeviceForLICModal
        isOpen={isSelectDeviceModalOpen}
        onClose={() => setIsSelectDeviceModalOpen(false)}
        licDevice={licDevice}
        availableDevices={devicesNotInCurrentNetwork}
        onSelectDevice={handleSelectDevice}
      />
    </Modal>
  );
}

export default ManageNetworkModal;
