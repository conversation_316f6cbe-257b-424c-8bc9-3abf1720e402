import { IrrigationPlanStep } from "@/api/model/irrigation-plan-step";
import { createIrrigationPlanAtom } from "@/store";
import { projectByIdAtom } from "@/store/data";
import { useAtomValue, useSetAtom } from "jotai";
import { ArrowLeft, Check } from "lucide-react";
import { useMemo, useState } from "react";
import { useLocation } from "wouter";
import Button from "@/components/ui/Button";
import IrrigationPlanConfigPanel from "./components/IrrigationPlanConfigPanel";
import IrrigationPlanStepsPanel from "./components/IrrigationPlanStepsPanel";
import IrrigationPlanSummaryPanel from "./components/IrrigationPlanSummaryPanel";

export interface IrrigationPlanFormData {
  name: string;
  description: string;
  startTime: string;
  daysOfWeek: ("MON" | "TUE" | "WED" | "THU" | "FRI" | "SAT" | "SUN")[];
  isEnabled: boolean;
  fertigationEnabled: boolean;
  backwashEnabled: boolean;
  startDate: string;
  endDate: string;
}

export interface IrrigationPlanStepFormData {
  id: string;
  sectorId: string;
  sectorName: string;
  order: number;
  durationMinutes: number;
  fertigationStartDelayMinutes?: number;
  fertigationDurationMinutes?: number;
}

const INITIAL_PLAN_DATA: IrrigationPlanFormData = {
  name: "",
  description: "",
  startTime: "06:00",
  daysOfWeek: [],
  isEnabled: true,
  fertigationEnabled: false,
  backwashEnabled: false,
  startDate: "",
  endDate: "",
};

export type IrrigationPlanWizardPageProps = {
  projectId: string;
};

function IrrigationPlanWizardPage({
  projectId,
}: IrrigationPlanWizardPageProps) {
  const [, setLocation] = useLocation();
  const [currentStep, setCurrentStep] = useState(1);
  const [planData, setPlanData] =
    useState<IrrigationPlanFormData>(INITIAL_PLAN_DATA);
  const [planSteps, setPlanSteps] = useState<IrrigationPlanStepFormData[]>([]);
  const createPlan = useSetAtom(createIrrigationPlanAtom);
  const getProjectById = useAtomValue(projectByIdAtom);

  const project = useMemo(() => {
    return getProjectById(projectId);
  }, [getProjectById, projectId]);
  // If project is not found, redirect to projects page
  if (!project) {
    console.error(`Project with ID ${projectId} not found`);
    setLocation("/app/projects");
    return null;
  }

  // Convert project sectors to the format expected by the component
  const sectors = useMemo(() => {
    return project.sectors ?? [];
  }, [project]);

  const steps = [
    {
      number: 1,
      title: "Configuração do Plano",
      description: "Configurações básicas do cronograma",
    },
    {
      number: 2,
      title: "Setores e Duração",
      description: "Defina a sequência e duração dos setores",
    },
    {
      number: 3,
      title: "Resumo",
      description: "Revise e confirme as configurações",
    },
  ];

  const handlePlanDataChange = (data: Partial<IrrigationPlanFormData>) => {
    setPlanData((prev) => ({ ...prev, ...data }));
  };

  const handlePlanStepsChange = (steps: IrrigationPlanStepFormData[]) => {
    setPlanSteps(steps);
  };

  const handleAddStep = () => {
    const newStep: IrrigationPlanStepFormData = {
      id: `step-${Date.now()}`, // Temporary ID
      sectorId: "",
      sectorName: "",
      order: planSteps.length + 1,
      durationMinutes: 15, // 5 minutes default
      fertigationStartDelayMinutes: undefined,
      fertigationDurationMinutes: undefined,
    };
    setPlanSteps([...planSteps, newStep]);
  };

  const handleRemoveStep = (stepId: string) => {
    handleBulkRemoveSteps([stepId]);
  };

  const handleBulkRemoveSteps = (stepIds: string[]) => {
    // Calculate updated steps with new order after removing all specified steps
    const updatedSteps = planSteps
      .filter((step) => !stepIds.includes(step.id))
      .map((step, index) => ({ ...step, order: index + 1 }));
    setPlanSteps(updatedSteps);
  };

  const handleSaveStep = (step: IrrigationPlanStepFormData) => {
    // For wizard mode, just update local state
    handleUpdateStep(step.id, step);
  };

  const handleUpdateStep = (
    stepId: string,
    updates: Partial<IrrigationPlanStepFormData>
  ) => {
    setPlanSteps(
      planSteps.map((step) =>
        step.id === stepId ? { ...step, ...updates } : step
      )
    );
  };

  const handleMoveStep = (stepId: string, direction: "up" | "down") => {
    const currentIndex = planSteps.findIndex((step) => step.id === stepId);
    if (currentIndex === -1) return;

    const newIndex = direction === "up" ? currentIndex - 1 : currentIndex + 1;
    if (newIndex < 0 || newIndex >= planSteps.length) return;

    const newSteps = [...planSteps];
    [newSteps[currentIndex], newSteps[newIndex]] = [
      newSteps[newIndex],
      newSteps[currentIndex],
    ];

    // Update order numbers
    newSteps.forEach((step, index) => {
      step.order = index + 1;
    });

    setPlanSteps(newSteps);
  };

  const handleNext = () => {
    if (currentStep < 3) {
      setCurrentStep((prev) => prev + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep((prev) => prev - 1);
    }
  };

  const handleSave = async () => {
    try {
      // TODO: Implement API call to save irrigation plan
      console.log("Saving irrigation plan:", { planData, planSteps });
      const result = await createPlan({
        description: planData.description,
        name: planData.name,
        days_of_week: planData.daysOfWeek,
        start_time: planData.startTime,
        start_date: planData.startDate || null,
        end_date: planData.endDate || null,
        is_enabled: planData.isEnabled,
        fertigation_enabled: planData.fertigationEnabled,
        backwash_enabled: planData.backwashEnabled,
        project: projectId,
        steps: planSteps.map(
          (step) =>
            ({
              fertigation_start_delay_seconds: step.fertigationStartDelayMinutes
                ? step.fertigationStartDelayMinutes * 60
                : null,
              fertigation_duration_seconds: step.fertigationDurationMinutes
                ? step.fertigationDurationMinutes * 60
                : null,
              duration_seconds: step.durationMinutes * 60,
              order: step.order,
              sector: step.sectorId,
              description: step.sectorName,
            }) as IrrigationPlanStep
        ),
      });

      const newPlanId = result.data?.id;
      if (!newPlanId) {
        throw new Error("Failed to create irrigation plan");
      }

      // Navigate back to project detail page
      setLocation(`/app/irrigation-plans/${newPlanId}`);
    } catch (error) {
      console.error("Error saving irrigation plan:", error);
    }
  };

  const canProceedToNextStep = () => {
    switch (currentStep) {
      case 1:
        return planData.name.trim() !== "" && planData.daysOfWeek.length > 0;
      case 2:
        return planSteps.length > 0;
      case 3:
        return true;
      default:
        return false;
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <IrrigationPlanConfigPanel
            data={planData}
            onChange={handlePlanDataChange}
            project={project}
          />
        );
      case 2:
        return (
          <IrrigationPlanStepsPanel
            projectId={projectId!}
            planData={planData}
            steps={planSteps}
            sectors={sectors}
            onAddStep={handleAddStep}
            onRemoveStep={handleRemoveStep}
            onSaveStep={handleSaveStep}
            onUpdateStep={handleUpdateStep}
            onMoveStep={handleMoveStep}
            projectData={
              project
                ? {
                    pipe_wash_time_seconds: project.pipe_wash_time_seconds,
                  }
                : undefined
            }
          />
        );
      case 3:
        return (
          <IrrigationPlanSummaryPanel
            planData={planData}
            planSteps={planSteps}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="flex flex-col h-full bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-4 py-3">
        <div className="flex items-center gap-3">
          <Button
            onClick={() => setLocation(`/app/projects/${projectId}`)}
            variant="ghost"
            size="sm"
            icon={<ArrowLeft className="h-5 w-5" />}
            iconPosition="left"
            className="p-2 text-gray-400 hover:text-gray-600 transition-colors flex-shrink-0"
          />
          <div className="min-w-0 flex-1">
            <h1 className="text-lg font-semibold text-gray-900">
              Novo Cronograma
            </h1>
            <div className="text-sm text-gray-700 font-bold truncate">
              Projeto {project.name}
            </div>
            <p className="text-sm text-gray-600">
              {steps[currentStep - 1].description}
            </p>
          </div>
        </div>
      </div>

      {/* Step Indicator */}
      <div className="bg-white border-b border-gray-200 px-4 py-4">
        <div className="flex items-center justify-between">
          {steps.map((step, index) => (
            <div key={step.number} className="flex items-center">
              <div className="flex items-center">
                <div
                  className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                    currentStep > step.number
                      ? "bg-green-600 text-white"
                      : currentStep === step.number
                        ? "bg-blue-600 text-white"
                        : "bg-gray-200 text-gray-600"
                  }`}
                >
                  {currentStep > step.number ? (
                    <Check className="h-4 w-4" />
                  ) : (
                    step.number
                  )}
                </div>
                <div className="ml-3 hidden sm:block">
                  <p
                    className={`text-sm font-medium ${
                      currentStep >= step.number
                        ? "text-gray-900"
                        : "text-gray-500"
                    }`}
                  >
                    {step.title}
                  </p>
                </div>
              </div>
              {index < steps.length - 1 && (
                <div
                  className={`w-12 h-0.5 mx-4 ${
                    currentStep > step.number ? "bg-green-600" : "bg-gray-200"
                  }`}
                />
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Step Content */}
      <div className="flex-1 overflow-auto">{renderStepContent()}</div>

      {/* Footer Actions */}
      <div className="bg-white border-t border-gray-200 px-4 py-4">
        <div className="flex items-center justify-between">
          <Button
            onClick={handlePrevious}
            disabled={currentStep === 1}
            variant="secondary"
            size="md"
            className={`px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
              currentStep === 1
                ? "text-gray-400 cursor-not-allowed"
                : "text-gray-700 hover:bg-gray-100"
            }`}
          >
            Anterior
          </Button>

          <div className="flex gap-2">
            {currentStep < 3 ? (
              <Button
                onClick={handleNext}
                disabled={!canProceedToNextStep()}
                variant="primary"
                size="md"
                className={`px-6 py-2 text-sm font-medium rounded-lg transition-colors ${
                  canProceedToNextStep()
                    ? "bg-blue-600 text-white hover:bg-blue-700"
                    : "bg-gray-300 text-gray-500 cursor-not-allowed"
                }`}
              >
                Próximo
              </Button>
            ) : (
              <Button
                onClick={handleSave}
                variant="primary"
                size="md"
                className="px-6 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 transition-colors"
              >
                Salvar Cronograma
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default IrrigationPlanWizardPage;
