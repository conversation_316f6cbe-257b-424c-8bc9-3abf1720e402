import { appShell } from "@/store";
import { projectsAtom } from "@/store/data";
import { formatDays } from "@/utils/format";
import { useAtomValue, useSetAtom } from "jotai";
import { Clock, ListChecks, Plus } from "lucide-react";
import React, { useEffect } from "react";
import { useLocation } from "wouter";
import Button from "@/components/ui/Button";

function IrrigationPlansPage() {
  const setBackButton = useSetAtom(appShell.backButtonAtom);
  useEffect(() => {
    setBackButton(false);
  });

  const [, setLocation] = useLocation();
  const rawProjects = useAtomValue(projectsAtom);
  const [selectedProjectId, setSelectedProjectId] = React.useState<
    string | null
  >(null);

  const projectsHavingPlans = React.useMemo(
    () => rawProjects,
    // rawProjects.filter(
    //   (project) =>
    //     project.irrigation_plans && project.irrigation_plans.length > 0
    // ),
    [rawProjects]
  );

  const filteredProjects = React.useMemo(() => {
    if (!selectedProjectId) return projectsHavingPlans;
    return projectsHavingPlans.filter(
      (project) => project.id === selectedProjectId
    );
  }, [projectsHavingPlans, selectedProjectId]);

  const shouldShowProjectHeader = projectsHavingPlans.length > 1;
  const shouldShowProjectFilter = projectsHavingPlans.length > 1;

  return (
    <>
      <div className=" bg-gray-50">
        {/* Header */}
        <div className=" border-b border-gray-200 px-4 py-4">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            Agendamentos de irrigação
          </h1>
          <p className="text-gray-600 text-sm">
            Gerencie seus agendamentos de irrigação
          </p>

          {/* Project Filter Dropdown */}
          {shouldShowProjectFilter && (
            <div className="relative mt-4">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <ListChecks className="h-5 w-5 text-gray-400" />
              </div>
              <select
                value={selectedProjectId || "all"}
                onChange={(e) =>
                  setSelectedProjectId(
                    e.target.value === "all" ? null : e.target.value
                  )
                }
                className="appearance-none bg-white border border-gray-300 rounded-lg pl-10 pr-10 py-3 text-sm font-medium text-gray-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 min-w-[200px] shadow-sm hover:border-gray-400 transition-colors w-full"
              >
                <option value="all">Todos os projetos</option>
                {projectsHavingPlans.map((project) => (
                  <option key={project.id} value={project.id}>
                    {project.name}
                  </option>
                ))}
              </select>
              <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                <svg
                  className="h-5 w-5 text-gray-400"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
            </div>
          )}
        </div>

        {/* Projects List */}
        <div className="px-4 pt-2 space-y-8">
          {filteredProjects.length === 0 ? (
            <div className="text-center py-12">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Clock className="w-8 h-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Nenhum plano de irrigação
              </h3>
              <p className="text-gray-500 mb-6">
                Crie seu primeiro projeto com planos de irrigação
              </p>
              <Button
                onClick={() => setLocation("/app/projects/new")}
                variant="primary"
                size="md"
                className="px-6 py-3 rounded-lg font-medium transition-colors"
              >
                Criar projeto
              </Button>
            </div>
          ) : (
            filteredProjects.map((project) => (
              <div key={project.id} className="space-y-4">
                {/* Project Header */}
                <div className="flex items-center justify-between">
                  <div>
                    {shouldShowProjectHeader && (
                      <h2 className="text-xl font-semibold text-gray-900">
                        {project.name}
                      </h2>
                    )}
                    <div className="flex items-center gap-2">
                      <div className="flex items-center gap-1">
                        <div
                          className={`w-1.5 h-1.5 rounded-full ${
                            project.irrigation_water_pump
                              ? "bg-blue-500"
                              : "bg-gray-300"
                          }`}
                        ></div>
                        <span
                          className={`text-xs ${
                            project.irrigation_water_pump
                              ? "text-blue-700"
                              : "text-gray-500"
                          }`}
                        >
                          Irrigação
                        </span>
                      </div>
                      {project.fertigation_water_pump && (
                        <div className="flex items-center gap-1">
                          <div
                            className={`w-1.5 h-1.5 rounded-full ${
                              project.fertigation_water_pump
                                ? "bg-green-500"
                                : "bg-gray-300"
                            }`}
                          ></div>
                          <span
                            className={`text-xs ${
                              project.fertigation_water_pump
                                ? "text-green-700"
                                : "text-gray-500"
                            }`}
                          >
                            Fertirrigação
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                </div>

                {/* Irrigation Plans Grid */}
                <div className="grid gap-4">
                  {project.irrigation_plans?.map((plan, index) => {
                    const iconColors = [
                      {
                        bg: "bg-blue-100",
                        icon: "text-blue-600",
                        border: "border-blue-200",
                      },
                      {
                        bg: "bg-purple-100",
                        icon: "text-purple-600",
                        border: "border-purple-200",
                      },
                      {
                        bg: "bg-orange-100",
                        icon: "text-orange-600",
                        border: "border-orange-200",
                      },
                      {
                        bg: "bg-indigo-100",
                        icon: "text-indigo-600",
                        border: "border-indigo-200",
                      },
                      {
                        bg: "bg-red-100",
                        icon: "text-red-600",
                        border: "border-red-200",
                      },
                    ];
                    const colorScheme = iconColors[index % iconColors.length];

                    return (
                      <div
                        key={plan.id}
                        className="bg-white rounded-xl border border-gray-200 p-5 transition-all duration-200 hover:shadow-md hover:border-green-300 cursor-pointer"
                        onClick={() => {
                          setLocation(
                            `/app/irrigation-plans/${plan.id}?projectId=${project.id}`
                          );
                        }}
                      >
                        <div className="flex items-start gap-4">
                          <div
                            className={`w-12 h-12 rounded-xl flex-shrink-0 flex items-center justify-center ${colorScheme.bg}`}
                          >
                            <Clock className={`w-6 h-6 ${colorScheme.icon}`} />
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-start justify-between mb-3">
                              <div>
                                <h3 className="text-lg font-semibold text-gray-900 truncate">
                                  {plan.name}
                                </h3>
                                <p className="text-sm text-gray-500 mt-1">
                                  {formatDays(plan.days_of_week)}
                                </p>
                              </div>
                              <span
                                className={`px-2 py-1 text-xs font-medium rounded-full flex-shrink-0 ${
                                  plan.is_enabled
                                    ? "bg-green-100 text-green-700"
                                    : "bg-gray-100 text-gray-600"
                                }`}
                              >
                                {plan.is_enabled ? "Ativo" : "Inativo"}
                              </span>
                            </div>

                            <div className="grid grid-cols-2 gap-4 text-sm text-gray-600 mb-3">
                              <div className="flex items-center gap-2">
                                <Clock className="h-4 w-4 text-gray-400" />
                                <span>{plan.start_time}</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <ListChecks className="h-4 w-4 text-gray-400" />
                                <span>{plan.steps?.length || 0} etapas</span>
                              </div>
                            </div>

                            {plan.is_enabled && (
                              <div className="text-xs text-gray-500 pt-3 border-t border-gray-100">
                                Próxima execução:{" "}
                                <span className="font-medium text-gray-700">
                                  Amanhã às {plan.start_time}
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
                <Button
                  className="w-full py-4 border-2 border-dashed border-gray-300 rounded-xl text-gray-600 hover:border-gray-400 hover:text-gray-700 transition-colors flex items-center justify-center gap-2"
                  onClick={() => {
                    setLocation(
                      `/app/irrigation-plans/new?projectId=${project.id}`
                    );
                  }}
                  variant="secondary"
                  size="md"
                  icon={<Plus className="h-5 w-5" />}
                  iconPosition="left"
                  fullWidth
                >
                  <div>
                    <div>Novo agendamento</div>
                    <div className="font-bold text-gray-800">
                      {project.name}
                    </div>
                  </div>
                </Button>
              </div>
            ))
          )}
        </div>
      </div>
      {/* Floating Action Button - Only show if there are projects */}
      {/* {rawProjects.length > 0 && (
        <button
          className="sticky bottom-2 ml-auto right-2  w-14 h-14 bg-green-600 hover:bg-green-700 text-white rounded-full shadow-lg hover:shadow-xl flex items-center justify-center transition-all duration-200 z-50"
          onClick={() => {
            setLocation("/app/projects/new");
          }}
          aria-label="Adicionar novo projeto"
        >
          <Plus className="h-6 w-6" />
        </button>
      )} */}
    </>
  );
}

export default IrrigationPlansPage;
