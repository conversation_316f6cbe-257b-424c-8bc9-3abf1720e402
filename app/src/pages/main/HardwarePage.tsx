import { WATER_PUMP_TYPE_LABELS } from "@/api/model/water-pump";
import type { AUTDevice, AUTWaterPump } from "@/api/queries/account";
import { Tabs } from "@/components";
import {
  appShell,
  propertyDevicesAtom,
  waterPumpsAtom,
  createMeshDeviceMapping<PERSON>tom,
  deleteMeshDeviceMappingAtom,
  updateMeshDeviceMappingAtom,
} from "@/store";
import { useAtomValue, useSetAtom } from "jotai";
import { ChevronRight, Cpu, Search, Wrench } from "lucide-react";
import { useEffect, useState, useMemo } from "react";
import { useSearchParams } from "wouter";
import DeviceDetailModal from "./components/DeviceDetailModal";
import PumpDetailModal from "./components/PumpDetailModal";
import DeviceFilterBar, {
  type DeviceFilter,
} from "./components/DeviceFilterBar";
import EnhancedDeviceListItem from "./components/EnhancedDeviceListItem";
import ManageNetworkModal from "./components/ManageNetworkModal";
import AssignToLICModal from "./components/AssignToLICModal";
import {
  enhanceDevicesWithMapping,
  sortDevicesForHierarchicalDisplay,
  type DeviceWithMapping,
} from "@/utils/mesh-device-utils";
import { FAB } from "@/components/ui/FAB";

// Removed unused TabType

// Define tabs configuration with full type safety
const HARDWARE_TABS = [
  { key: "devices", label: "Dispositivos", icon: Cpu },
  { key: "pumps", label: "Bombas", icon: Wrench },
] as const;

function HardwarePage({ activeTab }: { activeTab: string }) {
  if (activeTab && !HARDWARE_TABS.some((tab) => tab.key === activeTab)) {
    activeTab = "devices"; // Default to devices if invalid
  }
  const setBackButton = useSetAtom(appShell.backButtonAtom);
  useEffect(() => {
    setBackButton(false);
  });

  const [, setSearchParams] = useSearchParams();
  const propertyDevices = useAtomValue(propertyDevicesAtom);
  const currentPumps = useAtomValue(waterPumpsAtom);
  const createMeshDeviceMapping = useSetAtom(createMeshDeviceMappingAtom);
  const deleteMeshDeviceMapping = useSetAtom(deleteMeshDeviceMappingAtom);
  const updateMeshDeviceMapping = useSetAtom(updateMeshDeviceMappingAtom);

  const [searchQuery, setSearchQuery] = useState("");
  const [deviceFilter, setDeviceFilter] = useState<DeviceFilter>("all");
  const [isDeviceModalOpen, setIsDeviceModalOpen] = useState(false);
  const [isPumpModalOpen, setIsPumpModalOpen] = useState(false);
  const [isManageNetworkModalOpen, setIsManageNetworkModalOpen] =
    useState(false);
  const [isAssignToLICModalOpen, setIsAssignToLICModalOpen] = useState(false);
  const [selectedDevice, setSelectedDevice] = useState<AUTDevice | null>(null);
  const [selectedPump, setSelectedPump] = useState<AUTWaterPump | null>(null);
  const [selectedEnhancedDevice, setSelectedEnhancedDevice] =
    useState<DeviceWithMapping | null>(null);
  const [modalMode, setModalMode] = useState<"create" | "edit">("create");

  // Enhanced devices with mapping information
  const enhancedDevices = useMemo(() => {
    const enhanced = enhanceDevicesWithMapping(propertyDevices);
    return sortDevicesForHierarchicalDisplay(enhanced);
  }, [propertyDevices]);

  // Sync selectedEnhancedDevice when enhancedDevices change
  useEffect(() => {
    if (selectedEnhancedDevice) {
      const updated = enhancedDevices.find(
        (d) => d.id === selectedEnhancedDevice.id
      );
      if (updated) {
        setSelectedEnhancedDevice(updated);
      }
    }
  }, [enhancedDevices, selectedEnhancedDevice]);

  // Filter devices based on search query and device filter
  const filteredDevices = useMemo(() => {
    let filtered = enhancedDevices;

    // Apply device type/status filter
    switch (deviceFilter) {
      case "coordinators":
        filtered = filtered.filter((d) => d.mappingStatus === "coordinator");
        break;
      case "mapped":
        filtered = filtered.filter((d) => d.mappingStatus === "mapped");
        break;
      case "unmapped":
        filtered = filtered.filter((d) => d.mappingStatus === "unmapped");
        break;
      case "lic":
        filtered = filtered.filter((d) => d.device.model === "LIC");
        break;
      case "wpc":
        filtered = filtered.filter((d) => d.device.model.startsWith("WPC"));
        break;
      case "vc":
        filtered = filtered.filter((d) => d.device.model === "VC");
        break;
      case "rm":
        filtered = filtered.filter((d) => d.device.model === "RM");
        break;
      default:
        // "all" - no additional filtering
        break;
    }

    // Apply search query filter
    if (searchQuery.trim()) {
      filtered = filtered.filter(
        (device) =>
          device.device.identifier
            .toLowerCase()
            .includes(searchQuery.toLowerCase()) ||
          device.device.model.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    return filtered;
  }, [enhancedDevices, deviceFilter, searchQuery]);

  // Calculate device counts for filter bar
  const deviceCounts = useMemo(() => {
    return {
      all: enhancedDevices.length,
      coordinators: enhancedDevices.filter(
        (d) => d.mappingStatus === "coordinator"
      ).length,
      mapped: enhancedDevices.filter((d) => d.mappingStatus === "mapped")
        .length,
      unmapped: enhancedDevices.filter((d) => d.mappingStatus === "unmapped")
        .length,
      lic: enhancedDevices.filter((d) => d.device.model === "LIC").length,
      wpc: enhancedDevices.filter((d) => d.device.model.startsWith("WPC"))
        .length,
      vc: enhancedDevices.filter((d) => d.device.model === "VC").length,
      rm: enhancedDevices.filter((d) => d.device.model === "RM").length,
    };
  }, [enhancedDevices]);

  // Available LICs for assignment
  const availableLICs = useMemo(() => {
    return enhancedDevices.filter((d) => d.isLIC);
  }, [enhancedDevices]);

  // All mesh devices for adding to LIC networks (including mapped ones for reassignment)
  const allMeshDevices = useMemo(() => {
    return enhancedDevices.filter((d) => d.isMesh);
  }, [enhancedDevices]);

  // Filter pumps based on search query
  const filteredPumps = currentPumps.filter(
    (pump) =>
      pump.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
      pump.identifier.toLowerCase().includes(searchQuery.toLowerCase()) ||
      pump.pump_model.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleDeviceClick = (device: DeviceWithMapping) => {
    setSelectedDevice(device.device);
    setSelectedEnhancedDevice(device);
    setModalMode("edit");
    setIsDeviceModalOpen(true);
  };

  const handleManageNetwork = (device: DeviceWithMapping) => {
    setSelectedEnhancedDevice(device);
    setIsManageNetworkModalOpen(true);
  };

  const handleAssignToLIC = (device: DeviceWithMapping) => {
    setSelectedEnhancedDevice(device);
    setIsAssignToLICModalOpen(true);
  };

  const handleEditLIC = (licDevice: DeviceWithMapping) => {
    setSelectedDevice(licDevice.device);
    setSelectedEnhancedDevice(licDevice);
    setModalMode("edit");
    setIsManageNetworkModalOpen(false); // Close the manage network modal
    setIsDeviceModalOpen(true); // Open the device detail modal
  };

  const handleAddDeviceToLIC = async (
    meshDeviceId: string,
    licDeviceId: string,
    startDate: string
  ) => {
    try {
      // Find the device to check if it has an existing mapping
      const deviceToAdd = enhancedDevices.find((d) => d.id === meshDeviceId);

      // If device is already mapped, end the current mapping first
      if (deviceToAdd?.current_mesh_device_mapping) {
        await updateMeshDeviceMapping({
          id: deviceToAdd.current_mesh_device_mapping.id,
          data: {
            end_date: startDate, // End current mapping on the start date of new mapping
          },
        });
      }

      // Create the new mesh device mapping
      await createMeshDeviceMapping({
        mesh_property_device: meshDeviceId,
        lic_property_device: licDeviceId,
        start_date: startDate,
      });
    } catch (error) {
      console.error("Error adding device to LIC:", error);
    }
  };

  const handleRemoveDeviceFromLIC = async (deviceId: string) => {
    try {
      // Find the mesh device mapping to delete
      const deviceToRemove = enhancedDevices.find((d) => d.id === deviceId);
      if (deviceToRemove?.current_mesh_device_mapping) {
        await deleteMeshDeviceMapping(
          deviceToRemove.current_mesh_device_mapping.id
        );
      }
    } catch (error) {
      console.error("Error removing device from LIC:", error);
    }
  };

  const handleOpenAssignToLICFromDetail = (device: DeviceWithMapping) => {
    setSelectedEnhancedDevice(device);
    setIsDeviceModalOpen(false); // Close device detail modal
    setIsAssignToLICModalOpen(true); // Open assign to LIC modal
  };

  const handlePumpClick = (pump: AUTWaterPump) => {
    setSelectedPump(pump);
    setModalMode("edit");
    setIsPumpModalOpen(true);
  };

  const handleAddDevice = () => {
    setSelectedDevice(null);
    setModalMode("create");
    setIsDeviceModalOpen(true);
  };

  const handleAddPump = () => {
    setSelectedPump(null);
    setModalMode("create");
    setIsPumpModalOpen(true);
  };

  const closeDeviceModal = () => {
    setIsDeviceModalOpen(false);
    setSelectedDevice(null);
    setSelectedEnhancedDevice(null);
  };

  const closePumpModal = () => {
    setIsPumpModalOpen(false);
    setSelectedPump(null);
  };

  const closeManageNetworkModal = () => {
    setIsManageNetworkModalOpen(false);
    setSelectedEnhancedDevice(null);
  };

  const closeAssignToLICModal = () => {
    setIsAssignToLICModalOpen(false);
    setSelectedEnhancedDevice(null);
  };

  const getPumpLastActivity = (_pump: AUTWaterPump) => {
    // This would come from the API with real activity data
    return "Agora";
  };

  const setActiveTab = (tab: string) => {
    setSearchParams((prev) => {
      const newParams = new URLSearchParams(prev);
      newParams.set("activeTab", tab);
      return newParams;
    });
  };

  return (
    <div className="px-6 pb-4">
      <h1 className="text-2xl font-bold text-gray-900 mb-6">Hardware</h1>

      {/* Tab Navigation */}
      <Tabs
        tabs={HARDWARE_TABS}
        activeTab={activeTab}
        onTabChange={setActiveTab}
        className="mb-6"
      />

      {/* Device Filter Bar (only for devices tab) */}
      {activeTab === "devices" && (
        <DeviceFilterBar
          selectedFilter={deviceFilter}
          onFilterChange={setDeviceFilter}
          deviceCounts={deviceCounts}
        />
      )}

      {/* Search Bar */}
      <div className="relative mb-6">
        <Search
          className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
          size={20}
        />
        <input
          type="text"
          placeholder="Buscar..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent outline-none"
        />
      </div>

      {/* Content */}
      {activeTab === "devices" && (
        <div className="space-y-3">
          {filteredDevices.length === 0 ? (
            <div className="text-center py-12 text-gray-500">
              <div className="text-4xl mb-4">🔧</div>
              <p className="text-lg font-medium mb-2">
                Nenhum dispositivo encontrado
              </p>
              <p className="text-sm">
                {searchQuery.trim() || deviceFilter !== "all"
                  ? "Tente ajustar os filtros ou termo de busca"
                  : "Adicione dispositivos para começar"}
              </p>
            </div>
          ) : (
            filteredDevices.map((device, index) => (
              <EnhancedDeviceListItem
                key={device.id}
                device={device}
                index={index}
                devices={filteredDevices}
                onDeviceClick={handleDeviceClick}
                onManageNetwork={handleManageNetwork}
                onAssignToLIC={handleAssignToLIC}
              />
            ))
          )}
        </div>
      )}

      {activeTab === "pumps" && (
        <div className="space-y-3">
          {filteredPumps.map((pump) => (
            <div
              key={pump.id}
              onClick={() => handlePumpClick(pump)}
              className="bg-white border border-gray-200 rounded-lg p-4 cursor-pointer hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <Wrench className="text-gray-400" size={16} />
                    <span className="font-medium text-gray-900">
                      {pump.label}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 mb-1">
                    S/N: {pump.identifier}
                  </p>
                  <p className="text-xs text-gray-500 mb-1">
                    Tipo:{" "}
                    {WATER_PUMP_TYPE_LABELS[pump.pump_type] || pump.pump_type}
                  </p>
                  <p className="text-xs text-gray-500 mb-1">
                    Controlador: {pump.water_pump_controller || "Não definido"}
                  </p>
                  <p className="text-xs text-gray-500 mb-1">
                    Inversor de frequência:{" "}
                    {pump.has_frequency_inverter ? "Sim" : "Não"}
                  </p>
                  <p className="text-xs text-gray-500 mb-1">
                    Monitoramento:{" "}
                    {pump.monitor_operation ? "Ativo" : "Inativo"}
                  </p>
                  <p className="text-xs text-gray-400">
                    Última atividade: {getPumpLastActivity(pump)}
                  </p>
                </div>
                <ChevronRight className="text-gray-400" size={20} />
              </div>
            </div>
          ))}

          {filteredPumps.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              {searchQuery
                ? "Nenhuma bomba encontrada"
                : "Nenhuma bomba cadastrada"}
            </div>
          )}
        </div>
      )}

      {/* Floating Add Button */}
      <FAB
        onClick={activeTab === "devices" ? handleAddDevice : handleAddPump}
        className="bottom-20 right-4"
      />

      {/* Device Detail Modal */}
      <DeviceDetailModal
        isOpen={isDeviceModalOpen}
        onClose={closeDeviceModal}
        device={selectedDevice}
        enhancedDevice={selectedEnhancedDevice}
        mode={modalMode}
        onOpenAssignToLIC={handleOpenAssignToLICFromDetail}
      />

      {/* Pump Detail Modal */}
      <PumpDetailModal
        isOpen={isPumpModalOpen}
        onClose={closePumpModal}
        pump={selectedPump}
        mode={modalMode}
      />

      {/* Manage Network Modal */}
      <ManageNetworkModal
        isOpen={isManageNetworkModalOpen}
        onClose={closeManageNetworkModal}
        licDevice={selectedEnhancedDevice}
        availableDevices={allMeshDevices}
        onAddDevice={handleAddDeviceToLIC}
        onRemoveDevice={handleRemoveDeviceFromLIC}
        onEditLIC={handleEditLIC}
      />

      {/* Assign to LIC Modal */}
      <AssignToLICModal
        isOpen={isAssignToLICModalOpen}
        onClose={closeAssignToLICModal}
        meshDevice={selectedEnhancedDevice}
        availableLICs={availableLICs}
      />
    </div>
  );
}

export default HardwarePage;
