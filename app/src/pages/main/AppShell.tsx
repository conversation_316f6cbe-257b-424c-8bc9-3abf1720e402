import {
  appShell,
  selectedAccountUserId<PERSON>tom,
  selectedPropertyIdAtom,
} from "@/store";
import { useAtom, useAtomValue, useSetAtom } from "jotai";
import { useEffect, useRef, useState } from "react";
import { Route, Switch, useLocation, useSearchParams } from "wouter";

// Import page components
import Settings from "./components/Settings";
import DashboardPage from "./DashboardPage";
import HardwarePage from "./HardwarePage";
import IrrigationPlanPage from "./IrrigationPlanPage";
import ProjectConfigPage from "./ProjectConfigPage";
import ProjectDetailPage from "./ProjectDetailPage";
import ProjectsPage from "./ProjectsPage";
import ReportsPage from "./ReportsPage";

// Import layout components
import { PropertyEditModal } from "@/components";
import Modal from "@/components/Modal";
import BottomTabNavigation from "./components/BottomTabNavigation";
import Header, { MenuItemType } from "./components/Header";
import { ReservoirForm } from "./components/ReservoirForm";
import IrrigationPlansPage from "./IrrigationPlansPage";
import { ReservoirsPage } from "./ReservoirsPage";
import WaterPumpPage from "./WaterPumpsPage";

// Main AppShell component
function AppShell() {
  const [location, setLocation] = useLocation();
  const [searchParams, _] = useSearchParams();
  const [showSettingsModal, setShowSettingsModal] = useState(false);
  const [showPropertyEditModal, setShowPropertyEditModal] = useState(false);
  const getSelectedAccountUserId = useSetAtom(selectedAccountUserIdAtom);
  const selectedPropertyId = useAtomValue(selectedPropertyIdAtom);
  const [backButton, setBackButton] = useAtom(appShell.backButtonAtom);
  const mainRef = useRef<HTMLElement>(null);

  // Define the root tab paths that should show bottom navigation
  const tabPaths = [
    "/app/dashboard",
    "/app/projects",
    "/app/devices",
    "/app/reports",
    "/app/irrigation-plans",
    "/app/pumps",
    "/app/reservoirs",
  ];
  const shouldShowBottomNav = tabPaths.includes(location);

  useEffect(() => {
    // Scroll main content to top on location change
    if (mainRef.current) {
      mainRef.current.scrollTo({ top: 0, behavior: "smooth" });
    }
    console.log("AppShell - location changed: ", location);
  }, [location]);

  useEffect(() => {
    // Reset back button state when location changes
    console.log("AppShell - back button state: ", backButton);
  }, [backButton]);

  useEffect(() => {
    const selectedAccountUserId = getSelectedAccountUserId();
    setBackButton(true); // Reset back button state on mount
    // Route guards - check authentication and selections
    if (!selectedAccountUserId) {
      console.warn(
        "AppShell - No account selected, redirecting to account selection"
      );
      setLocation("/select-account");
      return;
    }

    if (!selectedPropertyId) {
      console.warn(
        "AppShell - No property selected, redirecting to property selection"
      );
      setLocation("/select-property");
      return;
    }
  }, [setLocation, location, getSelectedAccountUserId, selectedPropertyId]);

  function handleMenuItemClick(item: MenuItemType) {
    if (item === "profile") {
      setShowSettingsModal(true);
      // Redirect to previous page or dashboard
    } else if (item === "projects") {
      setLocation("/app/projects");
    } else if (item === "equipment") {
      setLocation("/app/devices");
    } else if (item === "reports") {
      setLocation("/app/reports");
    } else if (item === "reservoirs") {
      setLocation("/app/reservoirs");
    } else if (item === "property") {
      setShowPropertyEditModal(true);
    } else {
      console.warn(`Unhandled menu item: ${item}`);
    }
  }

  return (
    <div className="flex flex-col h-screen bg-gray-50">
      <Header
        showSettingsModal={showSettingsModal}
        onMenuClicked={handleMenuItemClick}
        backButton={backButton}
      />
      <main ref={mainRef} className="flex-1 overflow-auto">
        <Switch>
          <Route path="/app/dashboard" component={DashboardPage} />
          <Route
            path="/app/devices"
            component={(props) => (
              <HardwarePage
                {...props}
                activeTab={searchParams.get("activeTab") || "devices"}
              />
            )}
          />
          <Route path="/app/projects/new">
            {(props) => (
              <ProjectConfigPage
                {...props}
                projectId="new"
                activeTab={searchParams.get("activeTab") || "config"}
              />
            )}
          </Route>
          <Route
            path="/app/projects/:projectId/config"
            component={(props) => (
              <ProjectConfigPage
                {...props.params}
                activeTab={searchParams.get("activeTab") || "config"}
              />
            )}
          />
          <Route
            path="/app/projects/:projectId/irrigation-plans/new"
            component={(props) => {
              const projectId = props.params.projectId;
              setLocation(`/app/irrigation-plans/new?projectId=${projectId}`);
              return null;
            }}
          />
          <Route
            path="/app/projects/:projectId/irrigation-plans/:planId"
            component={(props) => (
              <IrrigationPlanPage
                {...props}
                planId={props.params.planId}
                activeTab={searchParams.get("activeTab") || "config"}
              />
            )}
          />
          <Route path="/app/projects/:projectId">
            {(props) => <ProjectDetailPage {...props} />}
          </Route>
          <Route path="/app/projects" component={ProjectsPage} />
          <Route path="/app/irrigation-plans" component={IrrigationPlansPage} />
          <Route path="/app/irrigation-plans/new">
            {(props) => {
              const projectId = searchParams.get("projectId");
              if (!projectId) {
                // If no projectId provided, redirect to irrigation plans list
                console.warn(
                  "No projectId provided in irrigation plan creation route"
                );
                setLocation("/app/irrigation-plans");
                return null;
              }
              return (
                <IrrigationPlanPage
                  {...props}
                  planId="new"
                  activeTab={searchParams.get("activeTab") || "config"}
                />
              );
            }}
          </Route>
          <Route path="/app/irrigation-plans/:planId">
            {(props) => {
              return (
                <IrrigationPlanPage
                  {...props}
                  activeTab={searchParams.get("activeTab") || "config"}
                />
              );
            }}
          </Route>
          <Route path="/app/pumps" component={WaterPumpPage} />
          <Route path="/app/reservoirs" component={ReservoirsPage} />
          <Route path="/app/reservoirs/new" component={ReservoirForm} />
          <Route path="/app/reservoirs/:id" component={ReservoirForm} />
          <Route path="/app/reports" component={ReportsPage} />
          {/* Default route within app - redirect to dashboard */}
          <Route
            path="/app"
            component={() => {
              setLocation("/app/dashboard");
              return null;
            }}
          />
        </Switch>
      </main>
      {shouldShowBottomNav && <BottomTabNavigation />}

      {/* Settings Modal */}
      <Modal
        isOpen={showSettingsModal}
        onClose={() => setShowSettingsModal(false)}
        title="Configurações"
        size="xl"
      >
        <Settings />
      </Modal>

      {/* Property Edit Modal */}
      <PropertyEditModal
        isOpen={showPropertyEditModal}
        onClose={() => setShowPropertyEditModal(false)}
      />
    </div>
  );
}

export default AppShell;
