import { appShell } from "@/store";
import { useSet<PERSON>tom } from "jotai";
import { useEffect } from "react";

function ReportsPage() {
  const setBackButton = useSetAtom(appShell.backButtonAtom);
  useEffect(() => {
    setBackButton(false);
  });
  return (
    <div className="p-6 pb-20">
      <h1 className="text-2xl font-bold text-gray-900 mb-4">Relatórios</h1>
      <p className="text-gray-600">Analise dados e gere relatórios.</p>
    </div>
  );
}

export default ReportsPage;
