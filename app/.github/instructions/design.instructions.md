---
applyTo: "**/*.tsx,**/*.ts,**/*.css"
description: "Complete tech stack documentation for the Irriga Mais project"
---

# Copilot Instructions for UI/UX Design

## Overview

This project follows a comprehensive design system defined in `docs/design.json`. All UI/UX components, styling, and layouts must strictly adhere to this design system to ensure consistency and maintainability.

## Design System Reference

Always reference the design system located at `docs/design.json` when creating or modifying any UI components.

## CSS Design System (`src/index.css`)

The project uses a comprehensive CSS design system built on Tailwind CSS with custom design tokens defined in `src/index.css`. This file serves as the single source of truth for all design values and should be referenced for consistent styling across the application.

## Implementation Rules

1. **Always reference design.json**: Before creating any component, check the design system specifications
2. **Use design tokens**: Reference colors, spacing, and typography using the defined token names
3. **Maintain consistency**: All similar components should look and behave identically
4. **Mobile-first approach**: Design for mobile first, then enhance for larger screens
5. **Accessibility**: Ensure proper contrast ratios and interactive element sizes (minimum 48px)
6. **Performance**: Use efficient CSS and avoid unnecessary re-renders
7. **Semantic HTML**: Use appropriate HTML elements for better accessibility and SEO

## Code Style Guidelines

### CSS/SCSS

```css
/* Use design tokens */
.button-primary {
  background-color: var(--primary-500);
  color: white;
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-md) var(--spacing-lg);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  min-height: 48px;
}
```

### React/TypeScript

```tsx
// Define component props with design system values
interface ButtonProps {
  variant: "primary" | "secondary" | "ghost";
  size: "sm" | "md" | "lg";
  children: React.ReactNode;
}

// Use consistent naming and structure
const Button: React.FC<ButtonProps> = ({ variant, size, children }) => {
  // Implementation following design system
};
```

## Quality Checklist

Before considering any UI component complete, verify:

- [ ] Colors match the design system palette
- [ ] Typography uses specified font sizes, weights, and line heights
- [ ] Spacing follows the defined spacing scale
- [ ] Border radius matches design system values
- [ ] Interactive states (hover, active, disabled) are implemented
- [ ] Component is responsive across all breakpoints
- [ ] Accessibility requirements are met
- [ ] Code follows the established patterns and conventions
- [ ] Component matches the visual specifications exactly

## Notes for Copilot

- Always prioritize design system consistency over personal preferences
- When in doubt, reference the design.json file for exact specifications
- Suggest improvements to the design system when patterns are missing
- Maintain the agricultural/irrigation theme with the green color palette
- Ensure all components feel modern, clean, and professional
- Focus on user experience and intuitive interactions
