---
applyTo: "**/*.tsx,**/*.ts"
description: "Complete tech stack documentation for the Irriga Mais project"
---

# Irriga Mais - Tech Stack Documentation

## Project Overview

**Project Name:** Irriga Mais (Bun React Template)  
**Version:** 0.1.0  
**Project Type:** Agricultural Irrigation Management Web Application  
**Architecture:** Full-stack web application with integrated frontend and backend

## Core Technology Stack

### Runtime & Build System

- **Bun v1.2.16+**: Fast all-in-one JavaScript runtime, package manager, bundler, and test runner
  - Used for dependency management (`bun install`)
  - Development server (`bun dev`)
  - Production server (`bun start`)
  - Custom build system (`bun run build.ts`)

### Frontend Framework

- **React 19**: Latest version with modern features
  - Functional components with hooks
  - TypeScript integration
  - Hot Module Replacement (HMR) enabled in development

### TypeScript Configuration

- **TypeScript**: Strict mode enabled with modern ESNext target
- **Module System**: ESM with bundler resolution
- **JSX**: React JSX transform
- **Path Aliases**: `@/*` mapped to `./src/*`
- **Target**: ESNext for modern JavaScript features

### Styling & UI

- **TailwindCSS v4.0.6**: Utility-first CSS framework
  - Custom design system integration (see design.json)
  - Dark theme as default
  - Responsive design utilities
  - Animations and transitions

### Routing

- **Wouter v3.7.1**: Lightweight client-side routing
  - Switch-based routing
  - Declarative route definitions
  - No external dependencies

### Icons

- **Lucide React v0.525.0**: Modern icon library
  - Tree-shakeable
  - TypeScript support
  - Consistent design language

### State Management

- **Jotai**: A minimalist, atom-based state management library for React.
  - **Atomic Approach**: State is built from small, independent units called atoms.
  - **Performance**: Optimized for minimal re-renders out-of-the-box.
  - **Async Handling**: Seamlessly integrates asynchronous operations within atoms.

## Architecture Patterns

### Frontend Architecture

```
src/
├── App.tsx              # Root application component
├── frontend.tsx         # Client-side entry point
├── index.tsx           # Server entry point with API routes
├── Routes.tsx          # Route configuration
├── store/              # Global state management (Jotai)
│   ├── index.ts        # Main export file for all atoms and hooks
│   ├── auth.ts         # Authentication-related state
│   ├── data.ts         # Core application data state
│   ├── crud.ts         # Atoms for CRUD operations
│   └── Provider.tsx    # Main provider and app initializer
├── components/         # Reusable UI components
├── pages/              # Page components
└── ...
```

## Build System

### Custom Build Configuration (`build.ts`)

- **Entry Points**: Scans for all HTML files in src/
- **Output**: Optimized production builds in `dist/`
- **Features**:
  - TailwindCSS processing via `bun-plugin-tailwind`
  - Source maps generation
  - Minification
  - Code splitting
  - File size reporting
  - CLI argument parsing for build customization

### Available Build Options

```bash
bun run build.ts [options]
--outdir <path>          # Output directory (default: "dist")
--minify                 # Enable minification
--source-map <type>      # Sourcemap type: none|linked|inline|external
--target <target>        # Build target: browser|bun|node
--format <format>        # Output format: esm|cjs|iife
--splitting              # Enable code splitting
--external <list>        # External packages (comma separated)
```

## Design System Integration

The project includes a comprehensive design system defined in `docs/design.json`:

## Development Environment

### Scripts

```json
{
  "dev": "bun --hot src/index.tsx",      # Development server with HMR
  "start": "NODE_ENV=production bun src/index.tsx",  # Production server
  "build": "bun run build.ts"            # Production build
}
```

### Environment Configuration

- **Development**: HMR enabled, console logging, non-minified
- **Production**: Minified, optimized, source maps
- **Environment Variables**: Prefix with `BUN_PUBLIC_*` for client-side access

### File Extensions & Module Declarations

- **SVG Modules**: Typed as path strings
- **CSS Modules**: Typed class name objects
- **TypeScript**: `.ts`, `.tsx` files
- **Assets**: SVG, CSS with proper type declarations

## API Architecture

- All API calls are made using directus client (@directus/sdk).
- `src/api/client.ts` contains the Directus client configuration with:
  - Base URL configuration for the Directus API endpoint
  - Authentication token management
  - Request/response interceptors for error handling
  - Type-safe SDK initialization with TypeScript collections
  - Environment-based configuration (development/production)
  - Custom headers and timeout settings
- `src/api/service.ts` provides a centralized API service layer with:
  - Singleton pattern implementation for consistent API access
  - Generic CRUD operations factory for all Directus collections
  - Type-safe collection interfaces with schema validation
  - Domain-specific service methods (auth, account, asset management)
  - Authentication flow management (login, logout, user fetching)
  - Account and user tree queries with relational data
  - Asset URL resolution for file handling
  - Error handling and type conversion utilities

## Dependencies Analysis

### Dependency Strategy

- **Minimal Dependencies**: Only essential packages included
- **Modern Versions**: Latest stable versions prioritized
- **Type Safety**: Full TypeScript support for all dependencies
- **Performance**: Lightweight alternatives chosen (wouter vs react-router)

## Security Considerations

### Environment Variables

- Client-side variables prefixed with `BUN_PUBLIC_*`
- Sensitive data kept server-side only
- Production environment isolation

### Build Security

- Source maps in development only
- Minification for production
- No sensitive data in client bundles

## Performance Optimizations

### Build Optimizations

- Code splitting enabled
- Tree shaking automatic
- Minification in production
- Source map optimization

### Runtime Optimizations

- React 19 performance features
- TailwindCSS purging
- Hot reloading in development only
- Lazy loading capabilities via React

## Future Considerations

### Scalability Preparations

- Modular architecture ready for feature expansion
- API route structure prepared for growth
- Component library foundation established
- Design system tokens ready for theming

### Technology Upgrade Path

- React 19 features adoption ready
- TailwindCSS v4 future-proofed
- Bun ecosystem maturation support
- TypeScript strict mode foundation

## Agricultural Domain Context

### Project Purpose

- **Irrigation Management**: Agricultural water management system
- **Mobile-First**: Responsive design for field use
- **Real-time Data**: Architecture prepared for sensor integration
- **User Management**: Authentication system foundation

### Industry-Specific Considerations

- **Offline Capability**: PWA-ready architecture
- **Data Visualization**: Chart library integration ready
- **Geolocation**: GPS integration preparation
- **Weather Integration**: API consumption patterns established

## Getting Started

### Prerequisites

- Bun v1.2.16 or later
- Node.js compatible environment (for fallback)
- Modern browser with ES6+ support

### Installation & Development

```bash
# Install dependencies
bun install

# Start development server
bun run dev

# Build for production
bun run build.ts

# Start production server
bun run start
```

### Development Workflow

1. **Feature Development**: Create components in `src/pages/` or `src/components/`
2. **Styling**: Use TailwindCSS utilities following design system
3. **Routing**: Add routes in `src/Routes.tsx`
4. **API**: Extend endpoints in `src/index.tsx`
5. **Building**: Use custom build script with options
6. **Testing**: Bun test runner ready for implementation

This documentation serves as a comprehensive guide for understanding and working with the Irriga Mais project's technology stack and architecture.
