---
applyTo: "**/*.tsx,**/*.ts"
description: "Coding standards, domain knowledge, and preferences that AI should follow."
---

# React (with TypeScript), TailwindCSS, Bun, and TypeScript Coding Standards

## General

- Use ES6+ syntax and features.
- Prefer functional components.
- Use named exports.
- Keep files and functions small and focused.
- Use consistent file and folder naming (e.g., kebab-case or camelCase).

## TypeScript

- Always type function parameters and return values.
- Use interfaces for object shapes and props.
- Prefer type aliases for unions and primitives.
- Avoid using `any`; use `unknown` or proper types.
- Enable strict mode in `tsconfig.json`.
- Use enums sparingly; prefer union types for limited sets of values.

## React (with TypeScript)

- Use hooks for state and side effects (`useState`, `useEffect`, etc.).
- Use `useCallback` and `useMemo` to optimize performance.
- Keep components pure and avoid side effects in render.
- Use TypeScript interfaces for component props.
- Use context for global state, avoid prop drilling.
- Use keys when rendering lists.
- Avoid inline functions in JSX when possible.

## Jotai (State Management)

- **Atom Granularity**: Create small, focused atoms for individual pieces of state.
- **Derived Atoms**: Use derived atoms (read-only atoms) to compute state from other atoms. This is the primary way to create memoized, reactive state.
- **Async Actions**: Encapsulate async logic (like API calls) within write-only atoms.
- **Atom Organization**: Keep atoms organized by domain in the `src/store` directory (e.g., `auth.ts`, `data.ts`).
- **Hooks**: Use the correct hook for the job:
  - `useAtom`: When a component needs to both read and write to an atom.
  - `useAtomValue`: For read-only access to an atom's value.
  - `useSetAtom`: For write-only access, to avoid re-renders when the value changes.
- **Naming**: Name atoms with an `Atom` suffix (e.g., `userAtom`, `isAuthenticatedAtom`).

## TailwindCSS

- Use utility classes directly in JSX.
- Prefer composable utility classes over custom CSS.
- Use `@apply` in CSS only for repeated patterns.
- Keep className strings short and readable.
- Use responsive and state variants (`sm:`, `hover:`, etc.) as needed.
- Avoid using arbitrary values unless necessary.
- Gotchas:
  - bg-opacity-[number] is not supported; use bg-[color]/[opacity] instead. Example: `bg-black bg-opacity-50` becomes `bg-black/50`.

## Bun

- Use Bun’s native APIs for scripts and tooling.
- Prefer Bun’s package manager (`bun install`) for dependencies.
- Use Bun’s test runner for unit and integration tests.
- Keep scripts in `package.json` or as Bun scripts.

## Code Style

- Use 2 spaces for indentation.
- Use single quotes for strings.
- End files with a newline.
- Use semicolons consistently.
- Prefer destructuring for objects and arrays.
- Sort imports: external, then internal, then styles/assets.
- Remove unused imports and variables.

## Testing

- Write unit tests for components and utilities.
- Use React Testing Library for component tests.
- Mock external dependencies.
- Aim for high code coverage, but prioritize meaningful tests.

## Documentation

- Use TSDoc for documenting complex functions and components.
- Add comments for non-obvious logic.
- Keep README and code comments up to date.

## CSS

- Minimize use of custom CSS; prefer TailwindCSS utilities.
- Use CSS modules or scoped styles for component-specific styles.
- Organize CSS files by feature or component.
- Use semantic and meaningful class names.
- Avoid global selectors and overrides.
- Document non-obvious CSS rules with comments.
- Keep CSS files small and focused.

## Language

- Use English for all code comments and documentation.
- Follow consistent naming conventions (e.g., camelCase for variables and functions).
- Avoid using non-standard abbreviations or jargon.
- Keep language clear and concise for better understanding.
- While all code and documentation should be in English, any visible text presented to the end user (UI labels, messages, etc.) must be written in Brazilian Portuguese.
