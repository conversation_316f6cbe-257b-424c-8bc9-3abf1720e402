syntax = "proto3";

package codec.in.device_scheduling;

message DeviceScheduling {
  int32 idx = 1;                  // Índice do agendamento do dispositivo
  int32 scheduling_idx = 2;       // Índice do agendamento principal
  int32 device_idx = 3;           // Índice do dispositivo
  int32 order = 4;                // Ordem de execução no agendamento
  int32 sector_working_time = 5;  // Tempo de início relativo (minutos após início do agendamento)
  int32 ferti_working_time = 6;   // Tempo de funcionamento para fertilização (em minutos)
  int32 ferti_delay = 7;          // Atraso antes da fertilização (em minutos)
}

message DeviceSchedulingPackage {
  repeated DeviceScheduling data = 1; // Lista de agendamentos de dispositivos
}