syntax = "proto3";

package codec.out.status;

message SystemStatusPackage {
  uint32 resets = 1;              // Número de resets
  uint32 scheduling_running = 2;  // Agendamento em execução
  uint32 scheduling_paused = 3;   // Pausa de agendamento ativa
  
  oneof has_paused_time {
    uint32 paused_time = 4;       // Minutos desde que a pausa foi ativada
  }

  oneof has_raining {
    uint32 raining = 5;           // Está chovendo (1 = sim)
  }

  oneof has_rainfall {
    uint32 rainfall = 6;          // Chuva acumulada nas últimas 24h
  }

  uint64 sync_bitmask = 7;        // Máscara de bits de dispositivos sincronizados
  uint64 on_bitmask = 8;          // Máscara de bits de dispositivos ligados
  uint64 input_bitmask = 9;       // Máscara de bits de dispositivos com entrada 1 
  uint32 failed_bitmask = 12;     // Máscara de bits de falhas do sistema
}