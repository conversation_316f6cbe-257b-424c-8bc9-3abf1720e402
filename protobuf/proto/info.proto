syntax = "proto3";

package codec.out.info;

message InfoPackage {
  string codec_id = 1;            // Número de série do Codec
  uint32 firmware_esp = 2;        // Versão do firmware ESP
  uint32 firmware_mesh = 3;       // Versão do firmware do mesh
  uint32 hardware_version = 4;    // Versão do hardware
  uint32 resets = 5;              // Número de resets
  uint32 scheduling_running = 6;  // Agendamento em execução
  uint32 scheduling_paused = 7;   // Agendamento pausado
  uint32 devices_id = 8;          // Última atualização dos dispositivos
  uint32 scheduling_id = 9;       // Última atualização dos agendamentos
  uint32 dev_scheduling_id = 10;  // Última atualização dos agendamentos por dispositivo
  uint32 automation_id = 11;      // Última atualização das automações
  uint32 config_id = 12;          // Última atualização da configuração
  uint32 failed_bitmask = 13;     // Máscara de bits de falhas do sistema
}